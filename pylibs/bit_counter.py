import sys

try:
    import bitarray
except ImportError:
    sys.stderr.write(f"Can not use QualifiedCounter due to fail to import bitarray.")


class BitCounter:
    """BitCounter is a counter used to count bool value of a recent list.

    Think there is an infinity increasing list (or queue), we need to count
    recent 100 values to see if the rate of successes beyond 80% like a slide
    window.

    It leverages bit shifting to for highest performance. If we use list or
    queue, it may need many CPU time to push/pop/scan. It will be a performance
    hell if the counter will be frequently used in loops.

    The limitation is that it only store 1 bit data for each item. But it's
    simple to extend to support more data.

    e.g.
    A qualified counter to count recent 10 product quality rate, qualified is
    1 (True), not qualified is 0 (False). qualified rate as below:

          v------------------v              this is recent 30-10, rate is 85%
    111111111011111111011110111111111110
                              ^--------^    this is recent 10, rate is 90%

    """

    __recent_limit: int
    __bits: bitarray

    __total: int
    __total_qualified: int

    def __init__(self, recent_limit: int = 20) -> None:
        self.__recent_limit = recent_limit
        self.__bits = bitarray(recent_limit)
        self.__total = 0
        self.__total_qualified = 0

    def __str__(self) -> str:
        return f"{self.__bits[-self.__recent_limit:].to01()} (Recent qualified/Recent total: {self.recent_qualified_count}/{self.recent_count}, Total qualified/Total: {self.total_qualified_count}/{self.total_count})"

    def append(self, qualified: bool) -> None:
        self.__bits = self.__bits << 1
        self.__total += 1
        if qualified:
            self.__bits[-1] = 1
            self.__total_qualified += 1

    @property
    def recent_count(self) -> int:
        return self.__recent_limit

    @property
    def recent_qualified_count(self) -> int:
        return self.__bits.count(1)

    @property
    def recent_qualify_rate(self) -> float:
        if self.total_count >= self.__recent_limit:
            return self.__bits.count(1) / self.__recent_limit
        else:
            # Few data may simply cause low rate.
            # so use 1.0 if not reach count threshold.
            return 1.0

    @property
    def total_count(self) -> int:
        return self.__total

    @property
    def total_qualified_count(self) -> int:
        return self.__total_qualified

    @property
    def total_qualify_rate(self) -> float:
        if self.__total > 0:
            return self.__total_qualified / self.__total
        else:
            return 1.0
