from pycommon.logwrapper import config_logging
from pycommon.utils.confutils import get_bool_env, get_str_env
from pycommon.utils.terminal import debug

DEBUG = get_bool_env("DEBUG", False)
ENV = get_str_env("ENV", "dev")
LANG = "zh"  # app display language, now only support ["zh", "en"]
__INITIALIZED__ = False


def init() -> None:

    global __INITIALIZED__

    if __INITIALIZED__:
        return

    config_logging()

    __INITIALIZED__ = True
    debug("Configuration initialized.")


init()
