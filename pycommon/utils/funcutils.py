import asyncio
import functools
import time
from contextlib import contextmanager
from typing import Callable


def time_tracker(func, log_funcs: list[Callable] = []):
    @contextmanager
    def wrapping_logic():
        start = time.perf_counter()
        yield
        dur = time.perf_counter() - start
        if log_funcs:
            for log_func in log_funcs:
                if asyncio.iscoroutinefunction(log_func):

                    async def alog_it():
                        await log_func("{} took {:.2} seconds".format(func.__name__, dur))

                    alog_it()
                else:
                    log_func("{} took {:.2} seconds".format(func.__name__, dur))
        else:
            print("{} took {:.2} seconds".format(func.__name__, dur))

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        if not asyncio.iscoroutinefunction(func):
            with wrapping_logic():
                return func(*args, **kwargs)
        else:

            async def tmp():
                with wrapping_logic():
                    return await func(*args, **kwargs)

            return tmp()

    return wrapper
