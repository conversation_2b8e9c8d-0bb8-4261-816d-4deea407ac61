import json
from typing import Any, AsyncGenerator

from pyagent.base import LlmAgent, AgentResult


class HelloAgent(LlmAgent):

    async def _async_run(self, **kwargs: Any) -> AgentResult:
        prompt = "Hello, who are you?"
        answer = await self.async_invoke_llm(prompt=prompt, resp_type="text")
        print(answer)
        prompt = """Can you tell me who are in JSON format? like {"name":<name>, "version":<version>, "cutoff_date":<cutoff date of your data>}"""
        answer = await self.async_invoke_llm(prompt=prompt, resp_type="object")
        print(json.dumps(answer, indent=2, default=str, ensure_ascii=False))
        return answer

    async def _async_iter(self, **kwargs: Any) -> AsyncGenerator:
        raise NotImplementedError


class ChatPromptAgent(LlmAgent):
    async def _async_run(self, **kwargs: Any) -> AgentResult:
        import pyllm
        from pydantic import BaseModel

        class ProductPlan(BaseModel):
            name: str
            target_user: str
            resolving_problem: str
            main_use_cases: list[str]
            features: list[str]
            development_steps: list[str]
            promotion_plan: list[str]
            marketing_plan: list[str]

        user_prompt = "今天你打算做一个AI相关的应用，现在写下初步的计划。"
        prompts = (
            pyllm.ChatPrompts(output_format="json", output_schema=ProductPlan.model_json_schema(), output_lang="中文")
            .set_system(
                "你是一个非常优秀的产品经理，擅长策划to C的应用，无论是web、移动、车载、大屏、游戏都有成功经验。"
            )
            .set_inquery(user_prompt)
        )
        print(json.dumps(prompts.to_list(), indent=2, default=str, ensure_ascii=False))
        answer = await self.async_invoke_llm(prompt=prompts.to_list(), resp_type="object")
        print(json.dumps(answer, indent=2, default=str, ensure_ascii=False))
        return answer

    async def _async_iter(self, **kwargs: Any) -> AsyncGenerator:
        raise NotImplementedError
