#!/usr/bin/env python3
# coding=utf-8
"""
API client wrapper for convenience invoking.

"""

from typing import Union, Optional, Iterable
from requests import Request, Session
from requests.exceptions import ConnectionError
from logwrapper import getLogger

from consts import DEBUG, API_END_POINT, API_HEADER_AUTH, API_SECURITY_TOKEN
from definitions import (
    HTTPMethod,
)
from schemas import (
    CredentialObject,
    ProfileObject,
)
from utils import auth_encode, debug, warn

API_PATH_LOGIN = "/login"
API_PATH_PING_LOGS = "/ping/logs"

log = getLogger("common.apicli")


class APIClient(object):

    def __init__(self, api_token: str = API_SECURITY_TOKEN, enable_terminal_output: bool = False):
        self.session = Session()
        self.session.headers.update(
            {
                API_HEADER_AUTH: auth_encode(api_token),
                # 'User-Agent': '%(app)s %(service)s/%(version)s'
            }
        )
        self.enable_terminal_output = enable_terminal_output

    def _request_api(
        self,
        method: str,
        url: str,
        query: dict = {},
        header: dict = {},
        payload: Optional[Union[dict, list]] = None,
        json: Optional[Union[dict, list]] = None,
        retry=1,
    ) -> Optional[str]:
        ret, resp = None, None
        if self.enable_terminal_output:
            debug("%s %s" % (method, url))

        timeout = (0.5, 2)
        while resp is None and retry > 0:
            try:
                req = Request(
                    method=method,
                    url=url,
                    headers=header,
                    params=query,
                    json=json,
                    data=payload,
                )
                prepared_req = self.session.prepare_request(req)
                resp = self.session.send(prepared_req, timeout=timeout)
                break
            except ConnectionError as e:
                log.debug(str(e))
                timeout = (timeout[0] * 2, timeout[1] * 2)
                retry -= 1
                log.warning("retry %s %s" % (method, url))
            except Exception as e:
                warn(e)
                if DEBUG:
                    log.exception(e)
                else:
                    log.error(str(e))
                break

        if resp is not None:
            # strange, need to set encoding (should be pydantic/fastapi reason.).
            # otherwise requests will detect encoding.
            resp.encoding = "utf-8"

            if 200 <= resp.status_code < 400:

                if self.enable_terminal_output:
                    debug("%s %s" % (resp.status_code, resp.reason))
                ret = resp.text
            else:
                if self.enable_terminal_output:
                    warn("%s %s" % (resp.status_code, resp.text))
                log.warning("%s %s" % (resp.status_code, resp.text))
        else:
            log.warning("No response %s %s" % (method, url))

        return ret

    def login(self, token: Optional[str] = None) -> Optional[str]:
        """login with token
        returns JSON str of ProfileObject.
        """
        url = "/".join([API_END_POINT, API_PATH_LOGIN])

        if token:
            headers = {API_HEADER_AUTH: auth_encode(token)}
        else:
            headers = {}
        ret = self._request_api(HTTPMethod.POST, url, header=headers)
        return ret

    def report_node_state(self, node_pk: str, data: dict) -> Optional[str]:
        """report node states.
        return JSON str of dict.
        """
        url = "/".join([API_END_POINT, API_PATH_NODE_STATE % node_pk])

        ret = self._request_api(HTTPMethod.POST, url, json=data)
        return ret

    def report_ping_logs(self, node_pk: str, ping_logs: Iterable[str]) -> Optional[str]:
        """report ping logs."""
        url = "/".join([API_END_POINT, API_PATH_PING_LOGS, node_pk])

        ret = self._request_api(HTTPMethod.POST, url, json=ping_logs)
        return ret
