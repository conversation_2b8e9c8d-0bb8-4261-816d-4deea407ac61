import asyncio
import json
import logging
import os
from typing import Async<PERSON><PERSON>ator

import colorlog
from fastapi import FastAPI, <PERSON>er, HTTPException, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from scraper.downloader import PlaywrightDownloader<PERSON>onfig, PlaywrightDownloader
from scraper.spiders.base import Spider
from scraper.spiders.instagram import (
    InstagramSearchSpider,
    InstagramSearchSpiderConfig,
)
from scraper.spiders.tiktok import (
    TiktokSearchSpider,
    TiktokSearchSpiderConfig,
)
from scraper.spiders.youtube import (
    YoutubeSearchSpider,
    YoutubeSearchSpiderConfig,
)

colorlog.basicConfig(
    level=logging.DEBUG,
    format="%(log_color)s[%(levelname).01s] [%(asctime)s] [%(name)s:%(lineno)d] %(message)s",
)
app = FastAPI()

logger = logging.getLogger(__name__)

# Get the API key from an environment variable
API_KEY = os.getenv("API_KEY", "default_api_key")
PROXY = json.loads(os.getenv("PROXY", '{"server":""}')) if os.getenv("PROXY") else None
TIMEOUT = int(os.getenv("TIMEOUT", 30))
HEADLESS = bool(os.getenv("HEADLESS", True))


class DownloadPayload(BaseModel):
    urls: list[str]


class TiktokSearchPayload(BaseModel):
    query: str
    pages: int = 10
    items: int = 100


class YoutubeSearchPayload(BaseModel):
    query: str
    pages: int = 10
    items: int = 100
    with_profile: bool = True


class InstagramSearchPayload(BaseModel):
    query: str
    pages: int = 10
    items: int = 100
    with_profile: bool = True


async def verify_api_key(x_api_key: str = Header(...)) -> None:
    if x_api_key != API_KEY:
        raise HTTPException(status_code=403, detail="Access Deny")


@app.get("/download/")
async def download(url: str, api_key: str = Depends(verify_api_key)) -> str:
    """download a url

    :param url:
    :param api_key:
    :return: html or its content
    """
    # e.g.
    # curl 'http://localhost:8000/download/?url=https://www.baidu.com' \                                                                                                                                    ─╯
    #  -H "Content-Type: application/json" \
    #  -H "X-API-Key: default_api_key"

    cfg = PlaywrightDownloaderConfig(is_headless=HEADLESS)
    downloader = PlaywrightDownloader(config=cfg)
    r = await downloader.async_download(url)
    return str(r.ex) if r.ex else r.data


@app.post("/download/")
async def download_all(payload: DownloadPayload, api_key: str = Depends(verify_api_key)) -> list[str]:
    """download multiple url parallelized

    :param payload:
    :param api_key:
    :return:
    """
    # e.g.
    # curl -X POST "http://localhost:8000/download/" \
    #     -H "Content-Type: application/json" \
    #     -H "X-API-Key: default_api_key" \
    #     -d '{ "urls": ["https://www.onwish.ai/insights/anticipating-advertising-spending-impacts-from-the-upcoming-us-presidential-election", "https://www.onwish.ai/insights/federal-reserve-rate-cuts-impact-on-the-semiconductor-sector", "https://www.onwish.ai/insights/amazons-ai-strategy-competing-with-microsoft-and-google"]}'

    cfg = PlaywrightDownloaderConfig(is_headless=HEADLESS)
    downloader = PlaywrightDownloader(config=cfg)
    results = await downloader.async_download_all(payload.urls)
    return [str(r.ex) if r.ex else r.data for r in results]


@app.post("/tiktok/search/")
async def tiktok_search(payload: TiktokSearchPayload, api_key: str = Depends(verify_api_key)) -> dict:
    cfg = TiktokSearchSpiderConfig(
        term=payload.query,
        name="tiktok_search",
        browser_type="chromium",
        start_url="https://www.tiktok.com",
        is_headless=HEADLESS,
        proxy=PROXY,
        timeout=TIMEOUT,
        # ignored_resources=["font", "image"],
        pages=payload.pages,
        items=payload.items,
    )
    spider = TiktokSearchSpider(config=cfg)

    await spider.async_start()
    r = spider.result
    r = {
        "error": str(r.ex) if r.ex else "",
        "data": r.entries,
        "visited_urls": r.visited_pages,
    }

    return r


@app.post("/tiktok/search/stream/")
async def tiktok_search_stream(
    payload: TiktokSearchPayload, api_key: str = Depends(verify_api_key)
) -> StreamingResponse:
    cfg = TiktokSearchSpiderConfig(
        term=payload.query,
        name="tiktok_search",
        browser_type="chromium",
        start_url="https://www.tiktok.com",
        is_headless=HEADLESS,
        proxy=PROXY,
        timeout=TIMEOUT,
        # ignored_resources=["font", "image"],
        pages=payload.pages,
        items=payload.items,
    )
    spider: Spider = TiktokSearchSpider(config=cfg)

    async def stream_generator() -> AsyncGenerator:
        nonlocal spider, cfg
        try:
            async for vid in spider.async_iterate():
                if isinstance(vid, Exception):
                    yield f"event: error\ndata: {vid}\n\n".encode("utf-8")
                    break
                else:
                    yield f"data: {json.dumps(vid)}\n\n".encode("utf-8")
                await asyncio.sleep(0.01)
        except Exception as e:
            logger.error(f"Error in stream generator: {e}")
            yield f"data: {json.dumps({'error': str(e)})}\n\n".encode("utf-8")
        finally:
            logger.info("streaming finished.")
            yield "data: [DONE]\n\n".encode("utf-8")
            # await spider.async_stop()
            # del spider
            # del cfg

    return StreamingResponse(stream_generator(), media_type="text/event-stream")


@app.post("/youtube/search/stream/")
async def youtube_search_stream(
    payload: YoutubeSearchPayload, api_key: str = Depends(verify_api_key)
) -> StreamingResponse:
    cfg = YoutubeSearchSpiderConfig(
        term=payload.query,
        name="youtube_search",
        browser_type="chromium",
        start_url="https://www.youtube.com",
        is_headless=HEADLESS,
        proxy=PROXY,
        timeout=TIMEOUT,
        # ignored_resources=["font", "image"],
        pages=payload.pages,
        items=payload.items,
        with_profile=payload.with_profile,
    )
    spider: Spider = YoutubeSearchSpider(config=cfg)

    async def stream_generator() -> AsyncGenerator:
        nonlocal spider, cfg
        try:
            async for vid in spider.async_iterate():
                if isinstance(vid, Exception):
                    yield f"event: error\ndata: {vid}\n\n".encode("utf-8")
                    break
                else:
                    yield f"data: {json.dumps(vid)}\n\n".encode("utf-8")
                await asyncio.sleep(0.01)
        except Exception as e:
            logger.error(f"Error in stream generator: {e}")
            yield f"data: {json.dumps({'error': str(e)})}\n\n".encode("utf-8")
        finally:
            logger.info("streaming finished.")
            yield "data: [DONE]\n\n".encode("utf-8")

    return StreamingResponse(stream_generator(), media_type="text/event-stream")


@app.post("/instagram/search/stream/")
async def instagram_search_stream(
    payload: InstagramSearchPayload, api_key: str = Depends(verify_api_key)
) -> StreamingResponse:
    cfg = InstagramSearchSpiderConfig(
        term=payload.query,
        name="youtube_search",
        browser_type="chromium",
        start_url="https://www.youtube.com",
        is_headless=HEADLESS,
        proxy=PROXY,
        timeout=TIMEOUT,
        # ignored_resources=["font", "image", "media"],
        pages=payload.pages,
        items=payload.items,
        with_profile=payload.with_profile,
    )
    spider: Spider = InstagramSearchSpider(config=cfg)

    async def stream_generator() -> AsyncGenerator:
        nonlocal spider, cfg
        try:
            async for vid in spider.async_iterate():
                if isinstance(vid, Exception):
                    yield f"event: error\ndata: {vid}\n\n".encode("utf-8")
                    break
                else:
                    yield f"data: {json.dumps(vid)}\n\n".encode("utf-8")
                await asyncio.sleep(0.01)
        except Exception as e:
            logger.error(f"Error in stream generator: {e}")
            yield f"data: {json.dumps({'error': str(e)})}\n\n".encode("utf-8")
        finally:
            logger.info("streaming finished.")
            yield "data: [DONE]\n\n".encode("utf-8")

    return StreamingResponse(stream_generator(), media_type="text/event-stream")


if __name__ == "__main__":
    import uvicorn
    import sys

    port = 9527  # Default port
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"Invalid port number: {sys.argv[1]}. Using default port {port}.")

    uvicorn.run(app, host="0.0.0.0", port=port)
