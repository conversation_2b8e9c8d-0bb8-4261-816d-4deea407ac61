import json
import logging
import unittest

import colorlog
import yaml
from dotenv import load_dotenv

from pyllm.params import get_pre_defined_settings

colorlog.basicConfig(
    level=logging.INFO,
    format="%(log_color)s[%(levelname).05s] [%(asctime)s] [%(name)s:%(lineno)d] %(message)s",
)

from pyllm import (
    ProviderEnum,
    get_client_type,
    OllamaClient,
    ModelNameEnum,
    get_param_type,
    OpenAIParam,
    OllamaParam,
    create_client,
    ChatParam,
)
from pyllm.client import OpenAIClient, LlmGeneralClient
from pyllm.observation import LlmCostTracker
from pyllm.prompts import ChatPrompts

load_dotenv()


class TestParams(unittest.TestCase):

    def test_get_client_type(self):
        func = get_client_type

        self.assertRaises(ValueError, func, "a", "b", "c")

        cls = func(ProviderEnum.OPENAI, "gpt-4o-mini")
        self.assertIs(cls, OpenAIClient)

        cls = func("ollama", ModelNameEnum.OLLAMA_LLAMA_32_3B)
        self.assertIs(cls, OllamaClient)

        cls = func(provider="openai", model_name="gpt-4o-mini")
        self.assertIs(cls, OpenAIClient)

        self.assertRaises(NotImplementedError, func, "ollama", ModelNameEnum.GEMINI_20_FLASH_LITE)

    def test_get_client_type_fallback(self):
        func = get_client_type

        cls = func(ProviderEnum.OPENAI, "gpt-4.5", fallback_to_default=True)
        self.assertIs(cls, LlmGeneralClient)

        self.assertRaises(ValueError, func, ProviderEnum.OLLAMA, "gpt-4.5")

    def test_get_param_type(self):
        func = get_param_type

        self.assertRaises(ValueError, func, "a", "b", "c")

        cls = func(ProviderEnum.OPENAI, "gpt-4o-mini")
        self.assertIs(cls, OpenAIParam)

        cls = func("ollama", ModelNameEnum.OLLAMA_LLAMA_32_3B)
        self.assertIs(cls, OllamaParam)

        self.assertRaises(NotImplementedError, func, "openai", ModelNameEnum.OLLAMA_LLAMA_32_3B)


class TestOllama(unittest.TestCase):

    def setUp(self):
        self.llm_cli = create_client(ProviderEnum.OLLAMA, ModelNameEnum.OLLAMA_LLAMA_32_3B)
        self.llm_cli.observer.trackers = [LlmCostTracker()]

    def test_simple_chat(self) -> None:
        params = OllamaParam()
        messages = [
            {
                "role": "system",
                "content": (
                    'Whatever your name is, please answer me "Samuel" ' "without any other words if I ask your name."
                ),
            },
            {"role": "user", "content": "what's your name?"},
        ]
        prompts = ChatPrompts.from_list(messages)
        answer = self.llm_cli._completion(prompts, params)
        assert answer == "Samuel"

    def test_json_output(self) -> None:
        params = OllamaParam()
        messages = [
            {
                "role": "system",
                "content": 'Whatever your name is, please remember your name is "Samuel".',
            },
            {"role": "user", "content": "what's your name?"},
        ]
        prompts = ChatPrompts.from_list(messages)
        prompts.output_format = "json"
        prompts.output_template = '{"name": <your_name>}'
        answer = self.llm_cli._completion(prompts, params)
        print(answer)
        d = json.loads(answer)
        assert d["name"] == "Samuel"

    def test_yaml_output(self) -> None:
        params = OllamaParam()
        messages = [
            {
                "role": "system",
                "content": 'Whatever your name is, please remember your name is "Samuel". ',
            },
            {"role": "user", "content": "what's your name?"},
        ]
        prompts = ChatPrompts.from_list(messages)
        prompts.output_format = "yaml"
        prompts.output_template = "name: <your_name>"
        answer = self.llm_cli._completion(prompts, params)
        print(answer)
        d = yaml.safe_load(answer)
        assert d["name"] == "Samuel"


# @pytest.fixture(
#     scope="class",
#     params=[
#         (ProviderEnum.GROK, ModelNameEnum.GROK_V2, ChatParam),
#         (ProviderEnum.OLLAMA, ModelNameEnum.LLAMA32, OllamaParam),
#     ],
# )
# def llm_settings(request) -> tuple[ProviderEnum, ModelNameEnum, type[ChatParam]]:
#     return request.param


class TestGeneral(unittest.TestCase):

    provider = "default"
    model_name = "default"
    param_cls: type[ChatParam] = ChatParam

    def setUp(self):

        # must set pyllm/.env form API keys & proxy

        # os.environ["http_proxy"] = "http://127.0.0.1:1087"
        # os.environ["https_proxy"] = "http://127.0.0.1:1087"

        setting = get_pre_defined_settings().get(ModelNameEnum.DEEPSEEK_V3)
        self.llm_cli = create_client(setting, fallback_to_default=True)
        self.llm_cli.ob.trackers = [LlmCostTracker()]

    def test_simple_chat(self) -> None:
        params = self.param_cls()
        messages = [
            {
                "role": "system",
                "content": (
                    'Whatever your name is, please answer me "Samuel" ' "without any other words if I ask your name."
                ),
            },
            {"role": "user", "content": "what's your name?"},
        ]
        prompts = ChatPrompts.from_list(messages)
        answer = self.llm_cli._completion(prompts, params)
        assert answer == "Samuel"

    def test_json_output(self) -> None:
        params = self.param_cls()
        messages = [
            {
                "role": "system",
                "content": 'Whatever your name is, please remember your name is "Samuel".',
            },
            {"role": "user", "content": "what's your name?"},
        ]
        prompts = ChatPrompts.from_list(messages)
        prompts.output_format = "json"
        prompts.output_template = '{"name": <your_name>}'
        answer = self.llm_cli._completion(prompts, params)
        print(answer)
        d = json.loads(answer)
        assert d["name"] == "Samuel"

    def test_yaml_output(self) -> None:
        params = self.param_cls()
        messages = [
            {
                "role": "system",
                "content": 'Whatever your name is, please remember your name is "Samuel". ',
            },
            {"role": "user", "content": "what's your name?"},
        ]
        prompts = ChatPrompts.from_list(messages)
        prompts.output_format = "yaml"
        prompts.output_template = "name: <your_name>"
        answer = self.llm_cli._completion(prompts, params)
        print(answer)
        d = yaml.safe_load(answer)
        assert d["name"] == "Samuel"
