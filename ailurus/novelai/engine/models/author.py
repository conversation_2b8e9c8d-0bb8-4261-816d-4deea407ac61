"""作者及其写作风格的定义"""

from abc import ABC
from typing import Iterable

from pydantic import BaseModel

from novelai.engine.base import PromptAble
from novelai.engine.consts import WEB_NOVEL_GENRE, WEB_NOVEL_THEME
from pycommon.utils.lang import DictMixin


class Author(BaseModel, DictMixin, PromptAble, ABC):
    """represents an author."""

    name: str  # 姓名
    language: str  # 母语
    skilled_type: str  # 擅长的小说类型，例如 网络文学、散文、童话故事 等等


class WebNovelAuthor(Author):
    """represents a web novel author."""

    # 作者特性相关配置
    cultural_bg: str = "中华"  # 擅长的文化背景
    skilled_genres: list[WEB_NOVEL_GENRE] = ""  # 擅长的小说体裁
    skilled_themes: list[WEB_NOVEL_THEME] = ""  # 擅长的主题
    skilled_styles: list[str] = ""  # 擅长的写作风格
    target_audience: str = ""  # 擅长的受众，如 少年，中青年，老年 等
    major_works_and_types: dict[str, str] = {}  # 主要作品及其类型

    @property
    def as_prompt(self, includes: Iterable[str] = (), excludes: Iterable[str] = ()) -> str:
        return f"""你是顶级{self.skilled_type}作者，擅长{self.cultural_bg}文化背景下的{', '.join(self.skilled_genres)}等体裁的小说，喜欢{'，'.join(self.skilled_styles)}的写作风格。
主要受众是{self.target_audience}，擅长{"，".join(self.skilled_themes)}的主题， 主要作品有{', '.join([f'《{k}》({v})' for k,v in self.major_works_and_types.items()])}等。
你的母语是{self.language}，所以使用{self.language}来写作，也会参考一些英文或其他语言的小说来获取灵感。"""


AUTHOR_DICT = {
    "猫腻": WebNovelAuthor(
        name="猫腻",
        language="中文",
        skilled_type="网络文学",
        cultural_bg="中华",
        skilled_genres=["玄幻", "仙侠", "历史"],
        skilled_themes=["自我超越", "友情与羁绊"],
        skilled_styles=["构思宏大", "设计缜密"],
        target_audience="中青年人",
        major_works_and_types={
            "择天记": "玄幻",
            "将夜": "玄幻",
            "庆余年": "架空历史",
            "间客": "科幻",
            "大道朝天": "仙侠",
        },
    )
}

if __name__ == "__main__":
    print(AUTHOR_DICT["猫腻"].as_prompt)
