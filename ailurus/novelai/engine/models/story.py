from asgiref.sync import sync_to_async
from django.db import models
from django.utils.html import escape

from novelai import conf
from novelai.engine.consts import WebNovelLength
from novelai.engine.models.author import AUTHOR_DICT
from novelai.engine.models.base import NovelModelBase
from pycommon.utils.strutil import to_pinyin, slugify

d_column_helps = {
    "name": {
        "zh": "小说名字，多样性的、流行的、具有多种元素且吸引人的名字（除了'-'外，不要使用其他标点符号）",
        "en": "The name of the story. A diverse, popular, and attractive name with multiple elements. (no punctuation other than '-')",
    },
    "author": {
        "zh": "小说作者名字",
        "en": "Name of the author of the story",
    },
    "key": {
        "zh": "小说唯一标识",
        "en": "The unique key of the story",
    },
    "words_plan": {
        "zh": "小说计划要写的字数(例: 100万)",
        "en": "The planed word count of the story(e.g. 1,000,000)",
    },
    "volumes_plan": {
        "zh": "小说计划要写的卷数(根据小说字数words_plan和情节概要plot来决定。字数较多，情节复杂，则需要多卷。一般一卷几乎是一个独立的故事)",
        "en": "The planed volume count of the story (determined by words_plan and plot. More words and complex plot require more volumes. Usually one volume is almost an independent story)",
    },
    "chapters_plan": {
        "zh": "小说计划要写的大约章节数",
        "en": "The planed chapter count of the story",
    },
    "words_per_chapter_plan": {
        "zh": "每章计划要写的大约字数，一般在1000-5000字之间，但不限于此。",
        "en": "The planed word count per chapter of the story. Usually between 1000 and 5000, but not limited to.",
    },
    "lang": {
        "zh": "小说语言(默认 ‘简体中文(zh-Hans)' ISO 639)",
        "en": "The language of the story (default 'Simplified Chinese(zh-Hans)' ISO 639)",
    },
    "genres": {
        "zh": "小说体裁，多个体裁用加号连接，如科幻+奇幻",
        "en": "The type of the story, multiple types joined by plus sign, like 'science-fiction+fantasy'. do not use blank space.",
    },
    "target_audience": {
        "zh": "小说目标读者(例:成人仙侠爱好者，偏爱长篇、复杂剧情与深刻人物塑造)",
        "en": "The target audience of the story(e.g. adult fantasy lovers who prefer long, complex plots and deep character development)",
    },
    "perspective": {
        "zh": "小说叙述视角(例:第三人称有限视角，主角为主，辅以关键配角视角)",
        "en": "The perspective of the story(e.g. third person limited, mainly from the protagonist’s perspective, supplemented by key supporting characters)",
    },
    "tone_and_style": {
        "zh": "小说基调和风格(例:恢弘大气，兼具热血激昂与细腻情感，融合仙侠的浪漫与残酷)",
        "en": "The tone and style of the story(e.g. epic and majestic, blending passionate heroism with nuanced emotions, capturing the romantic yet brutal essence of xianxia)",
    },
    "themes": {
        "zh": '小说主题。{"major":[<主要主题1>, <主要主题2>, ...], "minor":[<次要主题1>, <次要主题2>, ...]} 格式的 dict',
        "en": 'The themes of the story, dict like {"major":[<major theme 1>, <major theme 2>, ...], "minor":[<minor theme 1>, <minor theme 2>, ...]}',
    },
    "background": {
        "zh": "小说背景，如历史、人文、世界、重要事件/人物/势力等，500字以内",
        "en": "The background of the story, such as history, culture, world setting, important events/characters/forces, no more than 500 words",
    },
    "core_story": {
        "zh": "小说核心故事，2-3句话概括。(例: 凡人少年意外获得上古仙器，踏上修仙之路，历经磨难与阴谋，最终打破仙魔界壁，成就无上大道)",
        "en": "The core story of the story, summarized in 2-3 sentences.(e.g. A mortal youth stumbles upon an ancient immortal artifact, embarking on a path of cultivation, facing trials and conspiracies, ultimately breaking the barrier between immortal and demon realms to achieve the supreme Dao.)",
    },
    "plot": {
        "zh": "情节概要，5-8句话概述开端、中段、结局，或者根据字数有更多阶段。(例: 少年林辰因家族覆灭流落荒野，偶得仙器残魂指引，踏入修仙界。初期在小门派苦修，结识挚友与红颜知己，却卷入门派纷争。中期历经宗门大战、秘境探险，揭开仙器背后的上古秘密，发现魔族复苏的阴谋。后期直面仙魔两界强者，突破自我，化解浩劫，最终以牺牲挚爱为代价，重塑天地秩序，成就一代传奇。)",
        "en": "A summary of the plot, covering the beginning, middle, and end. Or more stages if the story is long.(e.g. A young man named Lin Chen witnesses the murder of his family and escapes into the wilderness. He discovers a powerful artifact and embarks on a journey of self-discovery. In the early stages, he faces challenges and builds relationships. In the middle, he confronts powerful enemies and discovers hidden secrets. In the later stages, he faces a final showdown and achieves his goals.)",
    },
    "conflict": {
        "zh": '小说主要冲突，用2-3句话概括。(例: {"external":"主角对抗强大敌对势力（如魔族、敌对门派）", "internal":"主角面对心魔、情感纠葛与修道信念的挣扎"})',
        "en": 'The main conflict of the story, summarized in 2-3.(e.g. {"external":"The protagonist confronts powerful enemies (such as demon tribes, rival sects, etc.)", "internal":"The protagonist struggles with inner demons, emotional conflicts, and doubts about the path of cultivation."})',
    },
}


class Story(NovelModelBase):
    """represents a story of the novel"""

    key = models.CharField(max_length=250, unique=True, help_text=escape(d_column_helps["key"][conf.LANG]))
    name = models.CharField(max_length=250, help_text=escape(d_column_helps["name"][conf.LANG]))
    author = models.CharField(
        max_length=250,
        choices=((k, k) for k in AUTHOR_DICT.keys()),
        help_text=escape(d_column_helps["author"][conf.LANG]),
    )

    lang = models.CharField(
        max_length=50, default="简体中文(zh-Hans)", help_text=escape(d_column_helps["lang"][conf.LANG])
    )
    words_plan = models.IntegerField(default=20_000, help_text=escape(d_column_helps["words_plan"][conf.LANG]))
    volumes_plan = models.PositiveSmallIntegerField(
        default=1, help_text=escape(d_column_helps["volumes_plan"][conf.LANG])
    )
    chapters_plan = models.PositiveIntegerField(
        default=10, help_text=escape(d_column_helps["chapters_plan"][conf.LANG])
    )
    words_per_chapter_plan = models.PositiveIntegerField(
        default=2500, help_text=escape(d_column_helps["words_per_chapter_plan"][conf.LANG])
    )

    target_audience = models.CharField(
        max_length=100, default="", help_text=escape(d_column_helps["target_audience"][conf.LANG])
    )
    perspective = models.CharField(
        max_length=100, default="", help_text=escape(d_column_helps["perspective"][conf.LANG])
    )
    genres = models.CharField(max_length=50, default="", help_text=escape(d_column_helps["genres"][conf.LANG]))
    tone_and_style = models.CharField(
        max_length=250, default="", help_text=escape(d_column_helps["tone_and_style"][conf.LANG])
    )
    themes = models.JSONField(default=dict, blank=True, help_text=escape(d_column_helps["themes"][conf.LANG]))

    background = models.TextField(default="", help_text=escape(d_column_helps["background"][conf.LANG]))
    core_story = models.CharField(max_length=250, default="", help_text=escape(d_column_helps["core_story"][conf.LANG]))
    plot = models.TextField(default="", help_text=escape(d_column_helps["plot"][conf.LANG]))
    conflict = models.JSONField(default=dict, blank=True, help_text=escape(d_column_helps["conflict"][conf.LANG]))

    class Meta:
        verbose_name_plural = "stories"
        ordering = ["-created_at"]

    def __str__(self):
        return f"《{self.name}》"

    @classmethod
    def schema_ignored_fields(cls) -> set[str]:
        return super().schema_ignored_fields() | {"author"}

    @staticmethod
    def generate_key(name: str) -> str:
        """generate a unique key for story by a given name

        1. If name is English, combine all parts of names in lower case.
        2. If name is Chinese, combine all Pinyin of each word.

        Args:
            name: The story's name

        Returns:
            A unique key for the story
        """
        return slugify(to_pinyin(name, initial_only=True))

    @property
    def summary(self) -> str:
        return self.core_story

    @property
    def length_of_story(self) -> WebNovelLength:
        l = WebNovelLength.from_word_count(self.words_plan)
        return l

    def get_worlds(self, with_not_selected: bool = False) -> list:
        """Get all created worlds settings of a story"""
        if with_not_selected:
            qs = self.storyworld_set
        else:
            qs = self.storyworld_set.filter(is_selected=True)

        return [world for world in qs.all()]

    def get_characters(self) -> dict:
        """Get all created characters of a story"""
        return {c.code: c for c in self.storycharacter_set.all()}

    async_get_worlds = sync_to_async(get_worlds)
    async_get_characters = sync_to_async(get_characters)
