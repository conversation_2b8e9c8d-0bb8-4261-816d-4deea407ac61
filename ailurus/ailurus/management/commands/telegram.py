import logging
import os

from django.core.management.base import BaseCommand
from dotenv import load_dotenv
from telegram.ext import (
    ContextTypes,
)

import pyllm
from services.tg import TelegramBot
from telegram import Update

# Load environment variables from .env file
load_dotenv()

TELEGRAM_API_TOKEN = os.getenv("TELEGRAM_API_TOKEN")

logger = logging.getLogger(__name__)

llm_setting = pyllm.ModelSetting(
    provider=pyllm.ProviderEnum.GEMINI,
    mod_name=pyllm.ModelNameEnum.GEMINI15_FLASH,
    api_key=os.environ["GEMINI_API_KEY"],
    # api_base="http://localhost:11434",
    max_input_token=1000000,
    max_output_token=1024,
    temperature=0.7,
    top_p=0.3,
)


# class TelegramNotifier(Notifier):
#
#     def __init__(
#         self,
#         chat_turn: ChatTurn,
#         context: ContextTypes.DEFAULT_TYPE,
#         telegram_chat_id: int,
#     ):
#         self.chat_turn = chat_turn
#         self.context = context
#         self.telegram_chat_id = telegram_chat_id
#
#     async def notify(self, message: str):
#         await send_tg_msg(self.context, self.telegram_chat_id, message)
#
#     async def notify_planning(self, data: str) -> None:
#         await send_tg_msg(self.context, self.telegram_chat_id, data)
#
#     async def notify_sources(self, data: List[FetchedData | Tuple]) -> None:
#         await send_tg_msg(self.context, self.telegram_chat_id, f"considering {len(data)} sources")


# async def msg(update: Update, context: ContextTypes.DEFAULT_TYPE):
# telegram_user = update.message.from_user
# message = update.message.text
#
# username = "telegram_" + str(telegram_user.id)
# try:
#     user = await User.objects.filter(
#         Q(username=username) | Q(profile__sso_provider="telegram", profile__sso_id=telegram_user.id)
#     ).aget()
#     logger.debug("user with this telegram id found.")
# except User.DoesNotExist:
#     # TODO: email
#     user = await User.objects.acreate(
#         username=username,
#         first_name=telegram_user.first_name,
#         last_name=telegram_user.last_name,
#     )
#     await Profile.objects.acreate(user=user, sso_provider="telegram", sso_id=telegram_user.id)
#     logger.debug("created user with this telegram id.")
#
# # Show typing indicator while waiting for AI response
# asyncio.create_task(
#     context.bot.send_chat_action(chat_id=update.effective_chat.id, action=constants.ChatAction.TYPING)
# )
#
# # match chat id with telegram chat id. if not, create new chat.
# chat_id = await _get_chat_id(user.id, update.effective_chat.id)
# chat, created = await Chat.acreate_if_id_not_exist(
#     chat_id,
#     user=user,
#     title=ellipse(message),
#     platform=AppPlatform.TELEGRAM.value,
# )
# chat_turn = await ChatTurn.objects.acreate(chat=chat, user_msg=message)
# await _set_chat_id(user.id, update.effective_chat.id, chat.id) if created else None
#
# observer = Observer(
#     notifiers=[
#         TelegramNotifier(chat_turn=chat_turn, context=context, telegram_chat_id=update.effective_chat.id),
#         DbNotifier(chat_turn=chat_turn),
#     ]
# )

# chat_engine = ChatEngine(
#     user,
#     chat_turn=chat_turn,
#     observer=observer,
#     streaming=False,
# )
# resp = await chat_engine.answer(message)

# asyncio.create_task(send_tg_msg(context, update.effective_chat.id, resp))


class Conversation:
    started: bool = False
    username: str = ""
    ai_name: str = "黎深"
    ai_profile: str = "性别男，年龄25岁，职业医生 知识面丰富 看起来有些高冷，言简意赅 实则内敛温柔"
    sys_prompt_msg = ""


class RolePlayBot(TelegramBot):

    commands: list[str] = {"start", "bye", "name", "profile"}

    conversations: dict[str, Conversation] = {}
    llm_cli = pyllm.create_client(llm_setting)
    llm_param = pyllm.create_param(llm_setting)

    def start_conversation(self, username: str) -> Conversation:
        chat = Conversation()
        chat.started = False
        chat.username = username
        chat.sys_prompt_msg = f"""姓名 {chat.ai_name}，{chat.ai_profile}，请以他的身份和我聊天。我名字叫{chat.username}，是你的约会对象。注意输出的时候不要用 <名字>: 开头 """
        return chat

    # def reply(self, message: str) -> str:
    #     cli = pyllm.create_client(llm_setting)
    #     param = pyllm.create_param(llm_setting)

    async def on_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """on message received"""

        telegram_user = update.message.from_user
        message = update.message.text
        username = telegram_user.username or "tg_" + str(telegram_user.id)
        logger.debug(f"{username}: {message}")

        chat = self.conversations.get(username)
        if not chat:
            self.send_message_no_wait(context, update.effective_chat.id, f"你好，{username}")
            chat = self.start_conversation(username)
            self.conversations[chat.username] = chat

        prompts = pyllm.ChatPrompts().set_system(chat.sys_prompt_msg).set_inquery(message)
        answer = self.llm_cli._completion(prompts, self.llm_param)
        logger.debug(f"{chat.ai_name}: {answer}")
        self.send_message_no_wait(context, update.effective_chat.id, answer)

        if chat.started:
            chat.sys_prompt_msg += "\n 以下是咱们的对话历史 \n ####################### \n\n"
            chat.started = True

        chat.sys_prompt_msg += f"{chat.username}: {chat.ai_name}\n"
        chat.sys_prompt_msg += f"{chat.ai_name}: {message} \n"

    async def on_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:

        telegram_user = update.message.from_user
        msg = update.message.text
        username = telegram_user.username or "tg_" + str(telegram_user.id)

        if msg == "/start":
            resp = "Hello !"
        elif msg == "/bye":
            resp = "See you next time !"
        elif msg == "/name":
            resp = ""
        elif msg == "/profile":
            resp = ""
        else:
            resp = "invalid command."

        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text=resp,
        )


class Command(BaseCommand):

    def handle(self, *args, **kwargs):
        bot = RolePlayBot()
        bot.start()
