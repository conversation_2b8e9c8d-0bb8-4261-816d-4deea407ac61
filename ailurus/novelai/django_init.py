# A script that's needed to setup django if it's not already running on a server.
# Without this, you won't be able to import django modules
import os
import sys
from pathlib import Path

import django

# Find the project base directory
BASE_DIR = str(Path(__file__).parent.parent)

# Add the project base directory to the sys.path for package lookup
sys.path.insert(0, BASE_DIR)

# The DJANGO_SETTINGS_MODULE has to be set to allow us to access django imports
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "app.settings")

#  Allow queryset filtering asynchronously when running in a Jupyter notebook
os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"

# set log level
# os.environ["LOG_LEVEL"] = "DEBUG"

# This is for setting up django
django.setup()


def init_django() -> None:
    print(f"Django Initialized for {BASE_DIR}.")
