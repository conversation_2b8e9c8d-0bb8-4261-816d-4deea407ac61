import asyncio
import os

from telegram import Update, Message
from telegram.ext import (
    <PERSON><PERSON><PERSON><PERSON>,
    CommandHandler,
    ContextTypes,
    MessageHandler,
    filters,
    Application,
)

from pycommon.utils.lang import json_dumps

TELEGRAM_API_TOKEN = os.getenv("TELEGRAM_API_TOKEN")


class TelegramBot:

    commands: list[str] = ["start", "bye"]

    __app: Application = ApplicationBuilder().token(TELEGRAM_API_TOKEN).build()

    def start(self) -> None:
        """start the bot"""
        application = self.__app
        start_handler = CommandHandler(self.commands, self.on_command)
        msg_handler = MessageHandler((filters.TEXT | filters.VOICE) & (~filters.COMMAND), self.on_message)
        application.add_handler(start_handler)
        application.add_handler(msg_handler)
        application.run_polling(stop_signals=[13])

    def stop(self) -> None:
        if self.__app:
            self.__app.stop_running()

    # async def on_command(self, command: str, chat_id: int, context: ContextTypes.DEFAULT_TYPE) -> Any:
    #
    #     msg = update.message.text
    #
    #     if command == "/start":
    #         resp = "Hello !"
    #     elif command == "/bye":
    #         resp = "See you next time !"
    #     else:
    #         resp = "invalid command."
    #
    #     self.send_message_no_wait(context, chat_id, resp)

    async def on_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        msg = update.message.text

        if msg == "/start":
            resp = "Hello !"
        elif msg == "/bye":
            resp = "See you next time !"
        else:
            resp = "invalid command."

        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text=resp,
        )

    async def on_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """on message received"""

        telegram_user = update.message.from_user
        message = update.message.text
        username = "telegram_" + str(telegram_user.id)
        print(f"{username}: {message}")
        # asyncio.create_task(self.send_message(context, update.effective_chat.id, resp))

    def send_message_no_wait(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, msg: str):
        asyncio.create_task(self.send_message(context, chat_id, msg))

    async def send_message(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, msg: str):
        # Telegram has a limit on resp per msg.
        # If resp is more than 4000 characters, chunk it and send it in multiple messages
        if len(msg) > 4000:
            for i in range(0, len(msg), 4000):
                await context.bot.send_message(chat_id=chat_id, text=msg[i : i + 4000])  # noqa: E203
        else:
            await context.bot.send_message(chat_id=chat_id, text=msg)

    async def echo(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        await context.bot.send_message(chat_id=update.effective_chat.id, text=update.message.text)

    def format_message(self, message: Message) -> str:
        print(json_dumps(message.channel_chat_created))
        print(json_dumps(message.chat))
        print(json_dumps(message.date))
        print(json_dumps(message.entities))
        print(json_dumps(message.from_user))
        print(json_dumps(message.text))
        return ""


if __name__ == "__main__":
    bot = TelegramBot()
    bot.start()
