#!/usr/bin/env python
# coding=utf-8

"""
Some common useful functions for all parts.
"""

from sys import stdout, stderr, platform as sys_platform, version_info as sys_ver_info
import platform
from os import getenv, linesep, system
from os.path import abspath
from gettext import gettext as _
from base64 import b64encode, b64decode
from hashlib import sha1
import hmac
from typing import Union
from datetime import timezone, timedelta, datetime

# import secrets
import random

from logwrapper import getLogger
from consts import DEBUG
from types import UniqueIdentity

UTC = timezone.utc
CST = timezone(timedelta(hours=8), "China Standard Time")

log = getLogger(__package__)


# ----- terminal output related -----

_win_vterm_mode = None
_is_win = sys_platform.startswith("win")
if _is_win:

    def enable_windows_terminal_mode():
        """Enable virtual terminal processing in windows terminal. Does
        nothing if not on Windows. This is based on the rejected
        enhancement <https://bugs.python.org/issue29059>."""
        global _win_vterm_mode
        if _win_vterm_mode != None:
            return _win_vterm_mode

        # Note: Cygwin should return something like "CYGWIN_NT..."
        _win_vterm_mode = platform.system().lower() == "windows"
        if _win_vterm_mode == False:
            return

        from ctypes import windll, c_int, byref, c_void_p

        ENABLE_VIRTUAL_TERMINAL_PROCESSING = 0x0004
        INVALID_HANDLE_VALUE = c_void_p(-1).value
        STD_OUTPUT_HANDLE = c_int(-11)

        hStdout = windll.kernel32.GetStdHandle(STD_OUTPUT_HANDLE)
        if hStdout == INVALID_HANDLE_VALUE:
            _win_vterm_mode = False
            return

        mode = c_int(0)
        ok = windll.kernel32.GetConsoleMode(c_int(hStdout), byref(mode))
        if not ok:
            _win_vterm_mode = False
            return

        mode = c_int(mode.value | ENABLE_VIRTUAL_TERMINAL_PROCESSING)
        ok = windll.kernel32.SetConsoleMode(c_int(hStdout), mode)
        if not ok:
            # Something went wrong, proably an too old version
            # that doesn't support the VT100 mode.
            # To be more certain we could check kernel32.GetLastError
            # for STATUS_INVALID_PARAMETER, but since we only enable
            # one flag we can be certain enough.
            _win_vterm_mode = False
            return

    enable_windows_terminal_mode()


class TerminalColor(object):
    black = lambda s: "\033[30m%s\033[0m" % s if _win_vterm_mode or not _is_win else s
    red = lambda s: "\033[31m%s\033[0m" % s if _win_vterm_mode or not _is_win else s
    green = lambda s: "\033[32m%s\033[0m" % s if _win_vterm_mode or not _is_win else s
    yellow = lambda s: "\033[33m%s\033[0m" % s if _win_vterm_mode or not _is_win else s
    blue = lambda s: "\033[34m%s\033[0m" % s if _win_vterm_mode or not _is_win else s
    magenta = lambda s: "\033[35m%s\033[0m" % s if _win_vterm_mode or not _is_win else s
    cyan = lambda s: "\033[36m%s\033[0m" % s if _win_vterm_mode or not _is_win else s
    white = lambda s: "\033[37m%s\033[0m" % s if _win_vterm_mode or not _is_win else s
    gray = lambda s: "\033[90m%s\033[0m" % s if _win_vterm_mode or not _is_win else s


def pcrlf(count=1, to_stderr=False):
    """print a CR|LF new line"""
    for i in range(count):
        if to_stderr is True:
            stderr.write(linesep)
            stderr.flush()
        else:
            stdout.write(linesep)
            stdout.flush()


def clear_screen():
    if _win_vterm_mode or not _is_win:
        print(chr(27) + "[2J")
    else:
        system("cls")


def debug(s, newline=True):
    if DEBUG is True:
        stdout.write(TerminalColor.gray(s))
        stdout.flush()
        if newline is True:
            pcrlf()


def info(s, newline=True):
    stdout.write(s)
    if newline is True:
        pcrlf()


def tip(s, newline=True):
    stdout.write(TerminalColor.cyan(s))
    stdout.flush()
    if newline is True:
        pcrlf()


def warn(s, newline=True):
    stdout.write(TerminalColor.yellow(s))
    stdout.flush()
    if newline is True:
        pcrlf(True)


def error(s, newline=True):
    stdout.write(TerminalColor.red(s))
    stdout.flush()
    if newline is True:
        pcrlf(True)


def success(s, newline=True):
    stdout.write(TerminalColor.green(s))
    stdout.flush()
    if newline is True:
        pcrlf(True)


# ----- sys related -----


def get_user_home():
    """get user home path on different sys_platform"""
    path = ""
    if sys_platform.startswith("win"):
        path = getenv("USERPROFILE")
    elif sys_platform.startswith("darwin"):
        path = getenv("HOME", "~")
    elif sys_platform.startswith("linux"):
        path = getenv("HOME", "~")
    else:
        warn(_('Unsupported OS %s. Use current path "." as home.') % sys_platform)
        path = abspath(".")

    return path


def get_local_time(dt: datetime = None, tz: timezone = CST):
    """convert given datetime to local datetime with given timezone information.

    :param dt: datetime with timezone information.
    :param tz: target timezone information.
    :return: converted datetime object with target timezone information.
    """
    if not dt:
        dt = datetime.utcnow()
    assert isinstance(dt, datetime)
    dt = dt.replace(tzinfo=UTC)
    return dt.astimezone(tz)


def get_thread_id(thread):
    # TODO: add get_tname func
    if sys_ver_info.major >= 3 and sys_ver_info.minor >= 8:
        return thread.native_id
    else:
        # return thread.ident
        return thread.getName()


# ----- auth methods -----

# def encrypt(s):
#     return s

# def decrypt(s):
#     return s


def auth_encode(s: str) -> str:
    if s is None:
        return s
    return b64encode(s.encode("utf-8")).decode()


def auth_decode(s: str) -> str:
    if s is None:
        return s
    return b64decode(s.encode("utf-8")).decode()


def sign_request(
    secur_key: Union[str, bytearray, bytes],
    scope: int,
    uid: UniqueIdentity,
    http_method: str,
    url: str,
    timestamp: Union[datetime, int, float],
) -> str:
    """
    Sign a HTTP request for authorization. (HMAC SHA1)

    :param secur_key: the key used to encript
    :param scope: the request client type. refer to `CredentialScope`
    :param pk: the primary key or identity of the client.
    :param http_method: The request http verb such as `POST`, `DELETE` and etc
    :param url: the full request url with queries. e.g. http://example.com/v1/call?foo=bar&code=1
    :param timestamp: the timestamp number the request submitted. e.g. 12338263467

    :return: signed string by contructed by the above parameters.
    """
    if isinstance(timestamp, datetime):
        timestamp: float = timestamp.timestamp()
    timestamp = int(timestamp)
    if isinstance(secur_key, str):
        secur_key = secur_key.encode("utf-8")
    secur_key = bytearray(secur_key)
    str_to_sign = "%s\n%s\n%s\n%s\n%d" % (scope, pk, http_method, url, timestamp)
    hashed = hmac.new(secur_key, str_to_sign.encode("utf-8"), sha1)
    signed = hashed.digest()
    encoded = b64encode(signed).decode()

    return encoded


def gen_token(length=32):
    s = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz"
    return "".join([random.choice(s) for i in range(length)])
    # return secrets.token_urlsafe(length)


# ----- util methods -----

# def parse_address(address):
#     ip: str = '127.0.0.1'
#     port: int = 0
#     if address:
#         try:
#             ip_port = address.split(':')
#             if len(ip_port) == 1:
#                 port = int(ip_port[0])
#             elif len(ip_port) == 2:
#                 ip = ip_port[0]
#                 port = int(ip_port[1])
#             else:
#                return (None, None)
#         except ValueError:
#             return (None, None)
#     return ip, port


# ---- util classes -----
# import re

# class Checker(object):
#     # _re_domain = re.compile(r'[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+.?')
#     _re_word = re.compile(r'^\w+$')
#     _re_domain = re.compile(r'^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$')
#     _re_domain_sub = re.compile(r'^[a-z0-9][a-z0-9\-]*$')
#     @classmethod
#     def is_domain(cls, value):
#         match = cls._re_word.match(value)
#         match1 = cls._re_domain.match(value)
#         return True if match or match1 else False


#     @classmethod
#     def is_sub_domain(cls, value):
#         match = cls._re_domain_sub.match(value)
#         return True if match else False


#     @classmethod
#     def is_ip(cls, value):
#         assert isinstance(value, str)

#         parts = value.split('.')
#         if len(parts) != 4:
#             return False
#         for p in parts:
#             try:
#                 if not (0 < int(p) < 255):
#                     return False
#             except:
#                 return False
#         return True

#     @classmethod
#     def is_port(cls, value):
#         assert isinstance(value, (str, int))
#         if isinstance(value, str):
#             if not str.isdigit(value):
#                 return False
#             value = int(value)
#         if 0 <= value <= 65535:
#             return True
#         else:
#             return False


# ----- rate limit ----
# from collections import OrderedDict

# class TimeframeWindow(object):

#     def __init__(self, buckets=60, duration=60):
#         """
#         buckets: how many frames will a period be slipped into
#         duration: seconds. how long will a bucket exist.
#         """
#         self._bucket_count = buckets
#         self._duration = duration
#         self._frame = duration / self._bucket_count
#         self._last_key1 = self._last_key = self._current_key = 0
#         self._buckets = OrderedDict()       # before 3.6
#         self._started = datetime.utcnow()
#         self._total = 0

#     def incr(self, count):
#         key = self.get_current_key()
#         self.incr_bucket(key, count)

#     def get(self):
#         key = self.get_current_key()
#         value = self.get_bucket(key)
#         return key, value

#     def set(self, value):
#         key = self.get_current_key()
#         self.set_bucket(key, value)

#     def get_current_key(self):
#         return self.get_key(datetime.utcnow())

#     def get_key(self, dt):
#         return int(dt.timestamp() // self._frame)

#     def exist(self, key):
#         value = self.get_bucket(key)
#         return True if value else False

#     def get_bucket(self, key, with_time=False):
#         if key in self._buckets:
#             st, value = self._buckets[key]
#             if (datetime.utcnow() - st).total_seconds() > self._duration:
#                 self.del_bucket(key)
#                 return (st, None) if with_time else None
#             else:
#                 return (st, value) if with_time else value
#         else:
#             return (None, None) if with_time else None

#     def set_bucket(self, key, value):
#         is_new = key not in self._buckets
#         if is_new:
#             self._buckets[key] = (datetime.utcnow(), value)
#             self._last_key1 = self._last_key
#             self._last_key = self._current_key
#             self._current_key = key
#         else:
#             self._buckets[key] = (self._buckets[key][0], value)
#         # print('set: ', key, value)

#     def incr_bucket(self, key, count):
#         value = self.get_bucket(key)
#         if value:
#             value += count
#         else:
#             value = count
#         self.set_bucket(key, value)
#         self._total += value

#     def del_bucket(self, key):
#         if key in self._buckets:
#             del self._buckets[key]


#     # def get_bandwidth_rt(self, frames=30):
#     #     """calculate real time (recent frames) bandwidth Byte/s

#     #     This function will calculate the duration (seconds) by given frames (frame * frames)
#     #     And loop to sum all traffic.

#     #     NOTE: DO NOT use it in performance sensitive scenario if too long.
#     #     """
#     #     duration =  self._frame * frames
#     #     # log.debug('bandwidth duration: %.2f seconds' % duration)
#     #     assert frames < 100         # do NOT too many (times)
#     #     assert duration < 120       # do NOT too long (seconds)
#     #     t = datetime.utcnow() - timedelta(seconds=duration)
#     #     first_key = int(self.get_key(t))
#     #     total = 0
#     #     # print('first key: ', first_key, '|', get_local_time(t).strftime('%H:%M:%S'))
#     #     for key in reversed(list(self._buckets.keys())):
#     #         if key <= first_key:
#     #             break
#     #         t, value = self.get_bucket(key, with_time=True)
#     #         # print('+', key, '|', get_local_time(t).strftime('%H:%M:%S'), '|', humanize.naturalsize(value))
#     #         total += value or 0

#     #     bw_rt = int(total / duration)
#     #     # print('total: ', humanize.naturalsize(total), duration, humanize.naturalsize(bw_rt))
#     #     return bw_rt

#     def get_bandwidth_rt(self):
#         # for key, (dt, value) in self._buckets.items():
#         #     print(key, value, int(value / self._frame))
#         value = self.get_bucket(self._last_key) or 0
#         frames = self._last_key - self._last_key1
#         if not frames:
#             return 0
#         duration = frames * self._frame
#         bw_rt = value / duration
#         return bw_rt
