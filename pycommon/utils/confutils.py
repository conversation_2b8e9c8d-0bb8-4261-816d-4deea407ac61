import os
from typing import Optional, Callable, List, Any


def get_bool_env(name: str, default: bool = False) -> bool:
    return os.getenv(name, str(default)).lower() in ["1", "on", "true", "yes"]


def get_int_env(name: str, default: int = 0) -> int:
    try:
        return int(os.getenv(name, default))
    except (ValueError, TypeError):
        return default


def get_str_env(name: str, default: str = "") -> str:
    return os.getenv(name, default)


def get_list_env(name: str, convertor: Optional[Callable] = None) -> List[Any]:
    """get a list from env.

    env name must be ENV_NAME and ENV_NAME.# (# is index)
    if ENV_NAME.# not found, use ENV_NAME
    i.e.

    name is 'MY_ENV'

    MY_ENV.0 = 'foo'
    MY_ENV.1 = 'bar'
    MY_ENV.2 = 'baz'
    # no MY_ENV.3
    MY_ENV.4 = 'except'

    you will get:
    ['foo', 'bar', 'baz']

    """
    values: List[Any] = []

    # try getting comma separated values first.
    v = os.getenv(name)
    if v:
        values = v.strip().split(",")
        if len(values) > 0 and values[0]:
            return values

    # try getting NAME.0, NAME.1 styles.
    i = 0
    while True:
        v = os.getenv(f"{name}.{i}")
        if v is not None:
            values.append(convertor(v) if convertor else v)
            i += 1
        else:
            if len(values) == 0:
                v = get_str_env(name)
                values.append(convertor(v) if convertor else v)
            break
    return values


class MultiValueConfItem(list):
    __idx: int = 0
    __circle_count: int = -1

    def __init__(self, name: str, convertor: Optional[Callable] = None):
        super().__init__(get_list_env(name, convertor))
        self.reset()

    @property
    def value(self) -> Any:
        return self[self.__idx]

    def next(self) -> Any:
        self.__idx = 0 if self.__idx == len(self) - 1 else self.__idx + 1
        self.__circle_count -= 1
        return self[self.__idx]

    @property
    def circled(self) -> bool:
        return self.__circle_count <= 0

    def reset(self, idx: int = 0) -> None:
        self.__idx = idx
        self.__circle_count = len(self)


class ObjectLikeConfItem(dict):
    def __getattr__(self, item: str) -> str:
        return str(self.get(item))

    def __setattr__(self, key: str, value: str) -> None:
        self[key] = value


def get_obj_env(name: str) -> ObjectLikeConfItem:
    """get a dict like object from env.

    env name must be ENV_NAME.{FIELD}
    i.e.

    name is 'MY_ENV'

    MY_ENV.foo = 'foo'
    MY_ENV.bar = 'bar'
    MY_ENV.baz = '3'

    you will get:
    my_env = { "foo": "foo", "bar": "bar", "baz": 3 }

    you can access:
    print(my_env.foo)

    """
    d = ObjectLikeConfItem()
    for k in os.environ.keys():
        if k.startswith(f"{name}."):
            field = k.split(".")[1]
            v = os.getenv(k)
            d[field] = v
    return d
