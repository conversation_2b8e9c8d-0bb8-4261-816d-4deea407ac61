import re
from typing import List, Optional

from pydantic import BaseModel, <PERSON>, model_validator

from scraper.base import INFLUENCER_PLATFORMS


class SocialPost(BaseModel):
    """Base model for social media posts"""

    id: str
    url: str

    # for ins and tiktok this is the caption, for youtube this is the video title
    caption: str

    # currently we scraped the text(or unix timestamp), leaving "parsing to datetime" to the caller
    published_time_str: str

    like_count: Optional[int] = None
    comment_count: Optional[int] = None


class InstagramPost(SocialPost):
    """Instagram specific post model"""

    pk: int
    reshare_count: Optional[int] = None
    play_count: Optional[int] = None


class TiktokPost(SocialPost):
    """TikTok specific post model"""

    duration: int  # in seconds
    music_title: str
    music_author: str
    play_count: int
    digg_count: int
    comment_count: int
    share_count: int
    collect_count: int
    hashtags: List[str] = Field(default_factory=list)


class YoutubePost(SocialPost):
    """YouTube specific post model"""

    duration: str  # Original duration string (e.g., "3:26")
    duration_seconds: int
    views: str  # Original views string (e.g., "1,370,468 views")


class SocialProfile(BaseModel):
    """Base model for all social media profiles"""

    platform: INFLUENCER_PLATFORMS = Field(
        ..., description="Which platform the profile belongs to"
    )
    handle: str = Field(..., description="Username/handle of the profile")
    full_name: str = Field(..., description="Full name of the profile")
    url: str = Field(..., description="URL of the profile page")
    biography: str = Field(
        ..., description="Biography, signature, or description of the profile"
    )
    followers: int = Field(..., description="Number of followers or subscribers")
    avatar_url: str = Field(..., description="Avatar URL of the profile")
    posts_count: int = Field(..., description="Number of posts of the profile")

    relevant_post: Optional[SocialPost] = Field(
        None,
        description="Most relevant post of the profile given the search query",
    )
    recent_posts: Optional[List[SocialPost]] = Field(
        default_factory=list,
        description="Most recent posts of the profile",
    )
    email: Optional[str] = Field(
        None,
        description="The extracted email of the profile",
    )

    @model_validator(mode="after")
    def check_posts_exist(self) -> "SocialProfile":
        if not self.relevant_post and not self.recent_posts:
            raise ValueError("At least one of relevant_post or recent_posts must exist")
        return self

    def __str__(self) -> str:
        return f"{self.handle} ({self.url})"

    def to_dict(self) -> dict:
        return self.model_dump()


class InstagramProfile(SocialProfile):
    """Instagram specific profile model"""

    platform: INFLUENCER_PLATFORMS = "instagram"
    following: int


class TiktokProfile(SocialProfile):
    """TikTok specific profile model"""

    platform: INFLUENCER_PLATFORMS = "tiktok"
    following: int


class YoutubeChannelProfile(SocialProfile):
    """YouTube channel specific information"""

    platform: INFLUENCER_PLATFORMS = "youtube"
    keywords: Optional[str] = None


def parse_count(count: str | int) -> int:
    """Parse the count string from Instagram crawler into an integer."""

    if isinstance(count, int):
        return count

    if not count:
        return 0

    # Clean the input to remove all non-numeric characters except K, M, and .
    count = re.sub(r"[^0-9KM\.]", "", count.upper().strip())

    if "K" in count:
        return int(float(count.replace("K", "")) * 1000)
    elif "M" in count:
        return int(float(count.replace("M", "")) * 1000000)
    else:
        return int(float(count))
