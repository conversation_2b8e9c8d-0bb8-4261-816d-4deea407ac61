"""This is a tool that act like a DB table.

1. It's similar to pandas Dataframe but not focus on data analysis. It holds a list of records.
2. It's similar to Django model but not focus on ORM. It's just a data holder.
3. Functionality
    a. access by column name
    b. access by row index
    c. filter by column value depends on the column type. (operators ref to Django)
        1) for str, filter like search. filter operators: in, not in, contains, not contains, =, !=, >, <, >=, <=
        2) for int/float/any number types, filter operators: =, !=, >, <, >=, <=
        3) for datetime/date/time, filter operators: =, !=, >, <, >=, <=
        4) for list, filter operators: in, not in, contains, not contains
        5) for dict, filter operators: key/value contains, key/value not contains
        4) logical operators: and, or, not
    d. sort by column value
    e. group by column value
    f. aggregate by column value
    g. pretty output
    h. join another table
    i. pagination action for a table
    j. limit max memory usage or limit max cells (row * columns)
4. Fast, Light
"""

import sys
from collections import defaultdict
from typing import Any, List, Dict, Union, Optional, Tuple, Callable


class PySimpleTable:
    """A light-weight fast table-like structure to hold, query, aggregate, sort, join and manipulate data.

    This class provides functionality similar to pandas DataFrame but focuses on data storage
    and querying rather than analysis. It's also similar to Django models but without ORM features.

    Features:
    - Access data by column name or row index
    - Filter data with various operators (Django-style)
    - Sort, group and aggregate data
    - Join with other tables
    - Pagination support
    - Memory/cell limit management
    - Pretty output formatting

    Usage Examples:

    # Create table from data
    data = [{'name': 'Alice', 'age': 25}, {'name': 'Bob', 'age': 30}]
    table = SamTable(data)

    # Access by column
    names = table['name']  # ['Alice', 'Bob']

    # Access by row
    first_row = table[0]  # {'name': 'Alice', 'age': 25}

    # Filter data
    adults = table.filter(age={'>=': 18})
    young = table.filter(name={'contains': 'A'})

    # Sort data
    sorted_table = table.sort_values('age', ascending=False)

    # Group and aggregate
    groups = table.group_by('age')
    stats = table.agg(age='mean')

    # Join tables
    other_table = SamTable([{'name': 'Alice', 'city': 'NY'}])
    joined = table.join(other_table, on='name')

    # Pagination
    page1 = table.paginate(page=1, page_size=10)

    # Memory limits
    table.set_memory_limit(max_cells=1000)
    """

    def __init__(
        self,
        data: Optional[List[Dict[str, Any]]] = None,
        columns: Optional[List[str]] = None,
        max_cells: Optional[int] = None,
        max_memory_mb: Optional[float] = None,
    ):
        self._data = data or []
        self._columns = columns or []
        self._max_cells = max_cells
        self._max_memory_mb = max_memory_mb

        if self._data and not self._columns:
            self._columns = list(self._data[0].keys()) if self._data else []

        if self._columns and not self._data:
            self._data = []

        # Check limits after initialization
        self._check_limits()

    def __len__(self):
        return len(self._data)

    def __iter__(self):
        return iter(self._data)

    def __bool__(self) -> bool:
        return bool(self._data)

    def __sizeof__(self) -> int:
        return sys.getsizeof(self._data) + sys.getsizeof(self._columns)

    def __contains__(self, key: str) -> bool:
        return key in self._columns

    @property
    def columns(self):
        return self._columns.copy()

    # @property
    # def data(self):
    #     return self._data.copy()

    def add_row(self, row: Union[Dict[str, Any], List[Any]]):
        if isinstance(row, dict):
            if not self._columns:
                self._columns = list(row.keys())
            self._data.append(row)
        elif isinstance(row, list):
            if not self._columns:
                self._columns = [f"col_{i}" for i in range(len(row))]
            row_dict = {self._columns[i]: row[i] for i in range(len(row))}
            self._data.append(row_dict)

        # Check limits after adding row
        self._check_limits()

    def delete_rows(self, condition: Callable[[Dict[dict]], bool]) -> int:
        """Delete rows that match the condition"""
        # TODO: optimize
        cnt = len(self._data)
        self._data = [row for row in self._data if not condition(row)]
        cnt = cnt - len(self._data)
        return cnt

    def clear(self) -> None:
        self._data.clear()

    def __getitem__(self, key: Union[str, int, slice, tuple]):
        if isinstance(key, str):
            return [row.get(key) for row in self._data]
        elif isinstance(key, int):
            if key < 0 or key >= len(self._data):
                raise IndexError("Row index out of range")
            return self._data[key]
        elif isinstance(key, slice):
            return PySimpleTable(
                data=self._data[key],
                columns=self._columns,
                max_cells=self._max_cells,
                max_memory_mb=self._max_memory_mb,
            )
        elif isinstance(key, tuple) and len(key) == 2:
            row_idx, col_key = key
            if isinstance(col_key, str):
                if row_idx < 0 or row_idx >= len(self._data):
                    raise IndexError("Row index out of range")
                return self._data[row_idx].get(col_key)
            elif isinstance(col_key, int):
                if col_key < 0 or col_key >= len(self._columns):
                    raise IndexError("Column index out of range")
                col_name = self._columns[col_key]
                if row_idx < 0 or row_idx >= len(self._data):
                    raise IndexError("Row index out of range")
                return self._data[row_idx].get(col_name)
        raise TypeError("Invalid key type")

    def __setitem__(self, key: Union[str, int, tuple], value: Any):
        if isinstance(key, str):
            if key not in self._columns:
                self._columns.append(key)

            if isinstance(value, list):
                if len(value) != len(self._data):
                    raise ValueError("Length of values must match number of rows")
                for i, row in enumerate(self._data):
                    row[key] = value[i]
            else:
                for row in self._data:
                    row[key] = value

        elif isinstance(key, int):
            if key < 0 or key >= len(self._data):
                raise IndexError("Row index out of range")
            if isinstance(value, dict):
                self._data[key] = value
            else:
                raise TypeError("Row value must be a dictionary")

        elif isinstance(key, tuple) and len(key) == 2:
            row_idx, col_key = key
            if isinstance(col_key, str):
                if col_key not in self._columns:
                    self._columns.append(col_key)
                if row_idx < 0 or row_idx >= len(self._data):
                    raise IndexError("Row index out of range")
                self._data[row_idx][col_key] = value
            elif isinstance(col_key, int):
                if col_key < 0 or col_key >= len(self._columns):
                    raise IndexError("Column index out of range")
                col_name = self._columns[col_key]
                if row_idx < 0 or row_idx >= len(self._data):
                    raise IndexError("Row index out of range")
                self._data[row_idx][col_name] = value
        else:
            raise TypeError("Invalid key type")

    def append(self, row: Union[Dict[str, Any], List[Any]]):
        if isinstance(row, dict):
            if not self._columns:
                self._columns = list(row.keys())
            self._data.append(row)
        elif isinstance(row, list):
            if not self._columns:
                self._columns = [f"col_{i}" for i in range(len(row))]
            row_dict = {self._columns[i]: row[i] for i in range(len(row))}
            self._data.append(row_dict)

        # Check limits after adding row
        self._check_limits()

    def filter(self, **kwargs):
        """

        :param kwargs:
                conditions: e.g. name={"contain", "Bob"}, age={">", 20}
                `ignore_case`: Whether to ignore case for string comparisons
        :return:
        """
        ignore_case: bool = str(kwargs.pop("ignore_case")).lower() == "true" if "ignore_case" in kwargs else False

        filtered_data = []
        for row in self._data:
            match = True
            for col, condition in kwargs.items():
                if col not in row:
                    match = False
                    break

                value = row[col]
                if not self._matches_condition(value, condition, ignore_case):
                    match = False
                    break

            if match:
                filtered_data.append(row)

        return PySimpleTable(
            data=filtered_data, columns=self._columns, max_cells=self._max_cells, max_memory_mb=self._max_memory_mb
        )

    def _matches_condition(self, value: Any, condition: Union[Any, Dict[str, Any]], ignore_case: bool = False) -> bool:
        if not isinstance(condition, dict):
            if isinstance(value, str) and isinstance(condition, str) and ignore_case:
                return value.lower() == condition.lower()
            return value == condition

        for op, expected in condition.items():
            if op == "eq" or op == "=":
                if isinstance(value, str) and isinstance(expected, str) and ignore_case:
                    if value.lower() != expected.lower():
                        return False
                else:
                    if value != expected:
                        return False
            elif op == "ne" or op == "!=":
                if isinstance(value, str) and isinstance(expected, str) and ignore_case:
                    if value.lower() == expected.lower():
                        return False
                else:
                    if value == expected:
                        return False
            elif op == "gt" or op == ">":
                # None values cannot be compared, exclude them
                if value is None or expected is None:
                    return False
                if isinstance(value, str) and isinstance(expected, str) and ignore_case:
                    if value.lower() <= expected.lower():
                        return False
                else:
                    if value <= expected:
                        return False
            elif op == "gte" or op == ">=":
                # None values cannot be compared, exclude them
                if value is None or expected is None:
                    return False
                if isinstance(value, str) and isinstance(expected, str) and ignore_case:
                    if value.lower() < expected.lower():
                        return False
                else:
                    if value < expected:
                        return False
            elif op == "lt" or op == "<":
                # None values cannot be compared, exclude them
                if value is None or expected is None:
                    return False
                if isinstance(value, str) and isinstance(expected, str) and ignore_case:
                    if value.lower() >= expected.lower():
                        return False
                else:
                    if value >= expected:
                        return False
            elif op == "lte" or op == "<=":
                # None values cannot be compared, exclude them
                if value is None or expected is None:
                    return False
                if isinstance(value, str) and isinstance(expected, str) and ignore_case:
                    if value.lower() > expected.lower():
                        return False
                else:
                    if value > expected:
                        return False
            elif op == "in":
                if isinstance(value, str) and isinstance(expected, str):
                    # None values cannot be searched in strings
                    if value is None or expected is None:
                        return False
                    if ignore_case:
                        if expected.lower() not in value.lower():
                            return False
                    else:
                        if expected not in value:
                            return False
                elif hasattr(expected, "__iter__") and not isinstance(expected, str):
                    # Handle iterables (but not strings, as they're handled above)
                    if isinstance(value, str) and ignore_case:
                        if not any(
                            value.lower() == item.lower() if isinstance(item, str) else value == item
                            for item in expected
                        ):
                            return False
                    else:
                        if value not in expected:
                            return False
                else:
                    return False
            elif op == "not_in":
                if isinstance(value, str) and isinstance(expected, str):
                    # None values cannot be searched in strings
                    if value is None or expected is None:
                        return False
                    if ignore_case:
                        if expected.lower() in value.lower():
                            return False
                    else:
                        if expected in value:
                            return False
                elif hasattr(expected, "__iter__") and not isinstance(expected, str):
                    # Handle iterables (but not strings, as they're handled above)
                    if isinstance(value, str) and ignore_case:
                        if any(
                            value.lower() == item.lower() if isinstance(item, str) else value == item
                            for item in expected
                        ):
                            return False
                    else:
                        if value in expected:
                            return False
            elif op == "contains":
                if isinstance(value, str):
                    # None values cannot contain anything or be contained
                    if value is None or expected is None:
                        return False
                    if ignore_case and isinstance(expected, str):
                        if expected.lower() not in value.lower():
                            return False
                    else:
                        if expected not in value:
                            return False
                elif isinstance(value, (list, tuple)):
                    if isinstance(expected, str) and ignore_case:
                        if not any(
                            expected.lower() == item.lower() if isinstance(item, str) else expected == item
                            for item in value
                        ):
                            return False
                    else:
                        if expected not in value:
                            return False
                elif isinstance(value, dict):
                    if isinstance(expected, str) and ignore_case:
                        if not any(expected.lower() == str(v).lower() for v in value.values()):
                            return False
                    else:
                        if expected not in value.values():
                            return False
                else:
                    return False
            elif op == "not_contains":
                if isinstance(value, str):
                    # None values cannot contain anything or be contained
                    if value is None or expected is None:
                        return False
                    if ignore_case and isinstance(expected, str):
                        if expected.lower() in value.lower():
                            return False
                    else:
                        if expected in value:
                            return False
                elif isinstance(value, (list, tuple)):
                    if isinstance(expected, str) and ignore_case:
                        if any(
                            expected.lower() == item.lower() if isinstance(item, str) else expected == item
                            for item in value
                        ):
                            return False
                    else:
                        if expected in value:
                            return False
                elif isinstance(value, dict):
                    if isinstance(expected, str) and ignore_case:
                        if any(expected.lower() == str(v).lower() for v in value.values()):
                            return False
                    else:
                        if expected in value.values():
                            return False

        return True

    def sort_values(self, by: Union[str, List[str]], ascending: bool = True):
        if isinstance(by, str):
            by = [by]

        def sort_key(row):
            values = []
            for col in by:
                value = row.get(col)
                if value is None:
                    values.append("")
                else:
                    values.append(value)
            return values

        sorted_data = sorted(self._data, key=sort_key, reverse=not ascending)
        return PySimpleTable(
            data=sorted_data, columns=self._columns, max_cells=self._max_cells, max_memory_mb=self._max_memory_mb
        )

    def group_by(self, by: Union[str, List[str]]):
        if isinstance(by, str):
            by = [by]

        groups = defaultdict(list)
        for row in self._data:
            key = tuple(row.get(col) for col in by)
            groups[key].append(row)

        return {
            key: PySimpleTable(
                data=rows, columns=self._columns, max_cells=self._max_cells, max_memory_mb=self._max_memory_mb
            )
            for key, rows in groups.items()
        }

    def agg(self, **kwargs):
        result = {}
        for col, func in kwargs.items():
            if col not in self._columns:
                continue

            values = [row.get(col) for row in self._data if row.get(col) is not None]

            if func == "sum":
                result[col] = sum(values)
            elif func == "mean" or func == "avg":
                result[col] = sum(values) / len(values) if values else 0
            elif func == "count":
                result[col] = len(values)
            elif func == "min":
                result[col] = min(values) if values else None
            elif func == "max":
                result[col] = max(values) if values else None
            elif callable(func):
                result[col] = func(values)

        return result

    def __str__(self):
        if not self._data:
            return "Empty SamTable"

        max_widths = {}
        for col in self._columns:
            max_widths[col] = max(
                len(str(col)), max(len(str(row.get(col, ""))) for row in self._data) if self._data else 0
            )

        lines = []
        header = " | ".join(str(col).ljust(max_widths[col]) for col in self._columns)
        lines.append(header)
        lines.append("-" * len(header))

        for row in self._data:
            line = " | ".join(str(row.get(col, "")).ljust(max_widths[col]) for col in self._columns)
            lines.append(line)

        return "\n".join(lines)

    def __repr__(self):
        return f"SamTable({len(self._data)} rows x {len(self._columns)} columns)"

    def head(self, n: int = 5):
        return PySimpleTable(
            data=self._data[:n], columns=self._columns, max_cells=self._max_cells, max_memory_mb=self._max_memory_mb
        )

    def tail(self, n: int = 5):
        return PySimpleTable(
            data=self._data[-n:], columns=self._columns, max_cells=self._max_cells, max_memory_mb=self._max_memory_mb
        )

    def info(self):
        info_lines = [
            f"SamTable Info:",
            f"Rows: {len(self._data)}",
            f"Columns: {len(self._columns)}",
            f"Total cells: {len(self._data) * len(self._columns)}",
            f"Memory limit: {self._max_memory_mb}MB" if self._max_memory_mb else "Memory limit: None",
            f"Cell limit: {self._max_cells}" if self._max_cells else "Cell limit: None",
            "",
            "Column Info:",
        ]

        for col in self._columns:
            values = [row.get(col) for row in self._data if row.get(col) is not None]
            if values:
                col_type = type(values[0]).__name__
                non_null = len(values)
            else:
                col_type = "unknown"
                non_null = 0

            info_lines.append(f"  {col}: {non_null} non-null, dtype: {col_type}")

        return "\n".join(info_lines)

    def _check_limits(self):
        """Check if current table exceeds memory or cell limits."""
        if self._max_cells and len(self._data) * len(self._columns) > self._max_cells:
            raise ValueError(f"Table exceeds maximum cells limit: {self._max_cells}")

        if self._max_memory_mb:
            # Rough memory estimation
            memory_mb = self._estimate_memory_mb()
            if memory_mb > self._max_memory_mb:
                raise ValueError(f"Table exceeds maximum memory limit: {self._max_memory_mb}MB")

    def _estimate_memory_mb(self) -> float:
        """Estimate memory usage in MB."""
        if not self._data:
            return 0.0

        # Sample a few rows to estimate average row size
        sample_size = min(10, len(self._data))
        sample_rows = self._data[:sample_size]

        total_bytes = 0
        for row in sample_rows:
            for value in row.values():
                total_bytes += sys.getsizeof(value)

        avg_row_bytes = total_bytes / sample_size
        total_estimated_bytes = avg_row_bytes * len(self._data)

        return total_estimated_bytes / (1024 * 1024)  # Convert to MB

    def set_memory_limit(self, max_memory_mb: Optional[float] = None, max_cells: Optional[int] = None):
        """Set memory and/or cell limits for the table.

        Args:
            max_memory_mb: Maximum memory usage in megabytes
            max_cells: Maximum number of cells (rows * columns)
        """
        if max_memory_mb is not None:
            self._max_memory_mb = max_memory_mb
        if max_cells is not None:
            self._max_cells = max_cells

        # Check current limits
        self._check_limits()

    def join(
        self,
        other: "PySimpleTable",
        on: Union[str, List[str]],
        how: str = "inner",
        suffixes: Tuple[str, str] = ("", "_y"),
    ) -> "PySimpleTable":
        """Join this table with another table.

        Args:
            other: Another SamTable to join with
            on: Column name(s) to join on
            how: Type of join ('inner', 'left', 'right', 'outer')
            suffixes: Suffixes for overlapping column names (left_suffix, right_suffix)

        Returns:
            New SamTable containing joined data
        """
        if isinstance(on, str):
            on = [on]

        # Validate join columns exist
        for col in on:
            if col not in self._columns:
                raise ValueError(f"Column '{col}' not found in left table")
            if col not in other._columns:
                raise ValueError(f"Column '{col}' not found in right table")

        # Prepare column names to avoid conflicts
        left_suffix, right_suffix = suffixes
        left_columns = self._columns[:]
        right_columns = []

        for col in other._columns:
            if col not in on and col in self._columns:
                right_columns.append(col + right_suffix)
            else:
                right_columns.append(col)

        # Build join key function
        def get_join_key(row, cols):
            return tuple(row.get(col) for col in cols)

        # Create lookup for right table
        right_lookup = defaultdict(list)
        for row in other._data:
            key = get_join_key(row, on)
            right_lookup[key].append(row)

        joined_data = []

        if how in ("inner", "left"):
            for left_row in self._data:
                left_key = get_join_key(left_row, on)
                right_matches = right_lookup.get(left_key, [])

                if right_matches:
                    for right_row in right_matches:
                        joined_row = left_row.copy()
                        for col in other._columns:
                            if col not in on:
                                new_col = col + right_suffix if col in self._columns else col
                                joined_row[new_col] = right_row[col]
                        joined_data.append(joined_row)
                elif how == "left":
                    # Add left row with None values for right columns
                    joined_row = left_row.copy()
                    for col in other._columns:
                        if col not in on:
                            new_col = col + right_suffix if col in self._columns else col
                            joined_row[new_col] = None
                    joined_data.append(joined_row)

        if how in ("right", "outer"):
            # Track which right rows were matched
            matched_right_keys = set()
            for left_row in self._data:
                left_key = get_join_key(left_row, on)
                if left_key in right_lookup:
                    matched_right_keys.add(left_key)

            # Add unmatched right rows
            for right_row in other._data:
                right_key = get_join_key(right_row, on)
                if right_key not in matched_right_keys:
                    joined_row = {}
                    # Add None values for left columns
                    for col in self._columns:
                        joined_row[col] = None
                    # Add right row values
                    for col in other._columns:
                        new_col = col + right_suffix if col in self._columns and col not in on else col
                        joined_row[new_col] = right_row[col]
                    joined_data.append(joined_row)

        # Determine final columns
        final_columns = left_columns[:]
        for col in right_columns:
            if col not in on and col not in final_columns:
                final_columns.append(col)

        result = PySimpleTable(
            data=joined_data, columns=final_columns, max_cells=self._max_cells, max_memory_mb=self._max_memory_mb
        )
        return result

    def paginate(self, page: int = 1, page_size: int = 20) -> "PySimpleTable":
        """Return a page of data from the table.

        Args:
            page: Page number (1-based)
            page_size: Number of rows per page

        Returns:
            SamTable containing the requested page of data
        """
        if page < 1:
            raise ValueError("Page number must be >= 1")
        if page_size < 1:
            raise ValueError("Page size must be >= 1")

        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size

        page_data = self._data[start_idx:end_idx]
        return PySimpleTable(
            data=page_data, columns=self._columns, max_cells=self._max_cells, max_memory_mb=self._max_memory_mb
        )

    def page_count(self, page_size: int = 20) -> int:
        """Get total number of pages for given page size.

        Args:
            page_size: Number of rows per page

        Returns:
            Total number of pages
        """
        if page_size < 1:
            raise ValueError("Page size must be >= 1")

        return (len(self._data) + page_size - 1) // page_size  # Ceiling division


if __name__ == "__main__":
    data = [
        {"name": "Alice", "age": 25, "city": "New York"},
        {"name": "Bob", "age": 30, "city": "San Francisco"},
        {"name": "Charlie", "age": 35, "city": "Los Angeles"},
    ]

    table = PySimpleTable(data, columns=["name", "age", "city"])
    t1 = table.filter(name={"in": "ch"}, ignore_case=True)
    print(t1)
