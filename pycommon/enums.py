"""
Enumrations
"""

from gettext import gettext as _, pgettext as _p

from optenum import Options


class DatabaseType(Options):
    MYSQL = "mysql", _p("option text", "database type - mysql")
    POSTGRESQL = "postgresql", _p("option text", "database type - postgresql")
    SQLITE = "sqlite3", _p("option text", "database type - sqlite")


class HTTPMethod(Options):
    GET = "GET"
    POST = "POST"
    DELETE = "DELETE"
    PUT = "PUT"
    OPTION = "OPTION"
    PATCH = "PATCH"
