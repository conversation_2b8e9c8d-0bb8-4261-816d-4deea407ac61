"""executable entrypoint for DEV purpose"""

import logging.config
import os
import sys

from dotenv import load_dotenv

from pyagent.sample_agents import HelloAgent, ChatPromptAgent

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

load_dotenv()
log_config = {
    "version": 1,
    "disable_existing_loggers": True,
    "level": os.getenv("LOG_LEVEL", "INFO"),
    "formatters": {
        "standard": {
            "format": "%(levelname).04s|%(asctime)s|%(name)s:%(lineno)d| %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
        "colorful": {
            # "format": "%(log_color)s[%(levelname).01s] [%(asctime)s] [%(processName).04s][%(threadName).016s][%(name)s:%(lineno)d] %(message)s",
            "format": "%(log_color)s[%(levelname).01s] [%(asctime)s] [%(processName).04s][%(name)s:%(lineno)d] %(message)s",
            "()": "colorlog.ColoredFormatter",
            "log_colors": {
                "DEBUG": os.getenv("LOG_COLOR_DEBUG", "thin_black"),
                "INFO": os.getenv("LOG_COLOR_INFO", "white"),
                "WARNING": "yellow",
                "ERROR": "red",
                "CRITICAL": "red,bg_white",
            },
            "secondary_log_colors": {},
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": os.getenv("LOG_FORMATTER", "standard"),
        },
    },
    "root": {"level": os.getenv("LOG_LEVEL", "INFO"), "handlers": ["console"]},
    "loggers": {
        **{name: {"level": "INFO"} for name in []},
        **{
            name: {"level": "WARNING"}
            for name in [
                "LiteLLM",
                "openai",
                "httpcore",
                "httpx",
                "pyrate_limiter",
                "asyncio",
                "botocore",
                "tzlocal",
                "urllib3",
            ]
        },
    },
}
logging.config.dictConfig(log_config)

logger = logging.getLogger(__name__)


def hello_agent():

    agent = HelloAgent()
    answer = agent.run()
    print(answer)


def agent_by_chat_prompts():
    agent = ChatPromptAgent()
    answer = agent.run()
    print(answer)


def main():
    """显示所有可用的开发函数并让用户选择运行"""
    functions = {
        "1": ("hello agent", hello_agent),
        "2": ("agent by ChatPrompt", agent_by_chat_prompts),
    }

    print("\n=== 开发函数菜单 ===")
    for key, (name, _) in functions.items():
        print(f"{key}. {name}")
    print("q. 退出")

    choice = input("\n请选择要运行的函数 (输入对应数字或q退出): ")

    if choice == "q":
        print("退出程序")
        return

    if choice in functions:
        print(f"\n运行: {functions[choice][0]}")
        functions[choice][1]()
    else:
        print("无效选择，请重新输入")
        main()


if __name__ == "__main__":
    main()
