# NovelAI

`novelai` is come from "novel" + "AI". Its the code name of the project.
This project is used to generate novel via AI.

## Features Roadmap

- [ ] Generate story outline
	- [ ] Generate Foreshadowing (Hint, Clue)
	- [ ]
- [ ] Design story background
- [ ] Design the story type & theme
- [ ] Generate main and secondary characters
- [ ] Generate

## How to use

- Run as web app: `python manage.py runserver <port>`
- Command line tool to conceive a story: `python manage.py novel story`
- Command line tool to design major characters for a story: `python manage.py novel characters --story=<story_id>`

