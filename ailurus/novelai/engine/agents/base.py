import os
import traceback
from abc import ABC
from datetime import datetime, UTC
from typing import Any

from asgiref.sync import sync_to_async
from mem0 import MemoryClient, AsyncMemoryClient
from pydantic import ConfigDict

from novelai.engine.models.author import Author, AUTHOR_DICT
from novelai.engine.models.base import AgentStat
from pyagent.base import LlmAgentConfig, LlmAgent
from pyagent.consts import LiteLlmModels
from pycommon.logwrapper import get_log_wrapper

logger = get_log_wrapper(__name__)


# ==========   Base Agents   ==========


class NovelWritingConfig(LlmAgentConfig, ABC):
    """base agent for all novel writing related agents"""

    llm_model: str = LiteLlmModels.DEFAULT
    llm_timeout: int = 60
    llm_temperature: float = 1.7  # 暂时所有的agent。理论上不同的agent，不同故事，用不同temperature

    author: Author | str
    novel_genres: str  # 小说体裁
    words_plan: int  # 计划字数. (中文,字数；英文:词数) planned word count, actually token count;
    novel_lang: str = "简体中文"  # 小说生成语言

    model_config = ConfigDict(arbitrary_types_allowed=True)

    def __init__(self, **kwargs: Any):
        author = kwargs.get("author")
        if author and isinstance(author, str):
            author = AUTHOR_DICT.get(author)
            if author:
                kwargs["author"] = author
        super().__init__(**kwargs)

    @property
    def sys_prompt(self) -> str:
        """system prompt for writing the novel by acting as the author"""
        return f"""{self.author.as_prompt}
在写作中，对于未确定的角色你喜欢使用<角色代码(code)>代替<角色名字(name)>，便于后期替换为正式名字。角色代码格式是使用'$['和']$'包围名字，例如: '$[张三]$', '$[ZhangSan]$', '$[路人1]$' 等
出版社约你写一本{self.novel_genres}类型的网络小说，因为极丰厚的报酬以及会带来非常大的影响力，你非常看重这本小说，因此进行了认真的构思。
"""


# ==========   Base Agent Configs   ==========


class NovelWritingAgent(LlmAgent, ABC):
    """base for novel writing related agents"""

    config: NovelWritingConfig
    run_id: str = datetime.now(tz=UTC).strftime("%Y%m%d-%H%M%S")
    mem_cli: MemoryClient = MemoryClient(api_key=os.getenv("MEM0AI_API_KEY"))
    mem_async_cli: AsyncMemoryClient = AsyncMemoryClient(api_key=os.getenv("MEM0AI_API_KEY"))

    def _save_state(self, **kwargs: Any) -> None:
        super()._save_state(**kwargs)
        ob = self.ob
        try:
            cfg_d = self.config.to_dict(exclude={"story"})
            if hasattr(self.config, "story"):
                cfg_d["story"] = self.config.story.name
            extra = kwargs.get("extra") or ob.check("extra") or {}
            extra["run_id"] = self.run_id
            AgentStat.objects.create(
                agent=f"{self.__class__.__name__}/{self.config.name}",
                config=cfg_d,
                perf=kwargs.get("perf") or ob.check("perf") or {},
                errors=kwargs.get("errors") or ob.check("errors") or {},
                llm_cost=kwargs.get("llm_cost") or ob.check("llm_cost") or {},
                llm_prompts=kwargs.get("llm_prompts") or ob.check("llm_prompts") or "",
                llm_outputs=kwargs.get("llm_output") or ob.check("llm_output") or "",
                extra_data=extra,
            )
        except Exception as e:
            logger.error(f"error saving agent state ({self.config.name}): {e}")
            logger.debug("".join(traceback.format_exception(e)))

    async def _async_save_state(self, **kwargs: Any) -> None:
        return await sync_to_async(self._save_state)(**kwargs)

    def _remember(self, messages: list[dict[str, str]], user_id: str = "sys") -> None:
        self.mem_cli.add(messages, agent_id=self.config.name, user_id=user_id, version="v2")

    def _forget(self, message_id: str) -> None:
        self.mem_cli.delete(message_id=message_id, version="v2")

    def _forget_user(self, user_id: str) -> None:
        if user_id == "sys":
            raise ValueError("can not forget system user")
        self.mem_cli.delete_users(agent_id=self.config.name, user_id=user_id, version="v2")

    async def _async_remember(self, messages: list[dict[str, str]], user_id: str = "sys") -> None:
        await self.mem_async_cli.add(messages, agent_id=self.config.name, user_id=user_id, version="v2")

    async def _async_forget(self, message_id: str) -> None:
        await self.mem_async_cli.delete(message_id=message_id, version="v2")

    async def _async_forget_user(self, user_id: str) -> None:
        if user_id == "sys":
            raise ValueError("can not forget system user")
        await self.mem_async_cli.delete_users(agent_id=self.config.name, user_id=user_id, version="v2")
