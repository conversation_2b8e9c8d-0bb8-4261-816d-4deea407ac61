- respond in Chinese
- NEVER drop a database/table. Only provide functional codes to do.
- If a file, module or package has `.ro.`(or `.RO.`) in name or has `<LLM_READONLY>` comment on top of it, it and its recursive children are read-only. Do not modify them.
- do not hardcode sensitive info in code. use env variables in `.env`. we use `load_dotenv()` to export them.
- use unique global config. do not configure everywhere
- 对于同样功能的 utils, helper 类似的类或函数，写一个并引用，不要到处写。如有修改，考虑兼容性。
- 尽量使用相对路径，或者使用常量/变量路径拼接，不要使用当前路径/文件来拼接路径。
- NEVER try to append workspace/project path into system path. User should declare PYTHONPATH.
- 不要立刻修改，先提出方案，然后询问，待用户手动确认后再开始。
- 在保证正确的情况下，尽可能采用最小化修改，不要随意大面积修改。注意多次检查，减少无用冗余代码。
