"""agent to conceive and design the outline of the novel"""

from typing import Any, As<PERSON><PERSON><PERSON><PERSON>

import pyllm
from novelai.engine.agents.base import Novel<PERSON><PERSON>ingConfig, NovelWritingAgent
from novelai.engine.agents.character import StoryCharacterAgentConfig, StoryCharacterAgent
from novelai.engine.agents.world import StoryWorldAgentConfig, StoryWorldAgent
from novelai.engine.models.character import StoryCharacter
from novelai.engine.models.outline import StoryOutline
from novelai.engine.models.story import Story
from novelai.engine.models.world import StoryWorld
from pycommon.logwrapper import get_log_wrapper

logger = get_log_wrapper(__name__)


class StoryOutlineAgentConfig(NovelWritingConfig):
    """config for StoryOutlineAgent"""

    name: str = "outline-agent"
    story: Story
    llm_timeout: int = 120  # override timeout for long context


class StoryOutlineAgent(NovelWritingAgent):
    """设计小说故事大纲的智能体。An agent to design the outline of a story for writing a novel"""

    config: StoryOutlineAgentConfig

    async def _async_run(self, **kwargs: Any) -> StoryOutline:
        story = self.config.story
        if not story:
            raise ValueError("Story must be conceived before designing outline")

        # 检查 story outline 是否已经完整
        outline: StoryOutline
        dirty: bool = False
        try:
            outline = await story.async_outline()
            if await outline.async_is_complete_outline():
                raise ValueError(f"Outline of {story} already exists and is complete")
        except StoryOutline.DoesNotExist:
            outline = StoryOutline(story=story)

        # 生成 框架 - 故事概览 (story_overview)
        if not outline.story_overview:
            overview_d = await self._async_gen_overview(**kwargs)
            logger.info(f"generated `outline.story_overview` for {story}")
            outline.story_overview = overview_d["story_overview"]
            dirty = True
        else:
            logger.debug(f"skip. {story} has `story_overview` already.")

        # 根据已有的 故事概览 生成 世界设定
        worlds = await self._async_gen_worlds(**kwargs)
        if len(worlds) > 0:
            logger.info(f"generated {len(worlds)} worlds ({[world.name for world in worlds]}) for {story}")

        # 根据已有的 故事概览、世界设定 生成 角色。
        characters = await self._async_gen_characters(**kwargs)
        if len(characters) > 0:
            logger.info(f"generated {len(characters)} characters ({[c.name for c in characters]}) for {story}")

        # 根据已有的 故事概览、世界设定、 角色，生成剩下的部分
        if not (
            outline.plot_framework
            and outline.chapter_planning
            and outline.themes_and_symbolism
            and outline.other_elements
            and outline.writing_advice
        ):
            d_rest = await self._async_gen_rest_outline(**kwargs)
            outline.plot_framework = d_rest.get("plot_framework") or outline.plot_framework
            outline.chapter_planning = d_rest.get("chapter_planning") or outline.chapter_planning
            outline.themes_and_symbolism = d_rest.get("themes_and_symbolism") or outline.themes_and_symbolism
            outline.other_elements = d_rest.get("other_elements") or outline.other_elements
            outline.writing_advice = d_rest.get("writing_advice") or outline.writing_advice
            logger.info(f"generated rest parts of `outline` for {story}")
            dirty = True

        if dirty:
            outline.tech_detail = {
                "llm_model": self.config.llm_model,
                "llm_temperature": self.config.llm_temperature,
                "llm_output_tokens": self.config.llm_output_tokens,
                "llm_timeout": self.config.llm_timeout,
            }
            await outline.asave()
            logger.info(f"outline of {story} saved")

        return outline

    async def _async_gen_overview(self, **kwargs: Any) -> dict:
        story = self.config.story
        if not story:
            raise ValueError("Story must be conceived before designing outline")

        # Create prompt for outline
        detail_prompt = f"""
#### 1. 故事概述(story_overview)
- 定义基调和风格（例如 [tone]，融入 [style]）。
- 明确目标读者（例如 [target_audience]）。
- 设置预计字数（约 [word_count] 字，分为 [chapter_count] 章，每章约 [words_per_chapter] 字）。
- 选择叙述视角（例如 [perspective]）。
- 概述主要冲突：
  - 外部：主角面对 [external_conflict]。
  - 内部：主角挣扎于 [internal_conflict]。
- 提供5-8句话的故事概述，涵盖开端、中段和结局。
"""
        format_prompt = f"""- 以详细的JSON文档呈现提纲。
- 使用符合 {story.genres} 氛围的命名和术语。
- 平衡动作、情感和世界观，体现 {story.genres} 的精神。"""
        user_prompt = (
            f"# 故事设定\n{story.as_prompt()}\n\n"
            f"# 提纲 - 故事概述生成\n为上面故事设定，生成提纲第1部分-故事概述。需结构清晰、内容全面，明确所面向读者群体[target_audience]，包含以下要素：\n{detail_prompt}\n\n"
            f"# 格式\n{format_prompt}\n\n"
            "输出如下：\n"
        )

        # Generate character profiles using LLM
        prompts = (
            pyllm.ChatPrompts(
                output_format="json",
                output_schema=StoryOutline.as_schema(includes=["story_overview"]),
                output_lang=self.config.novel_lang,
            )
            .set_system(self.config.sys_prompt)
            .set_inquery(user_prompt)
        )
        answer = await self.async_invoke_llm(prompt=prompts.to_list(), resp_type="object")
        return answer

    async def _async_gen_worlds(self, **kwargs) -> list[StoryWorld]:
        story = self.config.story
        config = StoryWorldAgentConfig(
            name=f"world-agent-{story.name}",
            story=story,
            author=story.author,
            novel_genres=story.genres,
            words_plan=story.words_plan,
            novel_lang=story.lang,
        )
        agent = StoryWorldAgent(config=config)
        agent.run_id = self.run_id  # copy run_id to identify a series runs
        worlds = await agent.async_run(**kwargs)
        return worlds

    async def _async_gen_characters(self, **kwargs: Any) -> list[StoryCharacter]:
        story = self.config.story
        config = StoryCharacterAgentConfig(
            name=f"chapter-agent-{story.name}",
            story=story,
            author=story.author,
            novel_genres=story.genres,
            words_plan=story.words_plan,
            novel_lang=story.lang,
        )
        agent = StoryCharacterAgent(config=config)
        agent.run_id = self.run_id  # copy run_id to identify a series runs
        characters = await agent.async_run(**kwargs)
        return characters

    async def _async_gen_rest_outline(self, **kwargs: Any) -> dict:
        story = self.config.story
        if not story:
            raise ValueError("Story must be conceived before designing outline")

        outline = await story.async_outline()
        outline_prompt = outline.as_prompt(includes=["story_overview"]) if outline else "无"

        # worlds prompts
        worlds = await story.async_get_worlds()
        worlds_prompts = "\n".join([f"## {w.name}\n{w.as_prompt(excludes={'story', 'name'})}" for w in worlds])

        characters = await story.async_get_characters()
        if self.config.only_if_empty and len(characters) > 0:
            logger.warning(
                f"skip. {story} has {len(characters)} characters already. "
                "Set `StoryCharacterAgentConfig.only_if_empty=False` to create extra characters."
            )
            return []

        # Create prompt for existed characters
        if len(characters) > 0:
            logger.debug(f"{story} has {len(characters)} characters. generate extra characters.")
            char_prompt = "\n".join(
                [f"## {c.name}\n{c.as_prompt(includes=['code', 'level', 'bio'])}" for c in characters]
            )
        else:
            char_prompt = "无"

        # Create prompt for outline
        detail_prompt = f"""
#### 4. 情节框架(plot_framework)
- 根据小说长度({story.length_of_story.cn}, {story.length_of_story.min_words}到{story.length_of_story.max_words}字) 确定有多少卷/幕(volumes, epics, acts)
- 各幕计划多少章节，多少字数。
- 定义各幕的：目标(objective),关键事件(events),激发事件或者转折点(???)，情感基调

#### 5. 章节规划(chapter_planning)
- 提供前10章的详细规划（每章约 [words_per_chapter] 字），包括：
  - 场景/setting。
  - 关键事件。
  - 涉及人物。
  - 后续剧情的伏笔。
- 概述后续章节的发展，每 [small_arc_chapters] 章为一个小型故事弧，每 [major_arc_chapters] 章为一个重大转折，确保节奏均衡。

#### 6. 主题与象征(themes_and_symbolism)
- 明确核心主题。
- 突出象征元素。
- 概述情感主线。

#### 7. 其他要素(other_elements)
- 列出关键伏笔和线索。
- 建议节奏控制（例如，每 [minor_climax_chapters] 章一个小高潮，每 [major_climax_chapters] 章一个大高潮）。
- 强调类型特定特征：
  - **类型特定描述**：（例如 [description_type]）。
  - **类型特定情节**：（例如 [plot_type]）。
  - **类型特定情感**：（例如 [emotion_type]）。
- 推荐写作风格：[style_description]。

#### 8. 写作建议(writing_advice)
- 提供准备建议（例如 [preparation_content]）。
- 建议节奏策略（例如 [pacing_strategy]）。
- 推荐灵活性（例如 [flexibility_advice]）。
- 考虑读者期待（例如 [reader_expectations]）。
"""
        format_prompt = f"""- 以详细的JSON文档呈现提纲。
- 确保提纲全面、灵活且分段清晰。
- 使用符合 {story.genres} 氛围的命名和术语。
- 平衡动作、情感和世界观，体现 {story.genres} 的精神。"""
        user_prompt = (
            f"# 故事设定\n{story.as_prompt()}\n\n"
            f"# 框架设计 - 故事概览\n{detail_prompt}\n\n"
            f"# 世界设定\n{worlds_prompts}\n\n"
            f"# 已创建的人物(不要再创建)\n{char_prompt}\n\n"
            f"# 提纲生成\n为上面故事设定，生成一份详细小说提纲（剩余部分）。需结构清晰、内容全面，面向 [target_audience]，包含以下要素：\n{detail_prompt}\n\n"
            f"# 格式\n{format_prompt}\n\n"
            "输出如下：\n"
        )

        # Generate character profiles using LLM
        prompts = (
            pyllm.ChatPrompts(
                output_format="json",
                output_schema=StoryOutline.as_schema(excludes=["story_overview"]),
                output_lang=self.config.novel_lang,
            )
            .set_system(self.config.sys_prompt)
            .set_inquery(user_prompt)
        )
        answer = await self.async_invoke_llm(prompt=prompts.to_list(), resp_type="object")
        return answer

    async def _async_iter(self, **kwargs: Any) -> AsyncGenerator:
        raise NotImplementedError
