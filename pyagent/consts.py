"""Constants, enums, Literal(or Literal class) and etc for Agent module"""

import os
from enum import StrEnum


class LiteLlmModels(StrEnum):
    # openai
    GPT_4O = "gpt-4o"
    GPT_4O_MINI = "gpt-4o-mini"
    GPT_O4 = "o4"
    GPT_O4_MINI = "o4-mini"

    # bedrock / antheropic
    CLAUDE_4_OPUS = "bedrock/us.anthropic.claude-opus-4-20250514-v1:0"
    CLAUDE_4_SONNET = "bedrock/us.anthropic.claude-sonnet-4-20250514-v1:0"

    # Google
    GEMINI_20_FLASH_LITE = "gemini/gemini-2.0-flash-lite"
    GEMINI_25_FLASH_LITE = "gemini/gemini-2.5-flash-lite-preview-06-17"
    GEMINI_25_FLASH = "gemini/gemini-2.5-flash"

    # grok
    GROK_V2 = "xai/grok-2-latest"
    GROK_V3 = "xai/grok-3"
    GROK_V3_MINI = "xai/grok-3-mini"
    GROK_V3_FAST = "xai/grok-3-fast"
    GROK_V3_MINI_FAST = "xai/grok-3-min-fast"

    # deepseek
    DEEPSEEK_V3 = "deepseek/deepseek-chat"
    DEEPSEEK_R1 = "deepseek/deepseek-reasoner"

    # ollama
    OLLAMA_LLAMA_2_UNCENSORED = "ollama/llama2-uncensored"
    OLLAMA_LLAMA_32_3B = "ollama/llama3.2:3b"
    OLLAMA_LLAMA_32_VISION = "ollama/llama3.2-vision"
    OLLAMA_DEEPSEEK_R1_8B = "ollama/deepseek-r1:8b"

    # default
    DEFAULT = os.getenv("LLM_MODEL") or GPT_4O_MINI
