from collections import defaultdict
from multiprocessing import Lock


class NamedLocker:
    def __init__(self) -> None:
        # each client process is served by a seperate thread,
        # this lock is used to serialize access to our state
        self.lock = Lock()
        self.locks: defaultdict = defaultdict(Lock)

    def acquire(self, name: str) -> None:
        with self.lock:
            lock = self.locks[name]
        lock.acquire()

    def release(self, name: str) -> None:
        with self.lock:
            lock = self.locks[name]
        lock.release()

    def use(self, name: str) -> None:
        return self.locks[name]


#
# class NamedLockerProxy(BaseProxy):
#     _exposed_ = ("acquire", "release")
#
#     def acquire(self, name: str) -> None:
#         return self._callmethod("acquire", (name,))
#
#     def release(self, name: str) -> None:
#         return self._callmethod("release", (name,))
#
#     @contextmanager
#     def use(self, name: str) -> None:
#         self.acquire(name)
#         try:
#             yield None
#         finally:
#             self.release(name)
#
#
# class LockerManager(SyncManager):
#     pass
#
#
# LockerManager.register("NamedLocker", NamedLocker)
