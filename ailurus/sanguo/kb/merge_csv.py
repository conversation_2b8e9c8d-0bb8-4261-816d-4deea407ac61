#!/usr/bin/env python3
"""
CSV合并脚本
将people-100.csv到people-1000.csv的内容合并生成people.csv

# 生成unique 名字列表
cat people.csv|cut -d , -f 2|xargs
"""

import csv
import glob
import os
import sys

persons = {}


def merge_csv_files():
    """合并CSV文件"""
    # 查找所有匹配的CSV文件
    pattern = "people-*.csv"
    csv_files = glob.glob(pattern)

    # 过滤出符合条件的文件（people-100.csv到people-1000.csv）
    valid_files = []
    for file in csv_files:
        # 提取文件名中的数字
        try:
            filename = os.path.basename(file)
            if filename.startswith("people-") and filename.endswith(".csv"):
                number_part = filename[7:-4]  # 去掉"people-"和".csv"
                number = int(number_part)
                if 100 <= number <= 1000:
                    valid_files.append((file, number))
        except ValueError:
            continue

    if not valid_files:
        print("未找到符合条件的CSV文件 (people-100.csv ~ people-1000.csv)")
        return False

    # 按数字排序
    valid_files.sort(key=lambda x: x[1])

    print(f"找到 {len(valid_files)} 个CSV文件:")
    for file, number in valid_files:
        print(f"  - {file}")

    output_file = "people.csv"
    header_written = False
    total_rows = 0

    try:
        with open(output_file, "w", newline="", encoding="utf-8") as outfile:
            writer = None

            for file_path, _ in valid_files:
                print(f"正在处理: {file_path}")

                try:
                    with open(file_path, "r", encoding="utf-8") as infile:
                        reader = csv.reader(infile)

                        # 读取第一行（通常是标题行）
                        try:
                            first_row = next(reader)
                        except StopIteration:
                            print(f"  警告: {file_path} 是空文件，跳过")
                            continue

                        # 如果还没有写入标题行，则写入
                        if not header_written:
                            writer = csv.writer(outfile)
                            writer.writerow(first_row)
                            header_written = True
                            print(f"  写入标题行: {first_row}")

                        # 写入数据行
                        file_rows = 0
                        dup_rows = 0
                        for row in reader:
                            if row:  # 跳过空行
                                name = row[1]
                                if name not in persons:
                                    row[0] = str(len(persons) + 1)
                                    persons[name] = row
                                    writer.writerow(row)
                                    file_rows += 1
                                else:
                                    dup_rows += 1
                                    existed = persons[name]
                                    print(f"existed: {existed}")
                                    print(f"    new: {row}")

                        total_rows += file_rows
                        print(f"  处理了 {file_rows} 行数据, 重复 {dup_rows} 行。")

                except FileNotFoundError:
                    print(f"  错误: 文件 {file_path} 不存在")
                except UnicodeDecodeError:
                    print(f"  错误: 文件 {file_path} 编码问题，尝试使用其他编码")
                    # try:
                    #     with open(file_path, "r", encoding="gbk") as infile:
                    #         reader = csv.reader(infile)
                    #         if not header_written:
                    #             first_row = next(reader)
                    #             writer = csv.writer(outfile)
                    #             writer.writerow(first_row)
                    #             header_written = True
                    #
                    #         file_rows = 0
                    #         for row in reader:
                    #             if row:
                    #                 writer.writerow(row)
                    #                 file_rows += 1
                    #         total_rows += file_rows
                    #         print(f"  使用GBK编码处理了 {file_rows} 行数据")
                    # except Exception as e:
                    #     print(f"  错误: 无法处理文件 {file_path}: {e}")
                except Exception as e:
                    print(f"  错误: 处理文件 {file_path} 时出错: {e}")

        print(f"\n合并完成!")
        print(f"输出文件: {output_file}")
        print(f"总共合并了 {total_rows} 行数据")
        return True

    except Exception as e:
        print(f"错误: 创建输出文件时出错: {e}")
        return False


def main():
    """主函数"""
    print("CSV文件合并工具")
    print("=" * 50)

    # 检查当前目录
    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")

    # 执行合并
    success = merge_csv_files()

    if success:
        print("\n✅ 合并成功完成!")
    else:
        print("\n❌ 合并失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
