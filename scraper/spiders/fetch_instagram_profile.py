#!/usr/bin/env python3
import asyncio
import json
import random
import time
import traceback
import uuid
from logging import getLogger
from typing import Any, Callable
from urllib.parse import urlencode

import requests
from django.core.cache import caches

from scraper.my_models import InstagramProfile
from services import slack
from ..login_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, SessionManager

# import sys
# from pathlib import Path
# sys.path.insert(0, str(Path(__file__).absolute().parent.parent.parent.parent))
# from django_init import init_django
# init_django()

logger = getLogger(__name__)
cache = caches["default"]


async def async_cache_get_or_set(key: str, func: Callable, *args: Any, **kwargs: Any) -> str:
    value = await cache.aget(key)
    if not value:
        if asyncio.iscoroutinefunction(func):
            value = await func(*args, **kwargs)
        else:
            value = func(*args, **kwargs)
        await cache.aset(key, value, timeout=3600 * 24)
    else:
        logger.warning(f"ig api cache hit - {key}")
    return value


pigeonSessionIdLifetime = 1200000


def _get_user_device_info() -> dict:
    # file_path = (
    #     Path(__file__).parent.parent / "ig-api-headers" / "instagram-headers.3.json"
    # )
    # with open(file_path, "r") as f:
    #     return json.load(f)
    # cookie
    session = SessionManager.get_instance()
    mgr_name = HeaderManager.get_manager_name()
    platform = "instagram"
    data = session.current_set_data(platform)
    headers = data[mgr_name]
    logger.debug(f"loaded {platform} {mgr_name} (set={session.current_set(platform)}) - {headers}")
    return headers


def _generate_bandwidth_headers() -> dict:
    speed_kbps = random.uniform(15000, 30000)
    # 10MB to 30MB
    total_bytes = random.randint(10000000, 30000000)
    # Calculate a realistic time
    total_time_ms = int(total_bytes * 8 / speed_kbps)

    return {
        "X-Ig-Bandwidth-Speed-Kbps": f"{speed_kbps:.3f}",
        "X-Ig-Bandwidth-Totalbytes-B": str(total_bytes),
        "X-Ig-Bandwidth-Totaltime-Ms": str(total_time_ms),
    }


def _generateTemporaryGuid(seed: str, device_id: str, lifetime: int) -> str:
    current_time = int(time.time() * 1000)
    unique_string = f"{seed}{device_id}{current_time // lifetime}"
    return str(uuid.uuid5(uuid.NAMESPACE_DNS, unique_string)) + "-0"


def _get_ig_r_rur(device_info: dict) -> str:
    try:
        with open(f'/tmp/autogather-ig-header-ig-r-rur-{device_info["user_id"]}', "r") as f:
            str = f.read()
            logger.debug(f"get ig-r-rur: {str}")
            return str
    except FileNotFoundError:
        return "NHA"
    except Exception as e:
        logger.error(f"Error reading ig-r-rur: {e}")
        return "NHA"


def _set_ig_r_rur(device_info: dict, value: str) -> int:
    logger.debug(f"set ig-r-rur to {value}")
    with open(f'/tmp/autogather-ig-header-ig-r-rur-{device_info["user_id"]}', "w") as f:
        return f.write(value)


def _pigeonSessionId(device_info: dict) -> str:
    device_id = device_info["global_headers"]["X-Ig-Device-Id"]
    return "UFS-" + _generateTemporaryGuid("pigeonSessionId", device_id, pigeonSessionIdLifetime)


async def async_fetch_user_info(user_id: str, use_cache: bool = True) -> dict:
    url = f"https://i.instagram.com/api/v1/users/{user_id}/info_stream/"

    async def __invoke() -> str:
        device_info = _get_user_device_info()
        form_data = {
            "entry_point": "profile",
            "from_module": "self_profile",
            "_uuid": uuid.uuid4(),
        }

        form_data_str = urlencode(form_data)
        global_headers: dict = device_info["global_headers"]
        static_headers: dict = device_info["fetch_user_info"]["headers"]

        headers = {
            **global_headers,
            **static_headers,
            **_generate_bandwidth_headers(),
            "X-Pigeon-Session-Id": _pigeonSessionId(device_info),
            "X-Pigeon-Rawclienttime": f"{time.time():.3f}",
            "X-Ig-Nav-Chain": f"MainFeedFragment:feed_timeline:1:cold_start:1737019396.339:::1737019431.478,UserDetailFragment:profile:6:suggested_users:1737019448.694:::{time.time():.3f}",
            "Ig-U-Rur": _get_ig_r_rur(device_info),
            "Content-Length": f"{len(form_data_str)}",
        }
        response = requests.post(url, headers=headers, data=urlencode(form_data))
        response.raise_for_status()
        _set_ig_r_rur(device_info, response.headers["ig-set-ig-u-rur"])
        return json.dumps(
            {
                "text": response.text.strip(),
                "headers": {"ig-set-ig-u-rur": response.headers["ig-set-ig-u-rur"]},
            }
        )

    try:
        cache_key = f"ig:fetch_user_info:{user_id}"
        if use_cache:
            s = await async_cache_get_or_set(cache_key, __invoke)
        else:
            s = await __invoke()
        s_json = json.loads(s)
        json_strs = s_json.get("text", "").split("\n")
        # assert len(json_strs) == 2

        try:
            # short_profile = json.loads(json_strs[0])
            # long_profile = json.loads(json_strs[1])

            # should be profile, in case of only 1, we use last
            obj = json.loads(json_strs[-1])
            if obj and "user" in obj:
                return obj["user"] or {}
        except json.JSONDecodeError as e:
            logger.error({"error": "JSON Parse Error", "rawResponse": s, "detail": str(e)})
    except requests.exceptions.RequestException as e:
        logger.error({"error": f"Request Error: {str(e)}"})
        handle_error(e)
    return {}


def instagram_id_to_shortcode(media_id: int) -> str:
    alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"
    shortcode = ""

    while media_id > 0:
        remainder = media_id % 64
        media_id //= 64
        shortcode = alphabet[remainder] + shortcode

    return shortcode


async def async_get_profile_posts(user_id: str | int, use_cache: bool = True) -> list[dict[str, Any]]:
    api_url = f"https://i.instagram.com/api/v1/feed/user/{user_id}/"
    params = {
        "exclude_comment": "false",
        "should_delay_media_metadata_fetch": "false",
    }

    async def __invoke() -> str:
        device_info = _get_user_device_info()
        global_headers: dict = device_info["global_headers"]
        static_headers: dict = device_info["fetch_user_info"]["headers"]
        headers = {
            **global_headers,
            **static_headers,
            **_generate_bandwidth_headers(),
            "X-Pigeon-Session-Id": _pigeonSessionId(device_info),
            "X-Pigeon-Rawclienttime": f"{time.time():.3f}",
            "X-Ig-Nav-Chain": f"MainFeedFragment:feed_timeline:1:cold_start:1736926439.904:::{time.time():.3f}::",
            "Ig-U-Rur": _get_ig_r_rur(device_info),
        }

        response = requests.get(api_url, headers=headers, params=params)
        response.raise_for_status()
        _set_ig_r_rur(device_info, response.headers["ig-set-ig-u-rur"])
        return json.dumps(
            {
                "text": response.text.strip(),
                "headers": {"ig-set-ig-u-rur": response.headers["ig-set-ig-u-rur"]},
            }
        )

    try:
        cache_key = f"ig:get_profile_posts:{user_id}"
        if use_cache:
            s = await async_cache_get_or_set(cache_key, __invoke)
        else:
            s = await __invoke()
        s_json = json.loads(s)

        data = json.loads(s_json.get("text", ""))
        handle_error(data)

        posts = [
            {
                "id": post.get("id"),
                "pk": post.get("pk"),
                # We don't have a direct "detail_url" in p,
                # so we build a best-guess URL from the pk
                "url": f"https://www.instagram.com/p/{instagram_id_to_shortcode(post.get('pk'))}/",
                "caption": (post.get("caption") or {}).get("text", ""),
                "taken_at": post.get("taken_at"),
                "like_count": post.get("like_count") or 0,
                "reshare_count": post.get("reshare_count") or 0,
                "play_count": post.get("play_count") or 0,
                "comment_count": post.get("comment_count") or 0,
            }
            for post in data.get("items", [])
        ]
        return posts
    except requests.RequestException as e:
        logger.error(f"Error fetching profile posts: {e}")
        handle_error(e)
        return []


async def async_fetch_userid_by_username(username: str, use_cache: bool = True) -> str:  # noqa: C901
    url = "https://i.instagram.com/api/v1/fbsearch/account_serp/"
    params = {
        "search_surface": "user_serp",
        "timezone_offset": "28800",
        "count": "30",
        "query": username,
    }

    async def __invoke() -> str:
        device_info = _get_user_device_info()
        global_headers: dict = device_info["global_headers"]
        static_headers: dict = device_info["fetch_user_info"]["headers"]
        headers = {
            **global_headers,
            **static_headers,
            **_generate_bandwidth_headers(),
            "X-Pigeon-Session-Id": _pigeonSessionId(device_info),
            "X-Pigeon-Rawclienttime": f"{time.time():.3f}",
            "X-Ig-Nav-Chain": f"ExploreFragment:explore_popular:6:main_search:**********.523:::**********.558,SingleSearchTypeaheadTabFragment:search_typeahead:15:button:**********.663:::{time.time():.3f}",
            "Ig-U-Rur": _get_ig_r_rur(device_info),
        }

        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        _set_ig_r_rur(device_info, response.headers["ig-set-ig-u-rur"])
        return json.dumps(
            {
                "text": response.text.strip(),
                "headers": {"ig-set-ig-u-rur": response.headers["ig-set-ig-u-rur"]},
            }
        )

    try:
        cache_key = f"ig:fetch_userid_by_username:{username}"
        if use_cache:
            s = await async_cache_get_or_set(cache_key, __invoke)
        else:
            s = await __invoke()
        s_json = json.loads(s)
        json_objects = s_json.get("text", "").split("\n")

        found_user = None
        for i, json_str in enumerate(json_objects):
            try:
                json_data = json.loads(json_str)
                handle_error(json_data)
                # Iterate through all users to find exact username match
                users = json_data.get("users", [])
                for user in users:
                    if user.get("username") == username:
                        found_user = user
                        break
            except json.JSONDecodeError as e:
                logger.debug(f"Error decoding JSON object {i}: {e}")

            if found_user:
                return found_user.get("id")
        return ""

    except requests.exceptions.RequestException as e:
        logger.error(f"error: {e}")
        handle_error(e)
        return ""


async def async_fetch_profile(username: str, use_cache: bool = True) -> InstagramProfile:
    """crawl instagram profile by fetch internal API"""

    user_id = await async_fetch_userid_by_username(username, use_cache=use_cache)
    profile = {}
    if user_id:
        p = await async_fetch_user_info(user_id, use_cache=use_cache)
        if p:
            profile = {
                "handle": username,
                "full_name": p.get("full_name") or "",
                "followers": p.get("follower_count") or 0,
                "following": p.get("following_count") or 0,
                "posts_count": p.get("media_count") or 0,
                "biography": p.get("biography"),
                "home_page": f"https://www.instagram.com/{username}",
                # TODO(ruiwang): consolidate url and home_page and make return value structured
                "url": f"https://www.instagram.com/{username}",
                "avatar": p.get("profile_pic_url") or "",
                "html": "",
                "email": p.get("public_email") or "",
            }
            posts = await async_get_profile_posts(user_id, use_cache=use_cache)
            profile["posts"] = posts
            profile["platform"] = "instagram"

    return profile


def handle_error(json_obj_or_err: dict | Exception) -> None:
    # suspected by automated behavior
    # [
    #     {
    #         "message": "challenge_required",
    #         "challenge": {
    #             "url": "https://i.instagram.com/challenge/?next=/api/v1/users/646713657/info_stream/",
    #             "api_path": "/challenge/",
    #             "hide_webview_header": True,
    #             "lock": True,
    #             "logout": False,
    #             "native_flow": True,
    #             "flow_render_type": 0,
    #         },
    #         "status": "fail",
    #     }
    # ]

    stack = traceback.extract_stack(limit=2)[0]
    loc = f"{stack.filename}:{stack.lineno}"

    if isinstance(json_obj_or_err, Exception):
        msg = str(json_obj_or_err)
    elif json_obj_or_err.get("status") == "fail":
        msg = json.dumps(json_obj_or_err, default=str)
    else:
        return
    msg = f"fail {loc} - {msg}"
    slack.send_alert(msg, channel=slack.ENGINEERING_CHANNEL)

    session = SessionManager.get_instance()
    platform = "instagram"
    if session.next(platform):
        logger.warning(f"{platform} headers rotated. (set={session.current_set(platform)}")


if __name__ == "__main__":

    async def run() -> None:
        # user = await async_fetch_user_info("58642837633")
        # print(user)
        # posts = await async_get_profile_posts("58642837633")
        # print(posts)
        #
        # user_id = await async_fetch_userid_by_username("c")
        # print("user_id", user_id)

        # profile = await async_fetch_profile("beauty.maven")
        # print(json.dumps(profile))

        pass

    asyncio.run(run())

    pass
