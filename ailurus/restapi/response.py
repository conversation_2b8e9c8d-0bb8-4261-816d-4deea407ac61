import copy
from typing import Optional, <PERSON><PERSON>lias, Any
from typing import Self

from agent.restapi.v1.schema import BaseFilterSchema
from asgiref.sync import sync_to_async
from django.db.models import QuerySet
from django.utils.functional import Promise
from django.utils.translation import pgettext_lazy
from ninja import Schema
from pydantic import Field


def _(message: str) -> Promise:
    return pgettext_lazy("api_resp", message)


class PaginationSchema(BaseFilterSchema):
    page: Optional[int] = Field(1, gt=0)
    page_size: Optional[int] = Field(20, gt=0)

    _count: int = -1

    @property
    def count(self) -> int:
        return self._count

    async def set_count(self, queryset: QuerySet) -> None:
        self._count = await sync_to_async(queryset.count)()

    async def filter(self, queryset: QuerySet) -> QuerySet:
        await self.set_count(queryset)
        offset = (self.page - 1) * self.page_size  # type: ignore
        qs = queryset[offset : offset + self.page_size]  # type: ignore # noqa: E203
        return qs


class ApiRespCode:
    # common
    SUCCESS = 0
    FAIL = 1
    NOT_IMPLEMENTED = 2

    # http match
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    INTERNAL_ERROR = 500

    # forbidden
    TRIAL_EXPIRED = 4030
    SUBSCRIPTION_EXPIRED = 4031


class ApiRespPagination(Schema):
    page: int = 0
    page_size: int = 0
    total: int = 0


class ApiResponse(Schema):
    code: int = ApiRespCode.SUCCESS
    message: Optional[str | Promise] = ""
    data: Any = None  # the data to return
    detail: str = ""  # extra info if required such as track_back, detailed error. etc.
    pagination: Optional[ApiRespPagination] = None

    def __call__(self, data: Any = None, detail: str = "") -> Self:
        r = copy.deepcopy(self)
        r.data = data
        r.detail = detail
        r.pagination = None
        return r

    async def with_pagination(self, schema: PaginationSchema) -> Self:
        pagination = ApiRespPagination()
        pagination.page = schema.page  # type: ignore
        pagination.page_size = schema.page_size  # type: ignore
        pagination.total = schema.count
        self.pagination = pagination
        return self


R: TypeAlias = ApiResponse


class ApiResults:
    # common result
    SUCCESS: R = R(code=ApiRespCode.SUCCESS, message=_("Succeed"))
    FAIL: R = R(code=ApiRespCode.FAIL, message=_("Failed"))
    NOT_IMPLEMENTED: R = R(code=ApiRespCode.NOT_IMPLEMENTED, message=_("Not implemented"))

    # match HTTP errors
    BAD_REQUEST: R = R(code=ApiRespCode.BAD_REQUEST, message=_("Bad request"))
    UNAUTHORIZED: R = R(code=ApiRespCode.UNAUTHORIZED, message=_("Unauthorized"))
    FORBIDDEN: R = R(code=ApiRespCode.FORBIDDEN, message=_("Forbidden"))
    NOT_FOUND: R = R(code=ApiRespCode.NOT_FOUND, message=_("Not found"))
    INTERNAL_ERROR: R = R(code=ApiRespCode.INTERNAL_ERROR, message=_("Internal error"))

    # forbidden
    TRIAL_EXPIRED: R = R(code=ApiRespCode.TRIAL_EXPIRED, message=_("Trial Expired"))
    SUBSCRIPTION_EXPIRED: R = R(code=ApiRespCode.SUBSCRIPTION_EXPIRED, message=_("Subscription Expired"))
