import asyncio
from abc import abstractmethod
from logging import get<PERSON>ogger
from typing import runtime_checkable, Protocol, Any, Callable, Self

from asgiref.sync import async_to_sync

from .utils.lang import get_caller_loc

logger = getLogger(__name__)


@runtime_checkable
class StopAble(Protocol):
    """interface of a class that can stop itself for long-running operation"""

    @abstractmethod
    def stop(self) -> None:
        """Synchronously stops the operation"""

    @abstractmethod
    async def async_stop(self) -> None:
        """Asynchronously stops the operation"""

    @abstractmethod
    def shall_stop(self, for_obj: Any = None, stop_if_true: bool = True, loc: str = "") -> bool:
        """Synchronously checks if the operation should stop

        Args:
            :param for_obj: Optional object to check stop condition against
            :param stop_if_true: if True, stop the operation if this is going to return True
            :param loc: if not empty, use this loc (line of code)

        Returns:
            bool: True if operation should stop, False otherwise
        """

    @abstractmethod
    async def async_shall_stop(self, for_obj: Any = None, stop_if_true: bool = True, loc: str = "") -> bool:
        """Asynchronously checks if the operation should stop

        Args:
            :param for_obj: Optional object to check stop condition against
            :param stop_if_true: if True, stop the operation if this is going to return True
            :param loc: if not empty, use this loc (line of code)

        Returns:
            bool: True if operation should stop, False otherwise

        """

    @property
    @abstractmethod
    def is_stopped(self) -> bool:
        """Indicates whether the operation is currently stopped

        Returns:
            bool: True if stopped, False otherwise
        """


class StopAbleMixin(StopAble):
    """A mixin class that provides stop functionality for long-running operations"""

    stop_event: asyncio.Event

    stop_callback: Callable[[Self, Any], bool] | None = None

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        event = kwargs.pop("stop_event", None)
        if event and isinstance(event, asyncio.Event):
            self.stop_event = event
        else:
            self.stop_event = asyncio.Event()
        super().__init__(*args, **kwargs)

    def stop(self) -> None:
        self.stop_event.set()

    async def async_stop(self) -> None:
        self.stop_event.set()

    @property
    def is_stopped(self) -> bool:
        return self.stop_event.is_set()

    def shall_stop(self, for_obj: Any = None, stop_if_true: bool = True, loc: str = "") -> bool:
        return async_to_sync(self.async_shall_stop)(for_obj=for_obj, stop_if_true=stop_if_true, loc=get_caller_loc())

    async def async_shall_stop(self, for_obj: Any = None, stop_if_true: bool = True, loc: str = "") -> bool:
        get_loc = lambda: loc if loc else get_caller_loc
        if self.is_stopped:
            logger.info(f"operation was stopped. check from {get_loc()}")
            return True

        # Check for stop_callback directly on the object
        if self.stop_callback:
            callback = self.stop_callback
            if asyncio.iscoroutinefunction(callback):
                shall_stop = await callback(self, for_obj)
            else:
                shall_stop = callback(self, for_obj)
            if shall_stop:
                logger.info(f"stop_callback returns True. check from {get_loc()}")
                if stop_if_true:
                    logger.info(f"stop operation due to `stop_if_true=True`")
                    self.stop()
                return True

        return False


@runtime_checkable
class ProgressAble(Protocol):
    """interface of a class that can report progress for long operation"""

    @property
    @abstractmethod
    def progress(self) -> tuple[int, int]:
        """Reports the current progress of the operation

        Returns:
            tuple[int, int]: A tuple of (current_count, total_count)
        """

    @abstractmethod
    def progress_inc(self, amount: int = 1, step: int = 1) -> None:
        """Synchronously increases the progress counter

        Args:
            amount: Number of units to increase by (default: 1)
            step: Multiplier for each unit (default: 1)
        """

    @abstractmethod
    def progress_dec(self, amount: int = 1, step: int = 1) -> None:
        """Synchronously decreases the progress counter

        Args:
            amount: Number of units to decrease by (default: 1)
            step: Multiplier for each unit (default: 1)
        """


class ProgressAbleMixin(ProgressAble):
    """A mixin class that provides progress reporting functionality for long-running operations"""

    progress_current: int = 0
    progress_total: int = 100

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        total = kwargs.pop("progress_total", None) or self.progress_total
        if isinstance(total, int):
            assert total > 0, f"progress_total must be int and greater than 0. you specified {total}"
            self.progress_total = total
        else:
            logger.warning(
                f"progress total count is set to default {self.progress_total} "
                f"due to you specified {type(total)} 'progress_total' argument which should be `int`. "
            )
        super().__init__(*args, **kwargs)

    @property
    def progress(self) -> tuple[int, int]:
        """Reports the current progress of the operation

        Returns:
            tuple[int, int]: A tuple of (current_count, total_count)
        """
        return self.progress_current, self.progress_total

    def progress_inc(self, amount: int = 1, step: int = 1) -> None:
        """Synchronously increases the progress counter

        Args:
            amount: Number of units to increase by (default: 1)
            step: Multiplier for each unit (default: 1)
        """
        assert step > 0, f"step must be greater than 0. you specified {amount}"
        assert amount > 0, f"amount must be greater than 0. you specified {amount}"

        delta = amount * step
        self.progress_current = min(self.progress_current + delta, self.progress_total)

    def progress_dec(self, amount: int = 1, step: int = 1) -> None:
        """Synchronously decreases the progress counter

        Args:
            amount: Number of units to decrease by (default: 1)
            step: Multiplier for each unit (default: 1)
        """
        assert step > 0, f"step must be greater than 0. you specified {amount}"
        assert amount > 0, f"amount must be greater than 0. you specified {amount}"

        delta = amount * step
        assert (
            self.progress_current >= delta
        ), f"Cannot decrease progress below 0 (current: {self.progress_current}, decrease: {delta}={amount}*{step})"
        self.progress_current = self.progress_current - delta
