import json
import logging
import boto3

logger = logging.getLogger(__name__)


class LambdaInvoker:
    lambda_client = boto3.client("lambda")

    @staticmethod
    async def invoke_avatar_processor(avatar: str, home_page: str) -> None:
        """
        Asynchronously invoke Lambda function to process avatar

        Args:
            avatar: URL of the avatar image
            home_page: User's profile URL
        """
        payload = {"avatar": avatar, "home_page": home_page}

        try:
            response = LambdaInvoker.lambda_client.invoke(
                FunctionName="social-media-avatar-test-download_and_upload_to_s3",
                InvocationType="Event",
                Payload=json.dumps(payload),
            )
            logger.debug(f"Successfully invoked avatar processor lambda for {payload}")
            return response
        except Exception as e:
            logger.error(
                f"Failed to invoke avatar processor lambda for {home_page}: {str(e)}"
            )
            return None
