from pyagent.tool import agent_tool, ToolRepo, get_local_tool_repo


@agent_tool
def hello():
    """Say hello to the world"""
    print("hello world")


@agent_tool
async def foo():
    """An async foo function"""
    print("foo")


if __name__ == "__main__":
    repo: ToolRepo = get_local_tool_repo()
    print("Tools in global repository:")
    print(repo.as_prompt())

    # 测试工具调用
    for tool in repo.list():
        print(f"\nTesting tool '{tool.name}':")
        try:
            result = tool.invoke()
            print(f"Result: {result}")
        except Exception as e:
            print(f"Error: {e}")
