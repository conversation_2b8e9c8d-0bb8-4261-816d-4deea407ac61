import json
import re
from abc import abstractmethod
from datetime import datetime
from logging import getLogger
from typing import Optional, List, Union, Dict, Any
from typing import TypeV<PERSON>, Tu<PERSON>, Generic, Literal

from pydantic import BaseModel

from pycommon.utils.datetimeutils import parse_datetime, format_datetime

logger = getLogger(__name__)

PromptMessages = TypeVar(
    "PromptMessages",
    bound=Union[
        List[Dict[str, str]],  # OpenAI JSON prompts
        List[str],  # Claude Legacy Text Prompts
        Tuple[str, List[Dict[str, str]]],  # Claude JSON prompts
    ],
)

PromptDoc = TypeVar("PromptDoc", bound=Union[str])
ShortTermMemory = list[str]


class PromptBuilder(BaseModel, Generic[PromptMessages, PromptDoc]):
    """Build prompt and truncate it if it exceeds the token limit"""

    sys_prompt: str
    user_msg: str | List
    llm_model_name: str
    few_shots_msgs: Optional[List[Dict[str, str]]] = None

    # insert memory messages into the prompt
    include_memory_msgs_type: Literal["only_user_msgs", "all_msgs", "none"] = "only_user_msgs"
    memory: Optional[ShortTermMemory] = None
    memory_token_limit: int = 500  # truncate memory messages if it exceeds this limit

    # insert fetched data into the prompt as retrieval augmented generation
    # usually this data should be ranked and truncated before being inserted
    fetched_data: Optional[List[PromptDoc]] = None

    # Sometimes we need to override the current time we tell LLM (e.g. for evaluation)
    override_datetime: Optional[Union[datetime, str]] = None

    @abstractmethod
    def build(self) -> PromptMessages:
        """How these data form the prompt should be tested systematically"""


class OpenAiPromptBuilder(PromptBuilder):
    """Build OpenAI prompt"""

    post_user_msg_sys_prompt: Optional[str] = None

    include_few_shots_divider: bool = False

    def _user_msg_with_fetched_data(self) -> List[Dict]:
        """Build user msg with fetched data as retrieval augmented generation."""

        if not self.fetched_data:
            return [{"role": "user", "content": self.user_msg}]

        content = ""
        for d in self.fetched_data:
            fetched_data_str = '"""\n' + str(d) + '\n"""\n'
            content += fetched_data_str
        content += f"\nQuestion: {self.user_msg}"

        return [
            {
                "role": "user",
                "content": content,
            }
        ]

    def build(self) -> List[Dict]:
        """How these data form the prompt should be tested systematically"""

        if self.override_datetime:
            if isinstance(self.override_datetime, str):
                self.override_datetime = parse_datetime(self.override_datetime)
            sys_prompt = (
                self.sys_prompt
                + f"\n\nCurrent date & time: {format_datetime(self.override_datetime, for_persist=False)}\n\n"
            )
        else:
            sys_prompt = self.sys_prompt

        prompt_msgs = [
            {"role": "system", "content": sys_prompt},
        ]

        if self.few_shots_msgs:
            prompt_msgs += self.few_shots_msgs

        if self.memory:
            raise NotImplementedError(f"memory for prompt builder is not implemented")

        if self.include_few_shots_divider:
            prompt_msgs += [
                {
                    "role": "system",
                    "content": "Above are the few-shot examples. Below is the real user message.",
                },
            ]

        prompt_msgs += self._user_msg_with_fetched_data()
        if self.post_user_msg_sys_prompt:
            prompt_msgs += [
                {"role": "system", "content": self.post_user_msg_sys_prompt},
            ]

        logger.debug(f"Prompt Msgs: {json.dumps(prompt_msgs, indent=4)}")
        return prompt_msgs


regex_gpt_ox_model = re.compile(r"^o\d(-\w+)*$")


def create_prompt_builder(
    sys_prompt: str,
    user_msg: str,
    llm_model_name: str,
    few_shots_msgs: Optional[List[Dict[str, str]]] = None,
    memory: Optional[ShortTermMemory] = None,
    fetched_data: Optional[List[PromptDoc]] = None,
    override_datetime: Optional[Union[datetime, str]] = None,
    **kwargs: Any,
) -> PromptBuilder:
    all_args = {
        "llm_model_name": llm_model_name,
        "sys_prompt": sys_prompt,
        "user_msg": user_msg,
        "few_shots_msgs": few_shots_msgs,
        "memory": memory,
        "fetched_data": fetched_data,
        "override_datetime": override_datetime,
    }
    all_args.update(kwargs)

    if llm_model_name.startswith("gpt-") or regex_gpt_ox_model.match(llm_model_name):
        builder = OpenAiPromptBuilder(**all_args)  # type: ignore
    elif llm_model_name.startswith("claude-"):
        builder = ClaudeJsonPromptBuilder(**all_args)  # type: ignore
    elif llm_model_name.startswith("anthropic."):
        builder = ClaudeTextPromptBuilder(**all_args)  # type: ignore
    else:
        raise NameError(f"{llm_model_name} is unsupported LLM model name.")

    return builder
