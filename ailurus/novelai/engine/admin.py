import re
from datetime import timed<PERSON><PERSON>, datetime
from typing import Any

from django import forms
from django.contrib import admin
from django.db import connection
from django.utils.safestring import mark_safe
from django_json_widget.widgets import JSONEditorWidget

from djcommon.model import CreateTimeMixin
from novelai.engine.consts import WEB_NOVEL_GENRE_DICT
from novelai.engine.models.base import AgentStat
from novelai.engine.models.chapter import StoryChapter
from novelai.engine.models.character import <PERSON><PERSON>haracter, StoryCharacterLevel
from novelai.engine.models.outline import StoryOutline
from novelai.engine.models.story import Story
from novelai.engine.models.world import StoryWorld
from pycommon.utils.datetimeutils import format_datetime


@admin.display(description="created at")
def created_at_disp(obj: CreateTimeMixin) -> str:
    s = format_datetime(obj.created_at)
    s = re.sub(r'^\d{4}-', '', s).replace("Asia/Shanghai", "CST")
    return mark_safe(s)


@admin.display(description="name/key")
def name_key_disp(obj: Story | StoryCharacter) -> str:
    name = obj.name or (obj.code if hasattr(obj, "code") else "-")
    is_name_confirmed = obj.is_name_confirmed if hasattr(obj, "is_name_confirmed") else False
    name_lock = "🔒" if is_name_confirmed else ""
    s = f"{name_lock}{name}<br/>{obj.key}"
    return mark_safe(s)


class StoryGenreFilter(admin.SimpleListFilter):
    title = "Story Genre"
    parameter_name = "genres"  # 过滤器使用的过滤字段

    def lookups(self, request, model_admin):
        """针对字段值设置过滤器的显示效果"""
        return ((k, k) for k in WEB_NOVEL_GENRE_DICT.keys())

    def queryset(self, request, queryset):
        """定义过滤器的过滤动作"""
        v = self.value()
        if not v:
            return queryset.all()
        return queryset.filter(types__contains=v).all()


class CharacterLevelFilter(admin.SimpleListFilter):
    title = "Character Level"
    parameter_name = "level"  # 过滤器使用的过滤字段

    def lookups(self, request, model_admin):
        """针对字段值设置过滤器的显示效果"""
        return ((c, c.disp()) for c in StoryCharacterLevel)

    def queryset(self, request, queryset):
        """定义过滤器的过滤动作"""
        v = self.value()
        if not v:
            return queryset.all()
        return queryset.filter(level__contains=v).all()


class StateSuccessFilter(admin.SimpleListFilter):
    title = "State Success"
    parameter_name = "state_success"  # 过滤器使用的过滤字段

    def lookups(self, request, model_admin):
        """针对字段值设置过滤器的显示效果"""
        return (
            ("success", "成功"),
            ("failed", "失败"),
        )

    def queryset(self, request, queryset):
        """定义过滤器的过滤动作"""
        v = self.value()
        if not v:
            return queryset.all()

        if v == "success":
            # 成功：errors 为 None 或空列表
            return queryset.filter(errors__isnull=True) | queryset.filter(errors={})
        elif v == "failed":
            # 失败：errors 不为 None 且不为空列表
            return queryset.exclude(errors__isnull=True).exclude(errors={})

        return queryset.all()


class StatStoryFilter(admin.SimpleListFilter):
    title = "Story"
    parameter_name = "agent"  # 过滤器使用的过滤字段
    __sorted = False

    def lookups(self, request, model_admin):
        """针对字段值设置过滤器的显示效果"""
        return ((s.name, s.name) for s in Story.objects.all())

    def queryset(self, request, queryset):
        """定义过滤器的过滤动作"""
        v = self.value()
        if not v:
            return queryset.all()

        return queryset.filter(agent__contains=v)

    def choices(self, changelist):
        """根据每一个选项的数量来排序，且去掉数量为0的选项"""

        if not self.__sorted:
            add_facets = changelist.add_facets
            if add_facets:
                facet_counts = self.get_facet_queryset(changelist) if add_facets else None
                new_choices = []
                for i, (lookup, title) in enumerate(self.lookup_choices):
                    count = facet_counts.get(f"{i}__c", -1)
                    if count > 0:
                        new_choices.append(((lookup, title), count))
                new_choices = sorted(new_choices, key=lambda x: x[1], reverse=True)
                self.lookup_choices = [x for x, count in new_choices]
                self.__sorted = True

        return super().choices(changelist)


class StatModelFilter(admin.SimpleListFilter):
    title = "LLM Model"
    parameter_name = "config"  # 过滤器使用的过滤字段
    __cache: tuple | None = None
    __cache_timeout: timedelta = timedelta(minutes=60)

    def lookups(self, request, model_admin):
        """针对字段值设置过滤器的显示效果"""
        # 获取所有 AgentStat 记录中 config.llm_model 的唯一值

        if self.__cache:
            ts = self.__cache[1]
            if ts > datetime.now() - self.__cache_timeout:
                return self.__cache

        if connection.vendor == "postgresql":
            with connection.cursor() as cursor:
                cursor.execute(
                    """
                           SELECT DISTINCT config->>'llm_model' as llm_model
                           FROM novelai_engine_agentstat
                           WHERE config->>'llm_model' IS NOT NULL
                           ORDER BY llm_model
                       """
                )
                models = [row[0] for row in cursor.fetchall()]

            result = [(model, model.split("/")[-1]) for model in models]
        else:
            models = set()
            for stat in AgentStat.objects.exclude(config__isnull=True):
                llm_model = stat.config.get("llm_model")
                if llm_model:
                    models.add(llm_model)

            result = [(model, model.split("/")[-1]) for model in sorted(models)]

        self.__cache = (result, datetime.now())
        return result

    def queryset(self, request, queryset):
        """定义过滤器的过滤动作"""
        v = self.value()
        if not v:
            return queryset.all()

        return queryset.filter(config__llm_model=v)


class StoryForm(forms.ModelForm):
    class Meta:
        model = Story
        fields = "__all__"
        widgets = {
            field.name: JSONEditorWidget(height="250px")
            for field in Story._meta.fields
            if field.get_internal_type() == "JSONField"
        } | {
            field.name: forms.Textarea(attrs={"rows": 3, "cols": 80})
            for field in Story._meta.fields
            if field.get_internal_type() == "CharField" and field.max_length > 50
        }


class StoryAdmin(admin.ModelAdmin):
    form = StoryForm
    list_display = ["id", name_key_disp, "author", "genres", "words_plan", "themes_disp", created_at_disp]
    list_display_links = ["id", name_key_disp]
    search_fields = ["id", "name", "key", "author", "background", "plot"]
    list_filter = ["lang", "author", StoryGenreFilter]
    readonly_fields = ["created_at", "tech_detail"]
    # date_hierarchy = "created_at"

    @admin.display(description="name")
    def themes_disp(self, obj: Story) -> str:
        s = ", ".join(obj.themes.get("major") or []) + " | " + ", ".join(obj.themes.get("minor") or [])
        return mark_safe(s)


class StoryOutlineForm(forms.ModelForm):
    class Meta:
        model = StoryOutline
        fields = "__all__"
        widgets = {
            field.name: JSONEditorWidget(height="250px")
            for field in StoryOutline._meta.fields
            if field.get_internal_type() == "JSONField"
        }


class StoryOutlineAdmin(admin.ModelAdmin):
    form = StoryOutlineForm
    list_display = [
        "id",
        "story",
        created_at_disp,
    ]
    list_display_links = ["id", "story"]
    raw_id_fields = ["story"]
    readonly_fields = ["created_at", "tech_detail"]
    list_filter = ["story"]
    search_fields = [
        "id",
        "story__name",
        "story__key",
        "story_overview",
        "plot_framework",
        "chapter_planning",
        "themes_and_symbolism",
        "other_elements",
        "writing_advice",
    ]


class StoryWorldForm(forms.ModelForm):
    class Meta:
        model = StoryWorld
        fields = "__all__"
        widgets = {
            field.name: JSONEditorWidget(height="250px")
            for field in StoryWorld._meta.fields
            if field.get_internal_type() == "JSONField"
        }


class StoryWorldAdmin(admin.ModelAdmin):
    form = StoryWorldForm
    list_display = [
        "id",
        "name",
        "story",
        "is_selected",
        created_at_disp,
    ]
    list_display_links = ["id", "name"]
    raw_id_fields = ["story"]
    readonly_fields = ["created_at", "tech_detail"]
    list_filter = ["story"]
    search_fields = [
        "id",
        "name",
        "story__name",
        "story__key",
        "world_setting",
        "historical_events",
        "cultural_traits",
        "factions",
        "specific_mechanics",
        "core_items",
        "world_rules",
    ]


class StoryCharacterForm(forms.ModelForm):
    class Meta:
        model = StoryCharacter
        fields = "__all__"
        widgets = {
            field.name: JSONEditorWidget(height="250px")
            for field in StoryCharacter._meta.fields
            if field.get_internal_type() == "JSONField"
        }


class StoryCharacterAdmin(admin.ModelAdmin):
    form = StoryCharacterForm
    list_display = ["id", name_key_disp, "level", "story", "aliases", "is_force", created_at_disp]
    list_display_links = ["id", name_key_disp, "aliases"]
    raw_id_fields = ["story"]
    readonly_fields = ["created_at", "tech_detail"]
    list_filter = ["story", CharacterLevelFilter]
    search_fields = ["id", "name", "key", "story__name", "story__key", "bio", "relationships"]

    @admin.display(description="")
    def name_key_disp(self, obj: Story | StoryCharacter) -> str:
        s = f"{obj.name}<br/>{obj.key}"
        return mark_safe(s)


class StoryChapterForm(forms.ModelForm):
    class Meta:
        model = StoryChapter
        fields = "__all__"
        widgets = {
            field.name: JSONEditorWidget(height="250px")
            for field in StoryChapter._meta.fields
            if field.get_internal_type() == "JSONField"
        }


class StoryChapterAdmin(admin.ModelAdmin):
    form = StoryChapterForm
    list_display = [
        "id",
        "vol_chap_disp",
        "title",
        "type",
        "story",
        "words_disp",
        created_at_disp,
    ]

    list_display_links = ["id", "title", "vol_chap_disp"]
    search_fields = ["title", "story__name", "story__key"]
    raw_id_fields = ["story"]
    readonly_fields = ["created_at", "tech_detail"]

    @admin.display(description="vol/chapter")
    def vol_chap_disp(self, obj: StoryChapter) -> str:
        return f"vol.{obj.volume} - {obj.order}"

    @admin.display(description="words")
    def words_disp(self, obj: StoryChapter) -> str:
        return str(len(obj.content))


class AgentStatesForm(forms.ModelForm):
    class Meta:
        model = AgentStat
        fields = "__all__"
        widgets = {
            field.name: JSONEditorWidget(height="250px")
            for field in AgentStat._meta.fields
            if field.get_internal_type() == "JSONField"
        }


class AgentStatAdmin(admin.ModelAdmin):
    form = AgentStatesForm
    list_display = [
        "id",
        "run_id_disp",
        "agent_disp",
        "model_disp",
        "cost_disp",
        "inputs_disp",
        "perf_disp",
        "succeed_disp",
        created_at_disp,
    ]
    list_display_links = ["id", "run_id_disp", "agent_disp"]
    search_fields = ["agent", "inputs", "output", "config", "llm_prompts", "llm_outputs", "errors"]
    list_filter = [StateSuccessFilter, StatStoryFilter, StatModelFilter]
    readonly_fields = ["created_at"]

    @admin.display(description="run_id")
    def run_id_disp(self, obj: AgentStat) -> str:
        return obj.extra_data.get("run_id") or "-"

    @admin.display(description="agent")
    def agent_disp(self, obj: AgentStat) -> str:
        return obj.agent.split("/")[-1].replace("-agent-", " | ")

    @admin.display(description="inputs")
    def inputs_disp(self, obj: AgentStat) -> str:
        return mark_safe("<br/>".join([f"{k}: {v}" for k, v in obj.inputs.items()]))

    @admin.display(description="✌️", boolean=True)
    def succeed_disp(self, obj: AgentStat) -> bool:
        return obj.errors is None or len(obj.errors) == 0

    @admin.display(description="model️")
    def model_disp(self, obj: AgentStat) -> bool:
        m = obj.config.get("llm_model") or "-"
        m = m.split("/")[-1]
        t = obj.config.get("llm_temperature") or "-"
        return m + " / " + str(t)

    @admin.display(description="💰($0.01)")
    def cost_disp(self, obj: AgentStat) -> str:

        if not obj.llm_cost:
            return "-"

        # cost threshold for color
        t1, t2, t3 = 0.1, 0.5, 1

        input_tokens = obj.llm_cost.get("input_tokens") or "-"
        output_tokens = obj.llm_cost.get("output_tokens") or "-"
        cost = obj.llm_cost.get("cost") or "-"
        cache_hit = obj.llm_cost.get("cache_hit") or False

        def _cost_html(v: Any, tip: str) -> str:
            try:
                v = float(v) * 100  # use cent ($0.01) as unit
                color = "red" if v > t3 else "goldenrod" if v > t2 else "black" if v > t1 else "green"
                v = f"{v:.4f}"  # 会四舍五入
            except:  # noqa: E722
                v = f"ERR:{v}"
                color = "red"
            return f"<span style='color:{color};' title='{tip}'>{v} {"🎯" if cache_hit else ""}</span>"

        lines = [
            # f"<span style='white-space:nowrap;'>{input_tokens} tokens/in</span>",
            # f"<span style='white-space:nowrap;'>{output_tokens} tokens/out</span>",
            # f"<span style='white-space:nowrap;'>{cache_hit} cache_hit</span>",
            _cost_html(cost, f"{input_tokens} tokens/in\n{output_tokens} tokens/out"),
        ]
        rc = mark_safe("<br/>".join(lines))
        return rc

    @admin.display(description="perf")
    def perf_disp(self, obj: AgentStat) -> str:
        def color(v: list[float] | float) -> str:
            if isinstance(v, float):
                return "green" if v < 15 else "black" if v < 30 else "goldenrod" if v < 60 else "red"
            else:
                if max(v) < 15:
                    return "green"
                if max(v) < 30:
                    return "black"
                if max(v) < 60:
                    return "goldenrod"
                return "red"

        _fmt = lambda v: f"{v:.4f}"  # 会四舍五入

        def format_perf(perf: float | list[float]) -> str:

            if not isinstance(perf, list):
                return _fmt(perf)

            if len(perf) <= 10:
                return str([_fmt(v) for v in perf])

            return f"[{', '.join(_fmt(x) for x in perf[:4])} ... {', '.join(_fmt(x) for x in perf[-4:])}] ({len(perf)} total)"

        return mark_safe(
            "<br/>".join(
                [
                    (
                        f'<span style="white-space:nowrap">{k}: '
                        f'<span style="color:{color(v)}">{format_perf(v)}</span>'
                    )
                    for k, v in obj.perf.items()
                ]
            )
        )


# Register your models here.
admin.site.register(AgentStat, AgentStatAdmin)
admin.site.register(Story, StoryAdmin)
admin.site.register(StoryOutline, StoryOutlineAdmin)
admin.site.register(StoryWorld, StoryWorldAdmin)
admin.site.register(StoryCharacter, StoryCharacterAdmin)
admin.site.register(StoryChapter, StoryChapterAdmin)
