import asyncio
import time
from abc import ABC
from logging import getLogger
from queue import Queue
from typing import Op<PERSON>, AsyncGenerator
from urllib.parse import quote

import playwright
from playwright.async_api import Response as AsyncResponse

from scraper.spiders.base import (
    PlaywrightSpiderConfig,
    Playwright<PERSON><PERSON>er,
    PlaywrightSearchSpiderConfig,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>pider,
    SpiderNavAnchor,
)
from ..my_models import parse_count, <PERSON>uyinProfile, DouyinPost
from ..utils.lambda_invoker import LambdaInvoker

logger = getLogger(__name__)


class DouyinSpiderConfig(PlaywrightSpiderConfig):
    pass


class DouyinSearchSpiderConfig(PlaywrightSearchSpiderConfig, DouyinSpiderConfig):
    """the config for Douyin search spider"""

    use_lambda_avatar_processor: bool = True


class DouyinSpider(PlaywrightSpider, ABC):
    """abstract base of Douyin spider built by <PERSON><PERSON>"""

    config: DouyinSpiderConfig

    async def async_login(self) -> None:
        logger.info("login Douyin ...")
        if True:
            # currently we do not login. we use user-data-dir
            pass
        else:
            page = await self.async_get_page()
            email = self.config.credential.get("email")
            password = self.config.credential.get("password")

            await page.wait_for_selector(
                'button#header-login-button:has-text("Log in"):visible',
                state="visible",
                timeout=self.config.timeout * 1000,
            )
            logger.debug("login button loaded")

            await page.get_by_role(role="button", name="Log in", include_hidden=False).first.click()
            await page.get_by_text("Use phone / email / username").click()
            await page.get_by_text("Log in with email or username").click()
            await page.get_by_placeholder("Email or username").fill(email)
            await page.get_by_placeholder("Password").fill(password)
            await page.get_by_role(role="button", name="Log in").last.click()

            await page.wait_for_load_state("load")
            # # Check for error message
            # """
            # <div type="error" aria-live="assertive" class="css-tfg9wk-DivTextContainer e3v3zbj0"><span role="status">Maximum number of attempts reached. Try again later.</span></div>
            # """
            # await expect(page.locator('div[type="error"]')).to_be_hidden()
            #
            # error_selector = 'div[type="error"]'
            # try:
            #     error_element = await page.wait_for_selector(
            #         error_selector, timeout=5000
            #     )
            #     if error_element:
            #         error_message = await error_element.inner_text()
            #         logger.error(f"Login error: {error_message}")
            #         raise Exception(f"Login failed: {error_message}")
            # except Exception as e:
            #     if "Login failed" in str(e):
            #         raise
            #     logger.debug("No error message found, assuming login successful")

        logger.debug("logged in")

    async def _async_iter_api_response(self, data: dict | list, term: str) -> AsyncGenerator[DouyinProfile, None]:
        """async iterator for extract video data from Douyin API response.
        Args:
            data: API response data
            term: Search keyword or tag
        """
        assert isinstance(data, dict)
        if "data" in data and isinstance(data["data"], list):
            for item in data["data"]:
                if "item" in item:
                    video_info = self._extract_item(item)
                    video_info["term"] = term
                    yield video_info

    def _extract_item(self, item: dict) -> dict:
        video_data = item["item"]
        author = video_data.get("author", {}).get("uniqueId", "")
        avatar = video_data.get("author", {}).get("avatarLarger", "")
        video_id = video_data.get("id", "")
        assert author and video_id
        # Construct detail URL
        detail_url = f"https://www.Douyin.com/@{author}/video/{video_id}"

        video_info = {
            "id": video_id,
            "detail_url": detail_url,
            "desc": video_data.get("desc", ""),
            "create_time": time.strftime(
                "%Y-%m-%d %H:%M:%S",
                time.localtime(video_data.get("createTime", 0)),
            ),
            "author": author,
            "avatar": avatar,
            "home_page": f"https://www.Douyin.com/@{author}",
            "author_stats": {
                "digg_count": video_data.get("authorStats", {}).get("diggCount", 0),
                "follower_count": video_data.get("authorStats", {}).get("followerCount", 0),
                "following_count": video_data.get("authorStats", {}).get("followingCount", 0),
                "friend_count": video_data.get("authorStats", {}).get("friendCount", 0),
                "heart": video_data.get("authorStats", {}).get("heart", 0),
                "heart_count": video_data.get("authorStats", {}).get("heartCount", 0),
                "video_count": video_data.get("authorStats", {}).get("videoCount", 0),
            },
            "nickname": video_data.get("author", {}).get("nickname", ""),
            "signature": video_data.get("author", {}).get("signature", ""),
            "duration": video_data.get("video", {}).get("duration", 0),
            "stats": {
                "play_count": video_data.get("stats", {}).get("playCount", 0),
                "digg_count": video_data.get("stats", {}).get("diggCount", 0),
                "comment_count": video_data.get("stats", {}).get("commentCount", 0),
                "share_count": video_data.get("stats", {}).get("shareCount", 0),
                "collect_count": video_data.get("stats", {}).get("collectCount", 0),
            },
            "music_title": video_data.get("music", {}).get("title", ""),
            "music_author": video_data.get("music", {}).get("authorName", ""),
            "video_url": video_data.get("video", {}).get("playAddr", ""),
            "cover_url": video_data.get("video", {}).get("cover", ""),
            "hashtags": [
                {"id": d1.get("hashtagId", ""), "name": d1.get("hashtagName", "")}
                for d in video_data.get("contents", [])
                for d1 in d.get("textExtra", [])
            ],
        }
        return video_info


class DouyinSearchSpider(PlaywrightSearchSpider, DouyinSpider):
    """Douyin spider for search"""

    config: DouyinSearchSpiderConfig

    __q_entries: Optional[Queue] = None

    def _parse_result_profile(self, item: dict) -> DouyinProfile:
        """
        Parse a raw TikTok search result dictionary into a DouyinProfile object.
        """
        # Basic profile fields
        handle = item["author"]
        full_name = item["nickname"]
        url = item["home_page"]
        signature = item["signature"]
        avatar_url = item["avatar"]

        # Stats
        author_stats = item["author_stats"]
        followers = parse_count(author_stats["follower_count"])
        following = parse_count(author_stats["following_count"])
        posts_count = parse_count(author_stats["video_count"])

        # Relevant post
        relevant_post = DouyinPost(
            id=item["id"],
            url=item["detail_url"],
            caption=item["desc"],
            published_time_str=item["create_time"],
            like_count=item["stats"]["digg_count"],
            comment_count=item["stats"]["comment_count"],
            share_count=item["stats"]["share_count"],
            play_count=item["stats"]["play_count"],
            duration=item["duration"],
            digg_count=item["stats"]["digg_count"],
            collect_count=item["stats"]["collect_count"],
            music_title=item["music_title"],
            music_author=item["music_author"],
            hashtags=([tag["name"] for tag in item["hashtags"]] if item["hashtags"] else []),
        )

        return DouyinProfile(
            handle=handle,
            full_name=full_name,
            url=url,
            biography=signature,
            avatar_url=avatar_url,
            followers=followers,
            following=following,
            posts_count=posts_count,
            relevant_post=relevant_post,
            email=None,  # To be populated in influencer agent
        )

    async def _async_search(self, query: str, q_iter: Optional[Queue] = None) -> dict[str, DouyinProfile]:  # noqa: C901
        current_keyword = None

        async def handle_response(response: AsyncResponse) -> None:
            url = response.url
            nonlocal current_keyword

            # Handle search results
            if "/api/search/general/full/" in url and current_keyword:
                logger.debug(f"handling Douyin search API: {url}")
                self.result.visited_pages.append(url)
                try:
                    data = await response.json()
                    # logger.debug(f"got {len(data)} data from API")
                    async for post in self._async_iter_api_response(data, current_keyword):
                        # if self.shall_stop():
                        #     logger.warning(
                        #         "quit handle_response due to shall_stop is True"
                        #     )
                        #     return

                        profile = self._parse_result_profile(post)
                        if q_iter is None:
                            self.all_entries[post["id"]] = profile
                        else:
                            q_iter.put(profile)
                        self.item_count += 1

                        # Process avatar using Lambda in a separate task
                        if self.config.use_lambda_avatar_processor and profile.avatar_url and profile.url:
                            asyncio.create_task(
                                LambdaInvoker.invoke_avatar_processor(avatar=profile.avatar_url, home_page=profile.url)
                            )

                except Exception as e:
                    logger.error(f"Error handling search response: {e}")

        page = await self.async_get_page()
        page.on("response", handle_response)

        current_keyword = query
        search_url = f"https://www.Douyin.com/search?q={quote(query)}&t={int(time.time() * 1000)}"
        self.result.visited_pages.append(search_url)

        anchor = 0
        await self.async_go_to(search_url)
        logger.debug(f"crawled entries: {max(len(self.all_entries), self.item_count)}")
        anchor += 1

        consecutive_no_new_data = 0
        while True:
            if await self.shall_stop(query):
                break

            last_item_count = self.item_count

            anchor = await self.async_go_next(anchor, is_infinity_page=True)
            await self.async_sleep()
            logger.info(f"crawled entries: {max(len(self.all_entries), self.item_count)}")
            if anchor is None:
                # reach the end
                break
            if 0 < self.config.items <= self.item_count:
                logger.info(f"reach config items count {self.item_count}>={self.config.items}")
                break

            if self.item_count == last_item_count:
                consecutive_no_new_data += 1
                if consecutive_no_new_data > 3:
                    logger.info(
                        f"finish '{query}' due to items count {self.item_count}) not increased for {consecutive_no_new_data} times."
                    )
                    break
            else:
                consecutive_no_new_data = 0

        self.result.entries = self.all_entries
        return self.all_entries

    async def async_check_if_has_next_page(self) -> bool:
        page = await self.async_get_page()
        return await page.locator("text=No more results").count() <= 0

    async def async_scroll_a_page(
        self,
        anchor: SpiderNavAnchor = None,
        timeout: int = 0,
    ) -> SpiderNavAnchor:
        timeout = 2
        try:
            anchor = await super().async_scroll_a_page(anchor, timeout)
        except playwright.async_api.TimeoutError:
            logger.debug(f"DouyinSearchSpider ignore timeout during scroll to next page {anchor}")
        return anchor


if __name__ == "__main__":
    import logging
    import colorlog

    colorlog.basicConfig(
        level=logging.DEBUG,
        format="%(log_color)s[%(levelname).05s] [%(asctime)s] [%(name)s:%(lineno)d] %(message)s",
    )

    async def __async_search() -> None:
        cfg = DouyinSearchSpiderConfig(
            term="吃播",
            name="douyin",
            browser_type="chromium",
            start_url="https://www.douyin.com",
            # proxy={"server": "http://127.0.0.1:1087"},
            # proxy={
            #     "server": "http://auto:<EMAIL>:8000"
            # },
            is_headless=False,
            timeout=30,
            # credential={
            #     "email": os.getenv("EMAIL", ""),
            #     "password": os.getenv("PASSWORD", ""),
            # },
            ignored_resources=[],  # ["font", "image", "stylesheet"],
            # user_data_dir=str(
            #     (Path(__file__).parent.parent / "chrom-user-data-douyin").absolute()
            # ),
        )
        spdr = DouyinSearchSpider(config=cfg)
        await spdr.async_start()
        print(spdr.result)
        await spdr.async_stop()
        # s = await asyncio.run(spd._async_download("https://www.baidu.com"))
        # print(s)

    asyncio.run(__async_search())
