from django.db import models
from django.utils.html import escape

from novelai import conf
from novelai.engine.models.base import NovelModelBase

d_column_helps = {
    "story": {
        "zh": "大纲纲所属的小说",
        "en": "The story this outline belongs to",
    },
    "name": {
        "zh": "世界的名称",
        "en": "The name of the world",
    },
    "world_setting": {
        "zh": "世界观设定",
        "en": "World setting and background",
    },
    "historical_events": {
        "zh": "世界观历史",
        "en": "Historical events of the world",
    },
    "cultural_traits": {
        "zh": "世界观文化",
        "en": "Cultural traits and characteristics",
    },
    "factions": {
        "zh": "关键派系或组织系统(格式: {<faction_name>:<description>})",
        "en": "Key factions or organizational systems(format: {<faction_name>:<description>})",
    },
    "specific_mechanics": {
        "zh": "特定体系的具体机制((格式: {<mechanic_name>:<description>})",
        "en": "Specific mechanics for genre-specific systems(format: {<mechanic_name>:<description>})",
    },
    "core_item": {
        "zh": "核心元素或物品(格式: {<item_name>:<description>})",
        "en": "Core elements or items(format: {<item_name>:<description>})",
    },
    "world_rules": {
        "zh": "世界规则(格式: {<rule>:<description>})",
        "en": "World rules and laws(format: {<rule>:<description>})",
    },
    "summary": {
        "zh": "世界观总结(将世界观所有设定浓缩到500字以内markdown，不要漏任何信息）",
        "en": "Summary of the world(concise summary of the world setting in markdown format)",
    },
    "is_selected": {
        "zh": "该世界是否被选择为小说的一个世界观",
        "en": "Whether this world is selected as one of the worlds of the story",
    },
}


class StoryWorld(NovelModelBase):
    """The world-building(s) of the story

    1. 世界观设定(world_setting)
    2. 世界观历史(historical_events)
    3. 世界观文化(cultural_traits)
    4. 关键派系或组织系统(factions:list[dict], 例如 faction_1, faction_2, ...)
    5. 特定体系(genre-specific systems), 包括具体机制 [specific_mechanics] (list[dict])
        - 修真/仙/道/佛/魔/...体系(Cultivation/Taoism/Buddhism/Ascension/Demon System) - 仙侠，武侠，玄幻 等
        - 境界划分(Level System) - 仙侠，武侠，玄幻 等
        - 功法/魔法/技能/...体系(KongFu/Magic/Technique System) - 仙侠、玄幻、奇幻、武侠 等
        - 门派/组织/...系统(Faction System) - 仙侠，武侠，玄幻 等
        - 超自然体系(supernatural) - 奇幻、玄幻 等
        - 社会体系(social) - 历史、都市、仙侠，武侠，玄幻 等
        - 科技体系(technological) - 科幻 等

    6. 核心元素或物品（例如 [core_item]）和世界规则（例如 [world_rules]）。
    """

    story = models.ForeignKey(
        "Story",
        on_delete=models.CASCADE,
        help_text=escape(d_column_helps["story"][conf.LANG]),
    )
    name = models.CharField(max_length=250, help_text=escape(d_column_helps["name"][conf.LANG]))

    world_setting = models.TextField(
        default="", blank=True, help_text=escape(d_column_helps["world_setting"][conf.LANG])
    )
    historical_events = models.TextField(
        default="", blank=True, help_text=escape(d_column_helps["historical_events"][conf.LANG])
    )
    cultural_traits = models.TextField(
        default="", blank=True, help_text=escape(d_column_helps["cultural_traits"][conf.LANG])
    )
    factions = models.JSONField(default=dict, blank=True, help_text=escape(d_column_helps["factions"][conf.LANG]))
    specific_mechanics = models.JSONField(
        default=dict, blank=True, help_text=escape(d_column_helps["specific_mechanics"][conf.LANG])
    )
    core_items = models.JSONField(default=dict, blank=True, help_text=escape(d_column_helps["core_item"][conf.LANG]))
    world_rules = models.JSONField(blank=dict, help_text=escape(d_column_helps["world_rules"][conf.LANG]))

    summary = models.TextField(default="", help_text=escape(d_column_helps["summary"][conf.LANG]))

    is_selected = models.BooleanField(default=False, help_text=escape(d_column_helps["is_selected"][conf.LANG]))

    class Meta:
        ordering = ["story", "name"]
        unique_together = [["story", "name"]]

    def __str__(self):
        return self.name
