from asgiref.sync import sync_to_async
from django.core.exceptions import SynchronousOnlyOperation
from django.db import models
from django.utils.html import escape

from djcommon.model import CreateTimeMixin, UpdateTimeMixin
from novelai import conf
from novelai.engine.models.base import NovelModelBase

d_column_helps = {
    "story": {
        "zh": "大纲所属的小说",
        "en": "The story this outline belongs to",
    },
    "story_overview": {
        "zh": "故事概览，包含基调、风格、目标读者、字数规划、视角、冲突和摘要等信息",
        "en": "Story overview including tone, style, target audience, word count planning, perspective, conflicts and summary",
    },
    "world_setting": {
        "zh": "世界设定，包含世界观、超自然系统、派系、历史事件、文化特征、核心物品和世界规则",
        "en": "World setting including worldview, supernatural systems, factions, historical events, cultural traits, core items and world rules",
    },
    "character_design": {
        "zh": "角色设计，包含主要角色、配角和角色关系描述",
        "en": "Character design including main characters, supporting characters and relationship descriptions",
    },
    "plot_framework": {
        "zh": "情节框架，包含三幕结构的详细规划",
        "en": "Plot framework including detailed three-act structure planning",
    },
    "chapter_planning": {
        "zh": "章节规划，包含前10章的详细规划和后续章节的结构安排",
        "en": "Chapter planning including detailed planning for first 10 chapters and structural arrangement for subsequent chapters",
    },
    "themes_and_symbolism": {
        "zh": "主题与象征，包含核心主题、象征元素和情感线",
        "en": "Themes and symbolism including core themes, symbolic elements and emotional lines",
    },
    "other_elements": {
        "zh": "其他元素，包含关键伏笔、节奏安排、类型特定元素和写作风格",
        "en": "Other elements including key foreshadowing, pacing arrangements, genre-specific elements and writing style",
    },
    "writing_advice": {
        "zh": "写作建议，包含准备工作、节奏策略、灵活性和读者期望",
        "en": "Writing advice including preparation, pacing strategy, flexibility and reader expectations",
    },
}


class StoryOutline(NovelModelBase, CreateTimeMixin, UpdateTimeMixin):
    """represents the story outline."""

    story = models.OneToOneField(
        "Story",
        related_name="outline",
        on_delete=models.CASCADE,
        help_text=escape(d_column_helps["story"][conf.LANG]),
    )

    # Enhanced outline structure based on the example JSON
    story_overview = models.JSONField(
        default=dict, blank=True, help_text=escape(d_column_helps["story_overview"][conf.LANG])
    )
    plot_framework = models.JSONField(
        default=dict, blank=True, help_text=escape(d_column_helps["plot_framework"][conf.LANG])
    )
    chapter_planning = models.JSONField(
        default=dict, blank=True, help_text=escape(d_column_helps["chapter_planning"][conf.LANG])
    )
    themes_and_symbolism = models.JSONField(
        default=dict, blank=True, help_text=escape(d_column_helps["themes_and_symbolism"][conf.LANG])
    )
    other_elements = models.JSONField(
        default=dict, blank=True, help_text=escape(d_column_helps["other_elements"][conf.LANG])
    )
    writing_advice = models.JSONField(
        default=dict, blank=True, help_text=escape(d_column_helps["writing_advice"][conf.LANG])
    )

    class Meta:
        ordering = ["story", "id"]
        # unique_together = ["story", "id"]

    def __str__(self):
        return f"Story Outline ({self.story}, {self.created_at})"

    async def async_story(self) -> "Story":
        try:
            return self.story
        except SynchronousOnlyOperation:
            from .story import Story

            return await Story.objects.filter(id=self.story_id).prefetch_related("outline").afirst()

    def get_chapter_count_plan(self) -> int:
        """Get the planned chapter count from story_overview"""
        if self.story_overview and "chapter_count" in self.story_overview:
            return self.story_overview["chapter_count"]
        return 0

    def is_complete_outline(self) -> bool:
        """Check if the outline has all major sections filled"""
        required_sections = [
            "story_overview",
            "plot_framework",
            "chapter_planning",
            "themes_and_symbolism",
            "other_elements",
            "writing_advice",
        ]
        for section in required_sections:
            section_data = getattr(self, section, {})
            if not section_data:
                return False

        if len(self.story.get_characters()) <= 0:
            return False

        if len(self.story.get_worlds()) <= 0:
            return False

        return True

    async_is_complete_outline = sync_to_async(is_complete_outline)
