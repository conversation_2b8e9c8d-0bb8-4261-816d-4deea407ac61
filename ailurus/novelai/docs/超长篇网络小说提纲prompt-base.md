# 优化后的超长篇网络小说提纲提示符

**提示符：生成详细的超长篇网络小说提纲**

请为一部约 [word_count] 字（通常超过100万字，300-400章，每章约2500-3500字）的 [genre] 类型超长篇网络小说生成一份详细提纲，面向 [target_audience]（通常为喜爱复杂剧情和长期连载的网络小说读者），融入模块化叙事、多层次弧线和节奏控制，确保故事连贯性、吸引力和读者黏性。提纲需包含以下要素，并可根据 [genre_features]（如修真体系、未来科技等，参考外部特征文件）灵活定制：

#### 1. 故事概述
- 建议小说的暂定标题，符合 [genre] 的氛围（如仙侠小说用“天霄录”，科幻小说用“星际流亡”）。
- 明确类型（[genre]，可选融合 [additional_elements]，如仙侠+穿越）。
- 定义基调和风格（[tone]，如恢弘热血、细腻悬疑，融入 [style]，如古风或科技感）。
- 用2-3句话概括核心主线（如“凡人少年获上古仙器，逆天改命，对抗仙魔势力”）。
- 确定核心主题（如自我超越、爱与牺牲、自由与天道）。
- 明确目标读者（如仙侠爱好者、科幻迷）。
- 设置预计字数（约 [word_count] 字，分为 [chapter_count] 章，每章约 [words_per_chapter] 字）。
- 选择叙述视角（[perspective]，如第三人称有限视角，主角为主，辅以关键配角）。
- 概述主要冲突：
  - 外部：主角面对 [external_conflict]（如敌对势力、终极反派）。
  - 内部：主角挣扎于 [internal_conflict]（如心魔、道德抉择）。
- 提供5-8句话的故事概述，涵盖开端、中段和结局，突出主线目标和终极结局方向。

#### 2. 世界观设定
- **分层展开**：描述设定（[world_setting]，如凡人界与仙界、未来星际社会），初期展示基础设定，逐步揭示深层秘密（如隐藏势力、宇宙规则）。
- **类型特定体系**：详述 [supernatural/social/technological_system]（如修真境界、星际科技），包括 [specific_mechanics]（如灵根、量子核心）。
- **关键派系**：列出主要势力或组织（[faction_1]、[faction_2]等，如宗门、星际联盟），突出其文化和动机。
- **历史与文化**：概述世界历史（[historical_events]，如万年仙魔大战）与文化（[cultural_traits]，如尊师重道、科技崇拜）。
- **核心元素与规则**：定义核心物品（[core_item]，如上古仙器、量子装置）与世界规则（[world_rules]，如天道、熵增法则），并设计动态变化（如势力更迭、世界重塑）。
- **多样性与探索感**：融入多样的文化、种族或星域，增强世界深度和冒险吸引力。

#### 3. 人物设计
- **主角**：塑造主角（姓名、背景、性格、[genre_specific_traits]，如灵根或科技专长；动机；从弱小到传奇的成长弧线）。
- **关键配角**：描述主要配角（[supporting_character_type]，如盟友、反派、导师），为每人设计独立动机和故事弧。
- **其他角色**：加入推动剧情的次要角色（如敌对势力的喽啰、神秘使者）。
- **关系网**：提供人物关系图（[relationship_description]，如主角与红颜的悲剧爱情、与盟友的兄弟情），突出情感张力和冲突（如背叛、误会）。

#### 4. 情节框架（模块化叙事与三层递进）
将小说分为三个大阶段，每个阶段包含多个故事弧（约10万-30万字），逐步提升冲突规模和深度，结合主线弧、支线弧、个人弧和事件弧。

- **第一阶段：世界引入与角色建立**（约20%-30%篇幅，[word_count*0.25] 字，[chapter_count*0.25] 章）：
  - **目标**：介绍世界观、主角及核心角色，吸引读者入坑。
  - **内容**：
    - 通过主角视角逐步展示世界观（地理、势力、文化、规则）。
    - 塑造主角初始动机（[motivation]，如复仇、生存）与性格特征。
    - 引入第一个故事弧的冲突（[arc_conflict]，如对抗小规模敌人）。
    - 埋下主线伏笔（[foreshadowing]，如神秘预言、隐藏敌人）。
  - **节奏**：以轻松或中等强度的冒险为主，穿插日常、幽默或成长元素，每10章一个小高潮。
  - **转折点**：定义激发事件（如获得核心物品、暴露身份）。
  - **情感基调**：如希望与挣扎交织。

- **第二阶段：冲突升级与世界扩展**（约40%-50%篇幅，[word_count*0.45] 字，[chapter_count*0.45] 章）：
  - **目标**：加深世界观，拓展角色关系，引入复杂冲突。
  - **内容**：
    - 主角面对更强敌人或挑战（[arc_conflict]，如加入势力、对抗区域反派）。
    - 发展支线故事，展示配角背景和动机（[supporting_arc]）。
    - 揭示主线部分真相（[main_plot_reveal]，如反派目的、世界秘密）。
    - 通过多个故事弧（3-5个）推动主角成长（[growth_milestone]，如新能力、团队组建）。
  - **节奏**：高强度冲突（战斗、阴谋）与低强度情节（日常、感情线）交替，每10-15章一个小高潮，每50章一个大转折。
  - **转折点**：定义重大事件（如盟友背叛、揭开阴谋）。
  - **情感基调**：如热血与悲壮。

- **第三阶段：高潮与收尾**（约20%-30%篇幅，[word_count*0.25] 字，[chapter_count*0.25] 章）：
  - **目标**：解决主线冲突，完成角色弧，达成故事高潮。
  - **内容**：
    - 主角面对终极反派或挑战（[final_conflict]），解决所有主线伏笔。
    - 支线故事收拢，与主线融合。
    - 展现主角及配角的最终成长或牺牲（[character_resolution]）。
    - 提供满足的结局（[ending_type]，如封闭式或开放式续作空间）。
  - **节奏**：以高强度冲突为主，辅以情感高潮和世界观最终揭示，每10章一个小高潮，每30章一个大高潮。
  - **转折点**：定义最终转折（如主角从个人目标转向宏大理想）。
  - **情感基调**：如悲壮与希望并存。

#### 5. 章节与小单元规划
- **前10章规划**（每章约 [words_per_chapter] 字）：
  - **场景**：具体设定（[chapter_setting]，如宗门、未来都市）。
  - **事件**：关键事件（[chapter_events]，如引子事件、结识角色、早期挑战）。
  - **人物**：涉及角色（主角、配角）。
  - **伏笔**：埋下后续剧情线索（[foreshadowing]，如宝物来历、反派暗示）。
- **后续章节**（11-[chapter_count]章）：
  - 每 [small_arc_chapters] 章（建议5-10章）形成一个小型故事弧，围绕子目标或事件（如完成任务、击败次级敌人）。
  - 每 [major_arc_chapters] 章（建议30-50章）完成一个完整故事弧，推动主线进展。
  - 确保节奏均衡，交替动作、情感和世界观揭示。

#### 6. 主题与象征
- 明确核心主题（[theme]，如自我超越、权力与牺牲）。
- 突出象征元素（[symbolism]，如仙器象征命运、量子核心象征希望）。
- 概述情感主线（[emotional_throughline]，如悲剧爱情、兄弟情深）。

#### 7. 其他要素
- **伏笔与线索**：列出关键伏笔（[foreshadowing_list]，如反派身份、世界真相），确保在合理时间内回收。
- **节奏控制**：
  - 短期：每章提供小型冲突或悬念（[chapter_hook]，如新线索、危机）。
  - 中期：每10-15章一个小高潮（如战斗、揭秘），推动子目标。
  - 长期：每50-100章一个大高潮，推进主线或揭示重大转折。
- **类型特定特征**（参考 [genre_features]，如修真体系、未来科技）：
  - **描述**：生动刻画 [description_type]（如仙山云海、星际战场）。
  - **情节**：融入 [plot_type]（如秘境探险、太空战争）。
  - **情感**：突出 [emotion_type]（如师徒情、宇宙孤独感）。
- **写作风格**：推荐 [style_description]（如古风典雅、科技感冷峻），注重画面感和情感余韵。

#### 8. 写作建议
- **准备工作**：使用工具（如Notion、Scrivener）整理故事弧、角色关系和时间线；细化 [genre_specific_system] 和世界观地图。
- **节奏策略**：初期以悬念和代入感吸引读者，中期平衡冲突与日常，后期收束伏笔，保持高潮冲击力。
- **灵活性**：根据连载反馈调整支线或次要角色，保持主线稳定。
- **读者期待**：设计爽点（[refresh_point]，如逆袭、能力突破）每10章一次；避免拖沓，确保代入感；融入 [genre] 特色（如仙侠的飞升、科幻的科技震撼）。

**输出格式**：
- 以详细的Markdown文档呈现提纲，分段清晰，结构完整。
- 使用符合 [genre] 氛围的命名（[naming_style]，如天霄宗、星际联盟）。
- 平衡动作、情感和世界观，体现 [genre] 的精神，融入模块化叙事和多层次弧线。
- 整个提纲包裹在单个 `<xaiArtifact>` 标签中，设置唯一 UUID 作为 `artifact_id`，标题为“超长篇网络小说提纲”，`contentType="text/markdown"`。

**附加说明**：
- 参考经典 [genre] 元素（[genre_examples]，如《凡人修仙传》），加入独特创意（[unique_twist]，如复杂反派、非常规宝物）。
- 确保 [genre_specific_system] 逻辑一致，包含独特亮点。
- 避免通俗剧情或角色，定期引入反转（[plot_twist]，如背叛、世界真相）。
- 可根据 [genre_features]（如修真体系、未来科技）定制，参考外部特征文件。
- 为超长篇设计，强调模块化故事弧（每个弧10万-30万字）、多层次叙事（主线、支线、个人弧、事件弧）和读者黏性（悬念、爽点）。