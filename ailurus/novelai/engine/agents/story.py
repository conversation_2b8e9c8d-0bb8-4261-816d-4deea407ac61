"""agent to conceive a story"""

from typing import Any, AsyncGenerator

import pyllm
from novelai.engine.agents.base import NovelWritingConfig, NovelWritingAgent
from novelai.engine.consts import WebNovelLength, WEB_NOVEL_THEME_DICT
from novelai.engine.models.story import Story
from pycommon.logwrapper import get_log_wrapper

logger = get_log_wrapper(__name__)


class StoryAgentConfig(NovelWritingConfig):
    """config for StoryAgent"""

    name: str = "story-agent"
    less_themes: bool = True  # 是否使用较少的主题数, default True.


class StoryAgent(NovelWritingAgent):
    """为写小说构思一个故事的智能体。An agent to conceive a story for writing a novel"""

    config: StoryAgentConfig

    async def _async_run(self, **kwargs: Any) -> Story:
        user_prompt = self.get_user_prompt()
        prompts = (
            pyllm.ChatPrompts(output_format="json", output_schema=Story.as_schema(), output_lang=self.config.novel_lang)
            .set_system(self.config.sys_prompt)
            .set_inquery(user_prompt)
        )
        d = await self.async_invoke_llm(prompt=prompts.to_list(), resp_type="object")
        story = Story(
            **d,
            key=Story.generate_key(d["name"]),
            lang=self.config.novel_lang,
            author=self.config.author.name,
            tech_detail={
                "llm_model": self.config.llm_model,
                "llm_temperature": self.config.llm_temperature,
                "llm_output_tokens": self.config.llm_output_tokens,
                "llm_timeout": self.config.llm_timeout,
            },
        )
        await story.asave()

        # TODO: test - hack to change agent name
        self.config.name = f"story-agent-{story.name}"

        return story

    async def _async_iter(self, **kwargs: Any) -> AsyncGenerator:
        raise NotImplementedError

    def get_user_prompt(self) -> str:
        major_theme_count, minor_theme_count = self.get_theme_counts()

        return f"""这本小说你计划写约{self.config.words_plan}字，并有如下构思：

# 主题
{self.get_theme_prompt()}
你从中选择了{major_theme_count}个主要主题{'' if major_theme_count>1 else '交织'}贯通主线{'，'+str(minor_theme_count)+'个次要主题穿插其间' if minor_theme_count>0 else ''}。
经过认真的思考、设计，你已经确定了主题、背景、情节、冲突等。输出如下：
"""

    def get_theme_counts(self) -> tuple[int, int]:
        """Get major and minor counts of themes depending on the word count.

        Returns:
            tuple[int, int]: A tuple containing the counts of major themes and minor themes
        """
        less = self.config.less_themes
        if self.config.words_plan < WebNovelLength.NOVELLA.max_words:
            return (1, 0) if less else (1, 1)
        elif self.config.words_plan < WebNovelLength.ENTRY_LEVEL_NOVEL.max_words:
            return (1, 2) if less else (2, 4)
        elif self.config.words_plan < WebNovelLength.STANDARD_NOVEL.max_words:
            return (2, 3) if less else (3, 5)
        else:
            return (3, 5) if less else (4, 7)

    def get_theme_prompt(self) -> str:
        s = "现在流行的故事主题主要有如下这些（当然你也可以选择其他主题）：\n"
        for k, v in WEB_NOVEL_THEME_DICT.items():
            # s += f"- {k}：{v['desc']}。例如:{', '.join(v['examples'])}。流行度:{v['popularity']}/10\n"
            s += f"- {k}：{v['desc']}。流行度:{v['popularity']}/10\n"
        return s
