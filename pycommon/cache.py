import asyncio
from abc import abstractmethod
from datetime import datetime
from logging import get<PERSON>ogger
from typing import Self, Optional, Iterable
from typing import runtime_checkable, Protocol, Callable, Any

from asgiref.sync import async_to_sync

from common.utils.confutils import get_str_env
from common.utils.datetimeutils import LOCAL_TZ

logger = getLogger(__name__)

try:
    import aioredis
except ImportError:
    logger.error("RedisCache is not available due to `aioredis` is not installed.")


@runtime_checkable
class CacheStore(Protocol):
    """the abstracted interface for downloader cache"""

    @property
    @abstractmethod
    def default_expires(self) -> int:
        """default cache expire seconds"""

    @abstractmethod
    async def async_get(self, key: str) -> str | bytes | None:
        """get a value by key"""

    @abstractmethod
    async def async_set(self, key: str, value: str | bytes, expires: int | datetime | None = None) -> None:
        """set a value by key"""

    @abstractmethod
    async def async_set_if_not_exist(self, key: str, value: str | bytes, expires: int | datetime | None = None) -> None:
        """set a value by key if not exists"""

    @abstractmethod
    async def async_delete(self, *keys: str) -> None:
        """delete values by given keys"""

    @abstractmethod
    async def async_list(self, prefix: str) -> list:
        """list caches by prefix"""

    @abstractmethod
    async def async_exists(self, keys: Iterable[str]) -> dict[str, bool]:
        """check if the keys exists"""

    def get(self, key: str) -> str | bytes | None:
        """get a value by key"""
        return async_to_sync(self.async_get)(key)

    def set(self, key: str, value: str | bytes, expires: int | datetime | None = None) -> None:
        """set a value by key"""
        return async_to_sync(self.async_set)(key, value, expires)

    def set_if_not_exist(self, key: str, value: str | bytes, expires: int | datetime | None = None) -> None:
        """set a value by key if not exists"""
        return async_to_sync(self.async_set_if_not_exist)(key, value, expires)

    def delete(self, *keys: str) -> None:
        """delete values by given keys"""
        return async_to_sync(self.async_delete)(*keys)

    def list(self, prefix: str) -> list:
        """list caches by prefix"""
        return async_to_sync(self.async_list)(prefix)

    def exists(self, keys: Iterable[str]) -> dict[str, bool]:
        """check if the keys exists"""
        return async_to_sync(self.async_exists)(keys)

    def get_or_set(
        self,
        key: str,
        func: Callable,
        expires: int | datetime | None = None,
        *func_args: Any,
        **func_kwargs: Any,
    ) -> str:
        return async_to_sync(self.async_get_or_set)(key, func=func, expires=expires, *func_args, **func_kwargs)

    async def async_get_or_set(
        self,
        key: str,
        func: Callable,
        expires: int | datetime | None = None,
        *func_args: Any,
        **func_kwargs: Any,
    ) -> str:
        value = await self.async_get(key)
        if not value:
            if asyncio.iscoroutinefunction(func):
                value = await func(*func_args, **func_kwargs)
            else:
                value = func(*func_args, **func_kwargs)
            if value:
                await self.async_set(key, value, expires=expires)
        else:
            logger.warning(f"cache hit - {key}")
        return value


class RedisCache(CacheStore):
    _cli: aioredis.Redis | None = None

    def __init__(self, conn_url: str = get_str_env("SPIDER_CACHE_REDIS_URL")) -> None:
        self._cli = async_to_sync(aioredis.create_redis_pool)(conn_url)

    def __del__(self) -> None:
        self._cli.close()
        async_to_sync(self._cli.wait_closed)()

    @property
    def default_expires(self) -> int:
        return 3600

    async def async_get(self, key: str) -> str | bytes | None:
        return await self._cli.get(key)

    async def async_set(self, key: str, value: str | bytes, expires: int | datetime | None = None) -> None:
        if isinstance(expires, datetime):
            delta = expires - datetime.now(tz=LOCAL_TZ)
            if delta.total_seconds() <= 0:
                raise ValueError(f"{expires} is a past time.")
            expires = delta.total_seconds()

        return await self._cli.set(key, value, expire=expires)

    async def async_set_if_not_exist(self, key: str, value: str | bytes, expires: int | datetime | None = None) -> None:
        if isinstance(expires, datetime):
            delta = expires - datetime.now(tz=LOCAL_TZ)
            if delta.total_seconds() <= 0:
                raise ValueError(f"{expires} is a past time.")
            expires = delta.total_seconds()

        if await self._cli.setnx(key, value):
            await self._cli.expire(key, expires)

    async def async_delete(self, *keys: str) -> None:
        return await self._cli.delete(*keys)

    async def async_list(self, prefix: str) -> list:
        return await self._cli.keys(f"{prefix}*")

    async def async_exists(self, keys: Iterable[str]) -> dict[str, bool]:
        exists = await self._cli.exists(*keys)
        return dict(zip(keys, map(bool, exists)))


cache: CacheStore = RedisCache()


class CacheMixin:
    __is_loaded_from_cache: bool = False

    @property
    def cache_expires(self) -> int:
        """how many seconds will the cache expires. default 3600"""
        return cache.default_expires

    @property
    @abstractmethod
    def cache_key(self) -> str:
        """return object's cache key"""

    @classmethod
    @abstractmethod
    def gen_cache_key(cls, *args: str) -> str:
        """generate a cache key for given arguments"""

    @classmethod
    @abstractmethod
    def from_cache_str(cls, s: str | bytes) -> Self:
        """generate object from deserializing cache string"""

    @abstractmethod
    def to_cache_str(self) -> str:
        """serialize object to string for caching"""

    @classmethod
    async def async_cache_get(cls, cache_key: str, raise_if_none: bool = False) -> Optional[Self]:
        """load object from cache by key

        :param cache_key:
        :param raise_if_none:
        :return:
        """

        if not cache_key:
            if raise_if_none:
                raise ValueError("cache_key is required")
            return None

        try:
            s = await cache.async_get(cache_key)
            if s:
                obj = cls.from_cache_str(s)
                obj.__is_loaded_from_cache = True
            else:
                obj = None
                if raise_if_none:
                    raise ValueError(f"no cached object found for '{cache_key}' )")
            return obj
        except Exception as e:
            if isinstance(e, ValueError):
                raise
            logger.exception(e)
            return None

    async def async_cache_set(self, overwrite: bool = False) -> None:
        """save entry to cache by key"""
        try:
            cache_key = self.cache_key
            s = self.to_cache_str()
            if not self.__is_loaded_from_cache:
                if overwrite:
                    await cache.async_set(cache_key, s, self.cache_expires)
                else:
                    await cache.async_set_if_not_exist(cache_key, s, self.cache_expires)
                self.__is_loaded_from_cache = True
        except Exception as e:
            logger.exception(e)
