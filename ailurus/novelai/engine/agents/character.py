"""agent to design the characters of a story for writing a novel"""

from typing import Any, AsyncGenerator

import pyllm
from novelai.engine.agents.base import NovelWritingConfig, NovelWritingAgent
from novelai.engine.models.character import StoryCharacter
from novelai.engine.models.story import Story
from pycommon.logwrapper import get_log_wrapper

logger = get_log_wrapper(__name__)


class StoryCharacterAgentConfig(NovelWritingConfig):
    """config for StoryCharacterAgent"""

    name: str = "character-agent"
    story: Story
    count: int = -1  # 创建的角色数量，<=0 表示根据设定自动选择
    only_if_empty: bool = True  # generate worlds only if the story has no worlds
    llm_timeout: int = 150  # override timeout for long context


class StoryCharacterAgent(NovelWritingAgent):
    """设计小说角色的智能体。An agent to design the characters of a story for writing a novel"""

    config: StoryCharacterAgentConfig

    async def _async_run(self, **kwargs: Any) -> list[StoryCharacter]:
        """Create detailed character profiles for the story's main characters.

        This method generates detailed descriptions and backgrounds for each character
        mentioned in the story's initial conception, including their:
        - Personality traits
        - Physical appearance
        - Background/origin story
        - Motivations and goals
        - Relationships with other characters

        Returns:
            list[StoryCharacter]: A list of StoryCharacter objects containing the
            detailed character profiles
        """
        story = self.config.story
        if not story:
            raise ValueError("Story must be conceived before creating characters")

        # worlds prompts
        worlds = await story.async_get_worlds()
        worlds_prompts = "\n".join([f"## {w.name}\n{w.as_prompt(excludes={'story', 'name'})}" for w in worlds])

        # exited characters prompt
        existed_characters = await story.async_get_characters()
        if self.config.only_if_empty and len(existed_characters) > 0:
            logger.warning(
                f"skip. {story} has {len(existed_characters)} characters already. "
                "Set `StoryCharacterAgentConfig.only_if_empty=False` to create extra characters."
            )
            return []

        # Create prompt for existed characters
        if len(existed_characters) > 0:
            logger.debug(f"{story} has {len(existed_characters)} characters. generate extra characters.")
            char_prompt = "\n".join(
                [
                    f"## {code}\n{c.summary or c.as_prompt(includes=['level', 'aliases', 'bio', 'background', 'motivation'])}"
                    for code, c in existed_characters.items()
                ]
            )
        else:
            char_prompt = "无"

        user_prompt = (
            f"为小说'{story.name}'创建{str(self.config.count) + '个' if self.config.count > 0 else '数个(根据设定需要)'}详细人物设定。\n\n"
            f"# 故事设定及概览\n{story.as_prompt(excludes={'name'})}\n\n"
            f"# 世界设定\n{worlds_prompts}\n\n"
            f"# 已创建的人物(不要再创建)\n{char_prompt}\n\n"
            "经过认真的思考以及参考流行的网络小说，你根据小说框架、世界、已有人物，来创建数个还需要的角色，且已为每个角色创建详细的背景设定，如无必要，可以不创建。"
            "包括：性格特征、外貌描写、背景故事、动机目标、与其他角色的关系等等(描述中不要包含角色名字)。输出如下：\n"
        )

        # Generate character profiles using LLM
        prompts = (
            pyllm.ChatPrompts(
                output_format="json",
                output_schema=StoryCharacter.as_array_schema(excludes={"code"}),
                output_lang=self.config.novel_lang,
            )
            .set_system(self.config.sys_prompt)
            .set_inquery(user_prompt)
        )
        characters_data = await self.async_invoke_llm(prompt=prompts.to_list(), resp_type="object")

        # Create and save StoryCharacter objects
        characters = []
        for char_d in characters_data["array"]:
            char_name = char_d["name"]
            if not char_name:
                logger.warning("character name is empty.")
            char_code = f"$[{char_name}]$"
            char_code = self._handle_duplicate_code(char_code, existed_characters)
            char_key = StoryCharacter.generate_key(char_code, story.key)
            char_d.pop("story", None)
            character = StoryCharacter(
                story=self.config.story,
                key=char_key,
                code=char_code,
                tech_detail={
                    "llm_model": self.config.llm_model,
                    "llm_temperature": self.config.llm_temperature,
                    "llm_output_tokens": self.config.llm_output_tokens,
                    "llm_timeout": self.config.llm_timeout,
                },
                **char_d,
            )
            await character.asave()
            characters.append(character)

        return characters

    async def _async_iter(self, **kwargs: Any) -> AsyncGenerator:
        raise NotImplementedError

    def _handle_duplicate_code(self, code: str, existed_characters: dict[str, StoryCharacter]) -> str:
        num = 2
        if code not in existed_characters:
            return code

        name = code.removeprefix("$[").removesuffix("]$")

        for c in existed_characters.keys():
            n = c.removeprefix("$[").removesuffix("]$")
            if n.startswith(f"{name}-"):
                nparts = n.split("-")
                if len(nparts) == 2:
                    try:
                        num = int(nparts[1])
                        if nparts[1] != str(num):
                            num += 1
                            break
                        else:
                            num = 2
                    except ValueError:
                        pass

        code = f"$[{name}-{num}]$"
        return code
