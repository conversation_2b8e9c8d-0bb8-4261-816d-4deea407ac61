"""agent to design the world building of a story for writing a novel"""

from typing import Any, AsyncGenerator

import pyllm
from novelai.engine.agents.base import NovelWritingConfig, NovelWritingAgent
from novelai.engine.models.story import Story
from novelai.engine.models.world import StoryWorld
from pycommon.logwrapper import get_log_wrapper

logger = get_log_wrapper(__name__)


class StoryWorldAgentConfig(NovelWritingConfig):
    """config for StoryWorldAgent"""

    name: str = "world-agent"
    story: Story
    only_if_empty: bool = True  # generate worlds only if the story has no worlds


# ==========   Worlds Schema/Settings Design (for a novel)   ==========


class StoryWorldAgent(NovelWritingAgent):
    """设计小说世界观的智能体。An agent to design the world building of a story for writing a novel"""

    config: StoryWorldAgentConfig

    async def _async_run(self, **kwargs: Any) -> list[StoryWorld]:
        story = self.config.story
        if not story:
            raise ValueError("Story must be conceived before designing outline")

        overview_prompt = story.as_prompt()

        # worlds prompts
        worlds = await story.async_get_worlds()
        if self.config.only_if_empty and len(worlds) > 0:
            logger.warning(
                f"skip. {story} has {len(worlds)} worlds already. "
                "Set `StoryWorldAgentConfig.only_if_empty=False` to create extra worlds."
            )
            return []

        if len(worlds) > 0:
            logger.debug(f"{story} has {len(worlds)} worlds. generate extra worlds.")
            worlds_prompts = "\n\n".join([f"## {w.name}\n{w.summary or w.as_prompt()}" for w in worlds])
        else:
            worlds_prompts = "无"

        detail_prompt = f"""- **分层展开**：描述设定（[world_setting]，如凡人界与仙界、未来星际社会），初期展示基础设定，逐步揭示深层秘密（如隐藏势力、宇宙规则）。
- **类型特定体系**：详述 [supernatural/social/technological_system]（如修真境界、星际科技），包括 [specific_mechanics]（如灵根、量子核心）。
- **关键派系**：列出主要势力或组织（[faction_1]、[faction_2]等，如宗门、星际联盟），突出其文化和动机。
- **历史与文化**：概述世界历史（[historical_events]，如万年仙魔大战）与文化（[cultural_traits]，如尊师重道、科技崇拜）。
- **核心元素与规则**：定义核心物品（[core_item]，如上古仙器、量子装置）与世界规则（[world_rules]，如天道、熵增法则），并设计动态变化（如势力更迭、世界重塑）。
- **多样性与探索感**：融入多样的文化、种族或星域，增强世界深度和冒险吸引力。

"""
        format_prompt = f"""- 使用符合 {story.genres} 氛围的命名和术语。
- 平衡动作、情感和世界观，体现 {story.genres} 的精神。"""
        user_prompt = (
            "\n\n"
            f"# 故事设定\n{story.as_prompt()}\n\n"
            f"# 故事概览\n{overview_prompt}\n\n"
            f"# 已有的世界设定（不要再生成类似的)\n\n{worlds_prompts}\n\n"
            f"# 世界观设定\n根据以上'故事设定'、'故事概览' 等，符合故事类型、主题、背景以及长度({story.length_of_story.desc})，生成1到多个详细世界观(**已有的世界不要再生成相似的**)，各世界包含以下要素：\n{detail_prompt}\n\n"
            f"# 格式\n{format_prompt}\n\n"
            "输出如下：\n"
        )

        prompts = (
            pyllm.ChatPrompts(
                output_format="json", output_schema=StoryWorld.as_array_schema(), output_lang=self.config.novel_lang
            )
            .set_system(self.config.sys_prompt)
            .set_inquery(user_prompt)
        )
        worlds_data = await self.async_invoke_llm(prompt=prompts.to_list(), resp_type="object")

        # Create and save StoryWorld objects
        worlds = []
        for d in worlds_data["array"]:
            d["story"] = story
            world = StoryWorld(
                tech_detail={
                    "llm_model": self.config.llm_model,
                    "llm_temperature": self.config.llm_temperature,
                    "llm_output_tokens": self.config.llm_output_tokens,
                    "llm_timeout": self.config.llm_timeout,
                },
                **d,
            )
            await world.asave()
            worlds.append(world)

        return worlds

    async def _async_iter(self, **kwargs: Any) -> AsyncGenerator:
        raise NotImplementedError
