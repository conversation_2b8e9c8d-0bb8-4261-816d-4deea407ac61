"""log wrapper"""

import json
import logging
import logging.config
import os
import traceback
from enum import IntEnum, StrEnum
from pathlib import Path
from typing import Dict, Any, Literal, Sequence, cast

from pycommon.config import APP_NAME, DEBUG, LOG_DIR, APP_PREFIX
from .utils.confutils import get_str_env
from .utils.terminal import debug, tip

__INITIALIZED__ = False

LOG_PREFIX: str = APP_PREFIX.lower()

SUPPORTED_LOG_SDKS = Literal["python", "logfire"]
LOG_SDK: SUPPORTED_LOG_SDKS = cast(SUPPORTED_LOG_SDKS, get_str_env("LOG_SDK", "python"))
LOGFIRE_TOKEN = get_str_env("LOGFIRE_TOKEN")

LOG_COLOR_DEBUG = get_str_env("LOG_COLOR_DEBUG", "thin_white")
LOG_COLOR_INFO = get_str_env("LOG_COLOR_INFO", "white")
LOG_FORMATTER = get_str_env("LOG_FORMATTER", "colorful")
LOG_LEVEL = get_str_env("LOG_LEVEL", "INFO" if DEBUG else "WARNING")

# fmt: off
LOGGING_INFO_LEVEL_LIBS = [
    "django.db.backends",
]
LOGGING_WARN_LEVEL_LIBS = [
    "asyncio", "unicorn", "uvicorn", "httpx", "urllib3", "httpcore", "daphne",
    "openai", "LiteLLM"
]
# fmt: on

if LOG_SDK == "logfire":
    import logfire

    # 使用 logfire 的 ConsoleOptions 来自定义控制台输出格式
    logfire.configure(
        send_to_logfire="if-token-present",
        console=logfire.ConsoleOptions(
            colors="auto",
            span_style="show-parents",
            include_timestamps=True,
            include_tags=True,
            # verbose=True,  # 启用详细模式，显示更多信息如级别和行号
            min_log_level=LOG_LEVEL.lower(),
            show_project_link=True,
        ),
    )


class LogLevel(IntEnum):
    NOTSET = logging.NOTSET
    TRACE = logging.DEBUG - 5  # 5
    DEBUG = logging.DEBUG  # 10
    INFO = logging.INFO  # 20
    NOTICE = logging.INFO + 5  # 25
    WARNING = logging.WARNING  # 30
    WARN = logging.WARN  # 30
    ERROR = logging.ERROR  # 40
    CRITICAL = logging.CRITICAL  # 50
    FATAL = logging.FATAL  # 50


class LogLevelName(StrEnum):
    NOTSET = "notset"
    TRACE = "trace"
    DEBUG = "debug"
    INFO = "info"
    NOTICE = "notice"
    WARNING = "warning"
    WARN = "warn"
    ERROR = "error"
    CRITICAL = "critical"
    FATAL = "fatal"


log_name_to_level: dict = {
    LogLevelName.NOTSET: LogLevel.NOTSET,
    LogLevelName.TRACE: LogLevel.TRACE,
    LogLevelName.DEBUG: LogLevel.DEBUG,
    LogLevelName.INFO: LogLevel.INFO,
    LogLevelName.NOTICE: LogLevel.NOTICE,
    LogLevelName.WARNING: LogLevel.WARNING,
    LogLevelName.WARN: LogLevel.WARN,
    LogLevelName.ERROR: LogLevel.ERROR,
    LogLevelName.CRITICAL: LogLevel.CRITICAL,
    LogLevelName.FATAL: LogLevel.FATAL,
}
log_level_to_name: dict = {v: k for k, v in log_name_to_level.items()}


class LogWrapper:
    name: str
    tags: Sequence[str] = []
    level: LogLevel = LogLevel.INFO
    log_file: str | None = None
    log_sdk: SUPPORTED_LOG_SDKS = LOG_SDK
    _logger: Any = None

    def __init__(
        self,
        name: str,
        tags: Sequence[str] | None = None,
        level: LogLevel | LogLevelName | int | str = "info",
        log_file: str | None = None,
        log_sdk: SUPPORTED_LOG_SDKS = LOG_SDK,
        **kwargs: Any,
    ):
        parts = name.split(".")
        if len(parts) > 2:
            name = ".".join(parts[:-1])
        self.name = name
        self.tags = tags
        self.level = LogLevel(level) if isinstance(level, (LogLevel, int)) else log_name_to_level[LogLevelName(level)]
        self.log_file = log_file
        self.log_sdk = log_sdk

        self._setup_loggers()

    def _setup_loggers(self):
        """设置各种日志记录器"""

        if self.log_sdk == "logfire":
            console_log = os.getenv("LOG_CONSOLE", "true").lower() in {"true", "yes", "on", "t", "y", "1"}
            tags = [t for t in [self.name, self.system] if t]
            # stack_offset not work. Keep it here. I raised issue at https://github.com/pydantic/logfire/issues/1326
            self._logger = logfire.with_settings(tags=tags, console_log=console_log, stack_offset=2)
        else:
            self._logger = logging.getLogger(self.name)

    def _log(
        self,
        level: LogLevel,
        message: str,
        *,
        metas: Dict[str, Any] | None = None,
        tags: Sequence[str] | None = None,
        **kwargs: Any,
    ):
        if level < self.level:
            return

        if metas is None:
            metas = {}

        if self.log_sdk == "logfire":
            # logfire支持metadata, attributes, tags
            lvl_name = log_level_to_name[level].value
            logfire_name = "fatal" if lvl_name == "critical" else lvl_name
            exc_info = True if level >= LogLevel.ERROR else False
            self._logger.log(level=logfire_name, msg_template=message, attributes=metas, tags=tags, exc_info=exc_info)
        else:
            d = {
                "message": message,
                "tags": tags,
                **metas,
            }
            s = json.dumps(d, default=str, ensure_ascii=False)
            # 使用stacklevel=3跳过包装器调用栈: _log -> info/debug等方法 -> 实际调用者
            self._logger.log(level, s, stacklevel=3)

    def trace(self, message: str, *, metas: Dict[str, Any] | None = None, tags: Sequence[str] | None = None):
        """记录TRACE级别日志"""
        self._log(LogLevel.TRACE, message, metas=metas, tags=tags)

    def debug(self, message: str, *, metas: Dict[str, Any] | None = None, tags: Sequence[str] | None = None):
        """记录DEBUG级别日志"""
        self._log(LogLevel.DEBUG, message, metas=metas, tags=tags)

    def info(self, message: str, *, metas: Dict[str, Any] | None = None, tags: Sequence[str] | None = None):
        """记录INFO级别日志"""
        self._log(LogLevel.INFO, message, metas=metas, tags=tags)

    def notice(self, message: str, *, metas: Dict[str, Any] | None = None, tags: Sequence[str] | None = None):
        """记录NOTICE级别日志"""
        self._log(LogLevel.NOTICE, message, metas=metas, tags=tags)

    def warning(self, message: str, *, metas: Dict[str, Any] | None = None, tags: Sequence[str] | None = None):
        """记录WARNING级别日志"""
        self._log(LogLevel.WARNING, message, metas=metas, tags=tags)

    def error(self, message: str, *, metas: Dict[str, Any] | None = None, tags: Sequence[str] | None = None):
        """记录ERROR级别日志"""
        self._log(LogLevel.ERROR, message, metas=metas, tags=tags)

    def critical(self, message: str, *, metas: Dict[str, Any] | None = None, tags: Sequence[str] | None = None):
        """记录CRITICAL级别日志"""
        self._log(LogLevel.CRITICAL, message, metas=metas, tags=tags)

    def exception(
        self, message: str | Exception, *, metas: Dict[str, Any] | None = None, tags: Sequence[str] | None = None
    ):
        """记录异常信息，包含完整的堆栈跟踪

        Args:
            message: 异常消息
            metas: 元数据字典
            tags: 标签列表
        """
        # 获取当前异常信息
        exc_info = traceback.format_exc()

        # 将异常堆栈信息添加到元数据中
        exception_metadata = metas.copy() if metas else {}
        exception_metadata["exception_traceback"] = exc_info

        if self.log_sdk == "logfire":
            # logfire有内置的异常处理，直接使用exc_info=True
            lvl_name = log_level_to_name[LogLevel.ERROR].value
            self._logger.log(
                level=lvl_name, msg_template=message, attributes=exception_metadata, tags=tags, exc_info=True
            )
        else:
            # python logging：使用ERROR级别记录异常
            if isinstance(message, Exception):
                self._logger.exception(message, stacklevel=3)
            else:
                self._logger.error(str(message), stacklevel=3)
            # d = {
            #     "message": message,
            #     "tags": tags,
            #     **exception_metadata,
            # }
            # s = json.dumps(d, default=str, ensure_ascii=False)
            # # 使用stacklevel=3跳过包装器调用栈，不使用exc_info避免重复输出
            # self._logger.error(s, stacklevel=3)


def get_log_wrapper(
    name: str,
    tags: Sequence[str] | None = None,
    level: LogLevel | LogLevelName | int | str = "info",
    log_file: str | None = None,
) -> LogWrapper:
    return LogWrapper(name, tags, level, log_file)


def getLogger(name: str) -> LogWrapper:
    name = name.lower()
    if LOG_PREFIX:
        if not name.startswith(f"{LOG_PREFIX}."):
            name = f"{LOG_PREFIX}.{name}"

    return get_log_wrapper(name)


def config_logging(fname: str = APP_NAME, prefix: str = f"{LOG_PREFIX}", level: str = LOG_LEVEL):
    global __INITIALIZED__

    if __INITIALIZED__:
        return

    if prefix:
        p = Path(f"{LOG_DIR}") / ".".join([prefix, fname, "log"])
    else:
        p = Path(f"{LOG_DIR}") / (fname + ".log")

    logging_conf_d = {
        "version": 1,
        "disable_existing_loggers": True,
        "formatters": {
            "standard": {
                "format": "[%(levelname).1s|%(asctime)s|%(name)s:%(lineno)d] %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "colorful": {
                "format": "%(log_color)s[%(asctime)s] %(levelname).1s [%(threadName).10s][%(name)s:%(lineno)d] %(message)s",
                "()": "colorlog.ColoredFormatter",
                "reset": True,
                "log_colors": {
                    "DEBUG": LOG_COLOR_DEBUG,
                    "INFO": LOG_COLOR_INFO,
                    "WARNING": "yellow",
                    "ERROR": "red",
                    "CRITICAL": "red,bg_white",
                },
                "secondary_log_colors": {},
            },
            "simple": {
                "format": "[%(levelname)s]%(asctime)s|%(name)s: %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "formatter": LOG_FORMATTER,
            },
            "logfire": {
                "class": "logfire.LogfireLoggingHandler",
                # "logfire_instance": logfire.with_tags(sys.argv[1]) if len(sys.argv) > 0 else None,
                "formatter": LOG_FORMATTER,
            },
        },
        "root": {
            "handlers": ["console"],
            "level": LOG_LEVEL,
        },
        "loggers": {
            LOG_PREFIX: {
                "handlers": ["console"],
                "level": level,
            },
            **{name: {"level": "INFO"} for name in LOGGING_INFO_LEVEL_LIBS},
            **{name: {"level": "WARNING"} for name in LOGGING_WARN_LEVEL_LIBS},
        },
    }

    if fname:
        try:
            p.parent.mkdir(exist_ok=True)
            p.touch()
            debug(f"log file created at {p}")
            logging_conf_d["file_rotate"] = {
                "class": "logging.handlers.RotatingFileHandler",
                "formatter": "verbose",
                "filename": str(p.absolute()),
                "maxBytes": 1048576 * 20,
                "backupCount": 10,
                "level": "DEBUG",
            }
            logging_conf_d["loggers"][LOG_PREFIX]["handlers"].append("file_rotate")
            tip(f"rotated file logging handler added. {p}")
        except:
            pass

    logging.config.dictConfig(logging_conf_d)

    tip(f"-----  logging configured. SDK:{LOG_SDK} LEVEL:{level} {'FILE:'+str(p) if fname else ''} -----")
    __INITIALIZED__ = True


config_logging()
