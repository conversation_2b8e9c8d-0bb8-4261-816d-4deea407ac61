import json
from typing import Iterable

from django.db import models

from djcommon.model import DictM<PERSON>in, CreateTimeMixin, UpdateTimeMixin
from novelai.engine.base import PromptAble, SchemaAble


class TechDetailMixin(models.Model):
    """Mixin to add tech details to a model"""

    tech_detail = models.JSONField(default=dict, blank=True, help_text="technical details.")

    class Meta:
        abstract = True


class NovelModelBase(
    DictMixin, CreateTimeMixin, UpdateTimeMixin, TechDetailMixin, PromptAble, SchemaAble, models.Model
):
    """abstract base model for all Django models which will be used as LLM Agent model"""

    @classmethod
    def schema_ignored_fields(cls) -> set[str]:
        """field-names set to be ignored when generating schema"""
        return {"id", "key", "created_at", "updated_at", "lang", "tech_detail"}

    @classmethod
    def prompt_ignored_fields(cls) -> set[str]:
        """field-names set to be ignored when generating prompt"""
        return {"id", "key", "created_at", "updated_at", "lang", "tech_detail", "summary"}

    @classmethod
    def as_schema(cls, *, includes: Iterable[str] = (), excludes: Iterable[str] = ()) -> dict:
        """return model's schema for LLM Agent use as JSON schema"""
        schema = {"type": "object", "properties": {}, "required": []}
        includes = set(includes)
        excludes = cls.schema_ignored_fields() | set(excludes)

        fields = cls._meta.fields
        for field in fields:
            if field.name in excludes or (includes and field.name not in includes):
                continue

            field_schema = {"type": cls._get_field_type(field), "description": field.help_text or ""}

            if field.max_length:
                field_schema["maxLength"] = field.max_length

            if field.choices:
                field_schema["enum"] = [k for k, v in field.choices]

            schema["properties"][field.name] = field_schema
            schema["required"].append(field.name)

            # if not field.null and not field.blank:
            #     schema["required"].append(field.name)

        # TODO: test - hack to make all field required (not standard)
        schema["required"] = "*"
        return schema

    @classmethod
    def as_schema_str(cls, *, includes: Iterable[str] = (), excludes: Iterable[str] = ()) -> str:
        schema = cls.as_schema(includes=includes, excludes=excludes)
        return json.dumps(schema, indent=2, ensure_ascii=False)

    @classmethod
    def as_array_schema(cls, *, includes: Iterable[str] = (), excludes: Iterable[str] = ()) -> dict:
        schema = {
            "type": "object",
            "properties": {"array": {"type": "array", "items": cls.as_schema(includes=includes, excludes=excludes)}},
            "required": ["array"],
        }
        return schema

    @classmethod
    def as_array_schema_str(cls, *, includes: Iterable[str] = (), excludes: Iterable[str] = ()) -> str:
        schema = cls.as_array_schema(includes=includes, excludes=excludes)
        return json.dumps(schema, indent=2, ensure_ascii=False)

    def as_prompt(self, *, includes: Iterable[str] = (), excludes: Iterable[str] = ()) -> str:
        """return model as a LLM prompt

        args:
            includes: list of field-names to include
            excludes: list of field-names to exclude
        """

        excludes = self.schema_ignored_fields() | set(excludes)
        includes = set(includes)

        prompt_parts = []
        fields = self._meta.fields
        for field in fields:
            if field.name in excludes or (includes and field.name not in includes):
                continue

            value = getattr(self, field.name)
            if value:
                if "\n" in str(value):
                    value = str(value).replace("\n", "\\n")
                prompt_parts.append(f"- {field.name}: {value}")

        return "\n".join(prompt_parts)

    @classmethod
    def _get_field_type(cls, field: models.Field) -> str:
        """Helper method to convert Django field types to JSON schema types"""
        if isinstance(field, (models.CharField, models.TextField)):
            return "string"
        elif isinstance(field, models.IntegerField):
            return "integer"
        elif isinstance(field, models.FloatField):
            return "number"
        elif isinstance(field, models.BooleanField):
            return "boolean"
        elif isinstance(field, models.DateTimeField):
            return "string"
        elif isinstance(field, models.JSONField):
            if field.default == list:
                return "array"
            else:
                return "object"
        else:
            return "string"

    class Meta:
        abstract = True


class AgentStat(DictMixin, CreateTimeMixin):
    """States of an agent's single run for debugging"""

    agent = models.CharField(max_length=250)
    config = models.JSONField(default=dict, blank=True)
    perf = models.JSONField(default=dict, blank=True)
    inputs = models.JSONField(default=dict, blank=True)
    outputs = models.JSONField(default=dict, blank=True)
    errors = models.JSONField(default=dict, blank=True)
    llm_cost = models.JSONField(default=dict, blank=True)
    llm_prompts = models.JSONField(default=dict)
    llm_outputs = models.JSONField(default=dict)
    extra_data = models.JSONField(default=dict, blank=True)

    def __str__(self) -> str:
        return f"{self.agent} {self.inputs} {self.created_at_str}"
