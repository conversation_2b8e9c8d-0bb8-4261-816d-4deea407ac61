from django import template
from django.utils.safestring import mark_safe

register = template.Library()

supported_fonts = ("fa", "mi")
fa_styles = {
    "fa": "fa-regular",
    "far": "fa-regular",
    "fas": "fa-solid",
    "fal": "fa-light",
    "fab": "fa-brand",
    "fat": "fa-thin",
}
mi_styles = {"mi": "mi"}
supported_styles = [k for k in fa_styles.keys()] + [k for k in mi_styles.keys()]


@register.simple_tag(name="icon", takes_context=True)
def icon(context: dict, symbol: str, font: str = "") -> str:
    """Use {% fa car %} or {% mi ball %} style to show font-icon in Django template"""

    if not font:
        font = (
            context.get("consts", context.get("conf", context))
            .get("ICON_FONT", "")
            .lower()
        )

    symbol_dict = {}
    symbol_with_fonts = symbol.split(",")
    if len(symbol_with_fonts) > 1:
        for pair in symbol_with_fonts:
            try:
                pair = pair.lower()
                name, style = pair.split(":")
            except:
                raise SyntaxError(f'Invalid icon symbol format "{symbol}".')
            if style not in supported_styles:
                raise ValueError(f'Unsupport font style "{style}".')
            if style in mi_styles:
                symbol_dict["mi"] = name
            else:
                symbol_dict["fa"] = f"fa-{fa_styles[style]} fa-{name}"

    if font in ("mi", "material", "material icon", "material-icon", "material+icon"):
        symbol = symbol_dict["mi"] if "mi" in symbol_dict else symbol
        html = f'<i class="material-icons">{symbol}</i>'
    else:
        symbol = symbol_dict["fa"] if "fa" in symbol_dict else symbol
        html = f'<i class="fa {symbol}"></i>'

    return mark_safe(html)
