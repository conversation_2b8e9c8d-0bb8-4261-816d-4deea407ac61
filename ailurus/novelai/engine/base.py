from abc import abstractmethod
from typing import Iterable


class PromptAble:
    """interface for objects that can be converted to prompt for LLM"""

    @abstractmethod
    def as_prompt(self, includes: Iterable[str] = (), excludes: Iterable[str] = ()) -> str:
        """return object as a LLM prompt"""


class SchemaAble:
    """interface for objects that can be converted to JSON schema"""

    @classmethod
    @abstractmethod
    def as_schema(cls) -> dict:
        """return object's JSON schema"""

    @classmethod
    @abstractmethod
    def as_schema_str(cls) -> str:
        """return object's JSON schema as string"""

    @classmethod
    @abstractmethod
    def as_array_schema(cls) -> list:
        """return JSON schema for an array of objects"""

    @classmethod
    @abstractmethod
    def as_array_schema_str(cls) -> str:
        """return JSON schema for an array of models as string"""
