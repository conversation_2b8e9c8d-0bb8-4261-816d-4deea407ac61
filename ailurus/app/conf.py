from pathlib import Path

from pycommon.utils.confutils import get_bool_env, get_int_env, get_str_env

# ====================   general   ====================

WORKSPACE_DIR = Path(__file__).resolve().parent.parent
BASE_DIR = WORKSPACE_DIR

DEBUG = get_bool_env("DEBUG", False)
VERBOSE = get_int_env("VERBOSE", 0)


# ====================   API keys   ====================

GEMINI_API_KEY = get_str_env("GEMINI_API_KEY")

TELEGRAM_API_TOKEN = get_str_env("TELEGRAM_API_TOKEN")
