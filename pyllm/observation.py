from logging import getLogger

import litellm
from litellm import CustomLogger, completion_cost
from litellm.litellm_core_utils.streaming_handler import CustomStreamWrapper
from litellm.types.utils import ModelResponse
from pydantic import BaseModel

from pycommon.observation import ObserverManager, LogObserver, Observer, Notification, CostObserver

logger = getLogger(__name__)


class LitellmLogHandler(CustomLogger):
    """handler for litellm callbacks"""

    # def log_pre_api_call(self, model, messages, kwargs):
    #     print(f"Pre-API Call")
    #
    # def log_post_api_call(self, kwargs, response_obj, start_time, end_time):
    #     print(f"Post-API Call")
    #
    # def log_success_event(self, kwargs, response_obj, start_time, end_time):
    #     print(f"On Success")
    #
    # def log_failure_event(self, kwargs, response_obj, start_time, end_time):
    #     print(f"On Failure")

    #### ASYNC #### - for acompletion/aembeddings

    async def async_log_success_event(self, kwargs, response_obj, start_time, end_time):
        print(f"On Async Success")

    async def async_log_failure_event(self, kwargs, response_obj, start_time, end_time):
        print(f"On Async Failure")


class LLMObserverManger(ObserverManager):
    _litellm_log_handler: LitellmLogHandler | None = None

    def register_observer(self, *observers: Observer) -> None:
        for ob in observers:
            if isinstance(ob, LitellmLogTracker):
                self._litellm_log_handler = LitellmLogHandler()
                litellm.callbacks.append(ob.litellm_log_handler)
        super().register_observer(*observers)


class LitellmLogTracker(LogObserver):
    """observer for litellm log as well as cost"""

    def _observe(self, note: Notification) -> None:
        pass


class LlmCostTracker(CostObserver):
    """home-made cost tracker (observer) for liteLLM"""

    def _observe(self, note: Notification) -> None:
        # ref: https://docs.litellm.ai/docs/completion/token_usage

        # example from gemini-2.0-flash / litellm
        """
        {
            "completion_tokens": 17,
            "completion_tokens_details": None,
            "prompt_tokens": 94,
            "prompt_tokens_details": None,
            "total_tokens": 111,
        }
        """
        if isinstance(note.data, ModelResponse):
            usage_obj: BaseModel = note.data.get("usage")
            usage = usage_obj.model_dump()
        elif isinstance(note.data, dict):
            usage = note.data.get("usage")
        elif isinstance(note.data, CustomStreamWrapper):
            raise NotImplementedError("streaming cost is not implemented")
        else:
            return

        # pass model with provider to completion_cost incase it can not automatically find it
        from pyllm import model_to_provider  # avoid import error

        model = note.data.get("model")
        provider = model_to_provider.get(model)
        if provider:
            model = f"{provider.value}/{model}"

        cost = completion_cost(completion_response=note.data, model=model)
        d = {
            "input_tokens": usage["prompt_tokens"],
            "output_tokens": usage["completion_tokens"],
            "currency": "USD",
            "cost": cost,
            "cost_str": f"${float(cost):.10f}",
        }

        if self.host:
            self.host.bag["cost"] = d

        logger.info(f"usage: {d}")
