import asyncio
import logging
from typing import AsyncGenerator, Callable, Literal

from agent.observer import Observer
from common.services.google.search import serpapi_search_iter

from scraper.exceptions import ScraperError
from scraper.models import InstagramPost, InstagramProfile
from scraper.spiders.fetch_instagram_profile import async_fetch_profile
from scraper.utils.lambda_invoker import LambdaInvoker

logger = logging.getLogger(__name__)


def _parse_result(item: dict) -> InstagramProfile:
    """
    Parse an Instagram profile dictionary into an InstagramProfile object.
    """
    # Convert posts to InstagramPost objects if available
    recent_posts = []
    if "posts" in item:
        for post in item["posts"]:
            recent_posts.append(
                InstagramPost(
                    id=str(post["id"]),
                    pk=str(post["pk"]),
                    url=post["url"],
                    caption=post.get("caption", ""),
                    published_time_str=str(post.get("taken_at", 0)),
                    like_count=post.get("like_count", 0),
                    comment_count=post.get("comment_count", 0),
                    reshare_count=post.get("reshare_count", 0),
                    play_count=post.get("play_count", 0),
                )
            )

    return InstagramProfile(
        handle=item["handle"],
        full_name=item.get("full_name", ""),
        url=item["url"],
        biography=item.get("biography", ""),
        followers=item.get("followers", 0),
        following=item.get("following", 0),
        posts_count=item.get("posts_count", 0),
        avatar_url=item.get("avatar", ""),
        email=item.get("email"),
        recent_posts=recent_posts,
        relevant_post=recent_posts[0] if recent_posts else None,
    )


async def search_influencers_iter(  # noqa: C901
    observer: Observer,
    term: str,
    platform: Literal["tiktok", "instagram", "youtube"] = "instagram",
    max_results: int = 500,
    stop_callback: Callable[[str], bool] | None = None,
) -> AsyncGenerator[InstagramProfile, None]:
    if platform == "tiktok":
        raise NotImplementedError("Tiktok is not supported yet")
    elif platform == "instagram":
        suffix = '-inurl:"/p/" -inurl:"/reel/" -inurl:"/stories/" -inurl:"/explore/" -inurl:"/directory/"'
        query = f"site:instagram.com {term} {suffix}"
    elif platform == "youtube":
        raise NotImplementedError("Youtube is not supported yet")
    # TODO(ruiwang): make below run in concurrency
    async for item in serpapi_search_iter(query, max_results=max_results):
        if stop_callback and term:
            if asyncio.iscoroutinefunction(stop_callback):
                shall_stop = await stop_callback(term)
            else:
                shall_stop = stop_callback(term)
            if shall_stop:
                break
        await asyncio.sleep(0.02)
        try:
            if item:
                logger.info(f"Processing search result {item.get('link', '')}")
                with observer.timer("google_search_process_result"):
                    profile = await process_result(observer, item)
                if profile:
                    yield profile
        except Exception as e:
            observer.log_error(e, f"Error processing search result {item.get('link', '')}")
            continue


async def process_result(observer: Observer, item: dict) -> InstagramProfile | None:
    base_link = item["link"].split("?")[0].rstrip("/")
    assert base_link.startswith("https://www.instagram.com/")
    parts = base_link.rstrip("/").split("/")
    if len(parts) > 4:
        logger.warning(f"Invalid Instagram profile URL format: {item['link']}")
        base_link = "/".join(parts[:4])

    username = base_link.split("/")[-1]
    res = await async_fetch_profile(username)
    if not res:
        observer.log_error(ScraperError(f"Failed to fetch profile for {username}"))
        return None
    profile = _parse_result(res)
    if profile.avatar_url and profile.url:
        asyncio.create_task(LambdaInvoker.invoke_avatar_processor(avatar=profile.avatar_url, home_page=profile.url))
    return profile
