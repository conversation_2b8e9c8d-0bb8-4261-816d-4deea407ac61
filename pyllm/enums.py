from typing import Literal

from pycommon.utils.lang import BaseStrEnum


class ProviderEnum(BaseStrEnum):
    """Enum of all supported LLM providers"""

    OPENAI = "openai"
    GEMINI = "gemini"  # google
    XAI = "xai"  # twitter, grok
    DEEPSEEK = "deepseek"
    OLLAMA = "ollama"


class ModelNameEnum(BaseStrEnum):
    """Enum of all supported LLM model names."""

    # OpenAI
    GPT_4o_MINI = "gpt-4o-mini"  # use lowercase "o" for distinguish from 0 (zero)

    # Google
    GEMINI_20_FLASH_LITE = "gemini-2.0-flash-lite"
    GEMINI_25_FLASH_LITE = "gemini-2.5-flash-lite-preview-06-17"
    GEMINI_25_FLASH = "gemini-2.5-flash"

    # grok
    GROK_V2 = "grok-2-latest"
    GROK_V3 = "grok-3"
    GROK_V3_MINI = "grok-3-mini"
    GROK_V3_FAST = "grok-3-fast"
    GROK_V3_MINI_FAST = "grok-3-min-fast"

    # deepseek
    DEEPSEEK_V3 = "deepseek-chat"
    DEEPSEEK_R1 = "deepseek-reasoner"

    # ollama
    OLLAMA_LLAMA_2_UNCENSORED = "llama2-uncensored"
    OLLAMA_LLAMA_32_3B = "llama3.2:3b"
    OLLAMA_LLAMA_32_VISION = "llama3.2-vision"
    OLLAMA_DEEPSEEK_R1_8B = "deepseek-r1:8b"


model_to_provider = {
    # openai
    ModelNameEnum.GPT_4o_MINI: ProviderEnum.OPENAI,
    # google gemini
    ModelNameEnum.GEMINI_20_FLASH_LITE: ProviderEnum.GEMINI,
    # xAI grok
    ModelNameEnum.GROK_V2: ProviderEnum.XAI,
    # deepseek
    ModelNameEnum.DEEPSEEK_V3: ProviderEnum.DEEPSEEK,
    ModelNameEnum.DEEPSEEK_R1: ProviderEnum.DEEPSEEK,
    # ollama
    ModelNameEnum.OLLAMA_LLAMA_2_UNCENSORED: ProviderEnum.OLLAMA,
    ModelNameEnum.OLLAMA_LLAMA_32_3B: ProviderEnum.OLLAMA,
    ModelNameEnum.OLLAMA_LLAMA_32_VISION: ProviderEnum.OLLAMA,
    ModelNameEnum.OLLAMA_DEEPSEEK_R1_8B: ProviderEnum.OLLAMA,
}


class PromptRole(BaseStrEnum):

    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"


OutputFormat = Literal["text", "markdown", "json", "yaml"]
