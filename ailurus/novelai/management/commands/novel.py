import asyncio
import random
from textwrap import fill
from typing import Any, Optional, List

from django.core.management.base import BaseCommand, CommandError
from django.utils.translation import gettext as _
from prettytable import PrettyTable

from novelai.engine.agents.chapter import Story<PERSON>hapterAgentConfig, StoryChapterAgent
from novelai.engine.agents.character import <PERSON>CharacterAgentConfig, StoryCharacterAgent
from novelai.engine.agents.outline import StoryOutlineAgentConfig, StoryOutlineAgent
from novelai.engine.agents.story import StoryAgentConfig, StoryAgent
from novelai.engine.agents.world import StoryWorldAgentConfig, StoryWorldAgent
from novelai.engine.consts import WEB_NOVEL_GENRE_DICT
from novelai.engine.models.author import AUTHOR_DICT, Author
from novelai.engine.models.character import StoryCharacter
from novelai.engine.models.outline import StoryOutline
from novelai.engine.models.story import Story
from novelai.engine.models.world import StoryWorld
from pyagent.consts import LiteLlmModels
from pycommon.utils.confutils import get_str_env
from pycommon.utils.strutil import ellipse
from pycommon.utils.terminal import tip, error, debug, success, warn, info

LLM_MODEL = LiteLlmModels.DEFAULT
AUTHOR = "猫腻"


class Command(BaseCommand):
    """Command to operate novelai model elements.

    Note: all function names are combined by arguments <action> and <element>, so do not use plural form.
    """

    help = "write novel"

    # -----  story  -----
    async def async_create_story(self, **kwargs: Any) -> Optional[Story]:

        # get author from kwargs, if not provided, list for user to choose, if not chosen, use default
        author = kwargs.get("author")
        if not author:
            author_d = await self._async_list_author()
            idx = input("Please select an author ID from the list above: ")
            if idx.isdigit():
                obj = author_d.get(int(idx))
                if obj:
                    author = obj.name
        author = author or AUTHOR
        tip(f"Using author: {author}")

        # Get novel genres from user or generate randomly
        novel_genres = input(_("Enter novel genre (press Enter for random): ")).strip()
        if not novel_genres:
            novel_genres = "+".join(random.sample(list(WEB_NOVEL_GENRE_DICT.keys()), random.randint(2, 4)))
            tip(_("Generated novel type: {}").format(novel_genres))

        # Get word count from user or use default
        while True:
            words_plan = input(_("Enter planned word count (max 1,500,000, press Enter for default 50000): ")).strip()
            if not words_plan:
                words_plan = 50000
                tip(_("Using default planned word count: {}").format(words_plan))
                break
            try:
                words_plan = int(words_plan)
                if 5000 < words_plan <= 1_500_000:
                    break
                error(_("Word count must be between 5,000 and 1,500,000"))
            except ValueError:
                error(_("Please enter a valid number"))

        config = StoryAgentConfig(
            llm_model=LLM_MODEL,
            llm_timeout=120,
            author=author,
            novel_genres=novel_genres,
            words_plan=words_plan,
        )
        agent = StoryAgent(config=config)
        story = await agent.async_run(**kwargs)
        if story:
            await self.async_show_story(story=story)
            return story
        return None

    async def async_delete_story(self, **kwargs: Any) -> None:
        story = await self._async_get_story(**kwargs)
        await story.adelete()
        self.stdout.write(_(f"Story {story.name}({story.id}, {story.key}) deleted successfully"))

    async def async_list_story(self, **kwargs: Any) -> List[Story]:
        stories = []
        fields = ["id", "name", "plan", "types", "theme", "sub-themes", "summary"]
        table = PrettyTable(field_names=fields, border=True, hrules=True, preserve_internal_border=True, align="l")
        async for story in Story.objects.all():
            w = 25
            # plot_chars = "\n".join([f"{k}: {ellipse(v, (w-5-len(k)))}" for k, v in story.plot_characters.items()])
            # plot = fill(ellipse(str(story.plot), len(story.plot_characters) * w - 3), width=w)
            # plot = fill(ellipse(str(story.plot), 2 * w - 3), width=w)
            summary = fill(ellipse(str(story.core_story), 2 * w - 3), width=w)

            stories.append(story)
            table.add_row(
                [
                    story.id,
                    story.name + "\n" + story.key,
                    f"{story.words_plan}\nvol:{story.volumes_plan}\nchap:{story.chapters_plan}",
                    "\n".join(story.genres.split("+")),
                    "\n".join(story.themes.get("major") or []),
                    "\n".join(story.themes.get("minor") or []),
                    summary,
                    # plot,
                    # plot_chars,
                ]
            )
        print(table)
        return stories

    async def async_show_story(self, **kwargs: Any) -> Story:
        story = await self._async_get_story(**kwargs)
        await self._async_show_detail(story, **kwargs)
        return story

    # -----  character  -----
    async def async_create_character(self, **kwargs: Any) -> Optional[list[StoryCharacter]]:
        story = await self._async_get_story(**kwargs)
        config = StoryCharacterAgentConfig(
            llm_model=LLM_MODEL,
            name=f"character-agent-{story.name}",
            only_if_empty=False,
            story=story,
            author=story.author,
            novel_genres=story.genres,
            words_plan=story.words_plan,
            novel_lang=story.lang,
        )
        existed_characters = await self.async_list_character(story=story)
        if len(existed_characters) > 0:
            create_more = input(f"story {story} has {len(existed_characters)} characters already. Create more? (Y/N):.")
            if create_more.lower() != "y":
                warn("abort creating more characters.")
                return []

        agent = StoryCharacterAgent(config=config)
        characters = await agent.async_run()
        if characters:
            await self.async_list_character(story=story)
            success(f"Created {len(characters)} characters ({[c.code for c in characters]}) for {story}")
            return characters
        success(f"Created 0 characters for {story}")
        return None

    async def async_delete_character(self, **kwargs: Any) -> None:
        character = await self._async_get_character(**kwargs)
        # character = await self.async_show_character(**kwargs)
        await character.adelete()
        self.stdout.write(f"Character {character.name}({character.code}, {character.key}) deleted successfully")

    async def async_list_character(self, **kwargs: Any) -> List[StoryCharacter]:
        story = await self._async_get_story(**kwargs)
        characters = []
        headers = ["id", "story", "name", "code", "key", "level", "nicks", "bio"]
        table = PrettyTable(field_names=headers, border=False, preserve_internal_border=True, align="l")
        async for character in story.storycharacter_set.all():
            characters.append(character)
            table.add_row(
                [
                    character.id,
                    character.story.name,
                    character.name,
                    character.code,
                    character.key,
                    character.level,
                    ",".join(character.aliases),
                    ellipse(str(character.bio), 40),
                ]
            )
        print(table)
        return characters

    async def async_show_character(self, **kwargs: Any) -> StoryCharacter:
        character = await self._async_get_character(**kwargs)
        await self._async_show_detail(character, **kwargs)
        return character

    # -----  outline  -----
    async def async_create_outline(self, **kwargs: Any) -> Optional[StoryOutline]:

        story = await self._async_get_story(**kwargs)
        config = StoryOutlineAgentConfig(
            llm_model=LLM_MODEL,
            name=f"outline-agent-{story.name}",
            story=story,
            author=story.author,
            novel_genres=story.genres,
            words_plan=story.words_plan,
            novel_lang=story.lang,
        )
        agent = StoryOutlineAgent(config=config)
        outline = await agent.async_run()
        if outline:
            await self.async_show_outline(outline=outline)
            return outline
        return None

    async def async_delete_outline(self, **kwargs: Any) -> None:
        outline = await self._async_get_outline(**kwargs)
        await outline.adelete()
        self.stdout.write(f"Outline {outline.story.name}({outline.key}) deleted successfully")

    async def async_list_outline(self, **kwargs: Any) -> List[Any]:
        tip("outline is singleton. show detail instead.")
        return await self.async_show_outline(**kwargs)

    async def async_show_outline(self, **kwargs: Any) -> Any:
        outline = await self._async_get_outline(**kwargs)
        await self._async_show_detail(outline, **kwargs)
        return outline

    # -----  world  -----
    async def async_create_world(self, **kwargs: Any) -> list[StoryWorld]:

        story = await self._async_get_story(**kwargs)
        kwargs.pop("story")
        existed_worlds = await self.async_list_world(story=story, **kwargs)
        if len(existed_worlds) > 0:
            create_more = input(f"story {story} has {len(existed_worlds)} worlds already. Create more? (Y/N):.")
            if create_more.lower() != "y":
                return []

        config = StoryWorldAgentConfig(
            llm_model=LLM_MODEL,
            name=f"world-agent-{story.name}",
            only_if_empty=False,
            story=story,
            author=story.author,
            novel_genres=story.genres,
            words_plan=story.words_plan,
            novel_lang=story.lang,
        )
        agent = StoryWorldAgent(config=config)
        worlds = await agent.async_run()
        if worlds:
            kwargs.pop("story", None)
            await self.async_list_world(story=story, **kwargs)
            return worlds
        return []

    async def async_delete_world(self, **kwargs: Any) -> None:
        world = await self._async_get_world(**kwargs)
        await world.adelete()
        self.stdout.write(f"World {world} deleted successfully")

    async def async_list_world(self, **kwargs: Any) -> List[Any]:
        story = await self._async_get_story(**kwargs)
        worlds = []
        headers = [
            "story",
            "world",
            "world_setting",
            # "historical_events",
            # "cultural_traits",
            "factions",
            "specific_mechanics",
            "core_items",
            "world_rules",
        ]
        table = PrettyTable(field_names=headers, border=True, hrules=True, preserve_internal_border=True, align="l")
        async for world in story.storyworld_set.all():
            worlds.append(world)
            table.add_row(
                [
                    world.story.name,
                    world.name,
                    fill(world.world_setting, width=20),
                    # _cell(world.historical_events),
                    # _cell(world.cultural_traits, key_only=True),
                    "\n".join(world.factions.keys()),
                    fill("\n".join([k + ": " + v for k, v in world.specific_mechanics.items()]), width=30),
                    "\n".join(world.core_items.keys()),
                    "\n".join(world.world_rules.keys()),
                ]
            )
        print(table)
        return worlds

    async def async_show_world(self, **kwargs: Any) -> Any:
        world = await self._async_get_world(**kwargs)
        await self._async_show_detail(world, **kwargs)
        return world

    # -----  chapter  -----
    async def async_create_chapter(self, **kwargs: Any) -> Optional[Any]:
        count = kwargs.get("count", 1)
        story = await self._async_get_story(**kwargs)
        step = 2
        n = count // step + (1 if count % step > 0 else 0)
        info(f"creating {n*step} chapters ({step}/call) for {story}")
        total = []
        for i in range(n):
            config = StoryChapterAgentConfig(
                llm_model=LLM_MODEL,
                name=f"chapter-agent-{story.name}",
                story=story,
                count=step,
                author=story.author,
                novel_genres=story.genres,
                words_plan=story.words_plan,
                novel_lang=story.lang,
            )
            agent = StoryChapterAgent(config=config)
            chapters = await agent.async_run()
            if chapters:
                for c in chapters:
                    debug(f"  {c.volume}/{c.order} {c.title} created.")
                total.extend(chapters)
            if i < n - 1:
                await asyncio.sleep(10)

        if total:
            kwargs.pop("story")
            await self.async_list_chapter(story=story, **kwargs)
            return total
        return []

    async def async_delete_chapter(self, **kwargs: Any) -> None:
        chapter = await self._async_get_chapter(**kwargs)
        await chapter.adelete()
        self.stdout.write(_("Chapter {} deleted successfully").format(chapter.key))

    async def async_list_chapter(self, **kwargs: Any) -> List[Any]:
        story = await self._async_get_story(**kwargs)
        chapters = []
        headers = ["vol", "#", "title", "content"]
        table = PrettyTable(field_names=headers, border=False, preserve_internal_border=True, align="l")
        w = 47 - 3
        async for chapter in story.storychapter_set.all():
            chapters.append(chapter)
            content_preview: str = chapter.content[:w] + "..." if len(chapter.content) > w else chapter.content
            content_preview = content_preview.replace("\n", " ").strip()
            table.add_row([chapter.volume, chapter.order, chapter.title, content_preview])
        print(table)
        return chapters

    async def async_show_chapter(self, **kwargs: Any) -> Any:
        chapter = await self._async_get_chapter(**kwargs)
        table = PrettyTable(
            field_names=["Field", "Value"],
            border=False,
            preserve_internal_border=True,
            align="l",
        )
        for key, value in chapter.to_dict().items():
            table.add_row([key, str(value)])
        print(table)
        return chapter

    # -----  helper functions  -----
    async def _async_get_story(self, **kwargs: Any) -> Story:
        """get story from kwargs, if not provided, list for user to choose, if not chosen, raise error"""

        # shall_raise = kwargs.pop("raise", True)
        story_id_or_key = kwargs["story"]
        if not story_id_or_key:
            # List available stories and let user choose
            story_d = {str(o.id): o for o in await self.async_list_story()}
            story_id_or_key = input("Please select a story ID or key from the list above: ")
            story_id_or_key = story_d.get(story_id_or_key)
            if not story_id_or_key:
                raise CommandError("Story id or key is required")

        if isinstance(story_id_or_key, Story):
            story = story_id_or_key
        else:
            if story_id_or_key.isdigit():
                story = await Story.objects.filter(id=story_id_or_key).afirst()
            else:
                story = await Story.objects.filter(key=story_id_or_key).afirst()
            if not story:
                raise CommandError(f"Story with id or key {story_id_or_key} not found")
        return story

    async def _async_get_outline(self, **kwargs: Any) -> Any:
        story = await self._async_get_story(**kwargs)
        return await story.async_outline()

    async def _async_get_world(self, **kwargs: Any) -> StoryWorld:
        id_key_code = kwargs["world"]
        if not id_key_code:
            worlds_d = {str(o.id): o for o in await self.async_list_world(**kwargs)}
            id_key_code = input("Please select a world ID from the list above: ")
            id_key_code = worlds_d.get(str(id_key_code))
            if not id_key_code:
                raise CommandError("World id, key or code is required")

        world = None
        if isinstance(id_key_code, StoryWorld):
            world = id_key_code
        else:
            if id_key_code.isdigit():
                world = await StoryWorld.objects.filter(id=id_key_code).afirst()
            if not world:
                raise CommandError(f"Character with id, key or code '{id_key_code}' not found")
        return world

    async def _async_get_character(self, **kwargs: Any) -> StoryCharacter:
        id_key_code = kwargs["character"]
        if not id_key_code:
            character_d = {str(o.id): o for o in await self.async_list_character(**kwargs)}
            id_key_code = input("Please select a character ID from the list above: ")
            id_key_code = character_d.get(str(id_key_code))
            if not id_key_code:
                raise CommandError("Character id, key or code is required")

        character = None
        if isinstance(id_key_code, StoryCharacter):
            character = id_key_code
        else:
            if id_key_code.isdigit():
                character = await StoryCharacter.objects.filter(id=id_key_code).afirst()
            else:
                if id_key_code.startswith("$[") and id_key_code.endswith("]$"):
                    story = await self._async_get_story(**kwargs)
                    if story:
                        character = await StoryCharacter.objects.filter(story=story, code=id_key_code).afirst()
                else:
                    character = await StoryCharacter.objects.filter(key=id_key_code).afirst()
            if not character:
                raise CommandError(f"Character with id, key or code '{id_key_code}' not found")
        return character

    async def _async_get_chapter(self, **kwargs: Any) -> Any:
        # TODO: Implement chapter retrieval logic
        self.stderr.write(_("Chapter retrieval not implemented yet"))
        exit(1)

    async def _async_show_detail(self, obj: "django.db.models.Model", **kwargs: Any) -> Any:

        table = PrettyTable(
            header=False,
            border=True,
            hrules=True,
            align="l",
        )
        d = {f.name: getattr(obj, f.name) for f in obj._meta.get_fields() if not f.is_relation}

        if isinstance(obj, StoryOutline):
            d["name"] = (await obj.async_story()).name

        d = {"name": f"{d.pop('id')}. {d.pop('name')} ({d.pop('key', '')}, {d.pop('code', '')})"} | d
        d["times"] = f"created: {d.pop('created_at')}\n updated: {d.pop('updated_at')}"

        w = 50

        for key, val in d.items():

            if key == "relationships":  # Character
                value = "\n".join([f"{k}: {ellipse(v, (w-2-len(k)))}" for k, v in val.items()])
            # elif key == "plot_characters":  # Story
            #     value = "\n".join([f"{k}: {ellipse(v, (w-2-len(k)))}" for k, v in val.items()])
            else:
                value = fill(str(val), width=w)
            table.add_row([key, value])
        print(table)
        return obj

    async def _async_list_author(self) -> dict[int, Author]:
        authors = {}
        headers = [
            "id",
            "name",
            "language",
            "cultural_bg",
            "skilled_genre",
            "skilled_types",
            "skilled_themes",
            "skilled_styles",
            "target_audience",
            "major_works_and_types",
        ]
        table = PrettyTable(field_names=headers, border=False, preserve_internal_border=True, align="l")
        for i, author in enumerate(AUTHOR_DICT.values()):
            authors[i] = author
            table.add_row(
                [
                    i,
                    author.name,
                    author.language,
                    author.cultural_bg,
                    author.skilled_type,
                    ",".join(author.skilled_genres),
                    ",".join(author.skilled_themes),
                    ",".join(author.skilled_styles),
                    author.target_audience,
                    ",".join([f"{k}({v})" for k, v in author.major_works_and_types.items()]),
                ]
            )
        print(table)
        return authors

    def add_arguments(self, parser):
        parser.add_argument(
            "action",
            type=str,
            choices=["create", "delete", "list", "show"],
            help="Action to perform on the element",
        )
        parser.add_argument(
            "element",
            type=str,
            choices=["story", "outline", "world", "character", "chapter"],
            help="Element to perform action on",
        )
        parser.add_argument(
            "-y",
            "--story",
            type=str,
            help="Story ID or key",
        )
        parser.add_argument(
            "-l",
            "--outline",
            type=str,
            help="Outline ID or key",
        )
        parser.add_argument(
            "-w",
            "--world",
            type=str,
            help="World ID or key",
        )
        parser.add_argument(
            "-c",
            "--character",
            type=str,
            help="Character ID, key or code",
        )
        parser.add_argument(
            "-p",
            "--chapter",
            type=str,
            help="Chapter ID or key",
        )
        parser.add_argument(
            "-a",
            "--author",
            type=str,
            choices=list(AUTHOR_DICT.keys()),
            help="Author name when creating a story",
        )
        parser.add_argument(
            "-n",
            "--count",
            type=int,
            default=1,
            help="Count of characters or chapters to create",
        )

    def handle(self, *args: Any, **kwargs: Any) -> None:
        action = kwargs["action"]
        element = kwargs["element"]
        func_name = f"async_{action}_{element}"
        func = getattr(self, func_name)

        if action in {"create"}:
            tip(f"--==!!  Using LLM model: {LLM_MODEL}  !!==--")
            debug(f"proxy: {get_str_env('http_proxy')}")
            debug(f"proxy: {get_str_env('https_proxy')}")

        if not func:
            self.stderr.write(f'"{action} {element}" is not implemented.')
            exit(1)

        try:
            asyncio.run(func(**kwargs))
            self.stdout.write(self.style.SUCCESS("-- FINISHED --"))
            exit(0)
        except CommandError as e:
            self.stderr.write(f"Error: {e}")

        exit(1)
