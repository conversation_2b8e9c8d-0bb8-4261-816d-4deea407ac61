from typing import Self

from asgiref.sync import sync_to_async
from django.db import models
from django.utils.html import escape

from djcommon.mixins import CreateTimeModelMixin
from djcommon.model import UpdateTimeMixin
from novelai import conf
from novelai.engine.models.base import NovelModelBase
from pycommon.utils.lang import BaseStrEnum

d_column_helps = {
    "story": {
        "zh": "章节所属的故事",
        "en": "The story this chapter belongs to",
    },
    "type": {
        "zh": "章节类型",
        "en": "The type of this chapter",
    },
    "volume": {
        "zh": "卷。章节所属的卷",
        "en": "Volume this chapter belongs to",
    },
    "order": {
        "zh": "章节在卷中的顺序",
        "en": "The order of this chapter in the volume",
    },
    "title": {
        "zh": "章节标题",
        "en": "chapter title",
    },
    "content": {
        "zh": "章节内容",
        "en": "The content of the chapter",
    },
    "characters": {
        "zh": "本章出现的角色code列表，按出场顺序排列",
        "en": "Character Codes present in this chapter, list in order of appearance",
    },
    "summary": {
        "zh": "当前章节的摘要（根据content生成)",
        "en": "Summary of the chapter (summarize from content)",
    },
}


class StoryChapterType(BaseStrEnum):
    PREFACE = "preface"  # 序言 - Preface/Introduction
    NOTE = "note"  # 作者的话 - Author's notes
    CHAPTER = "chapter"  # 正文 - Main chapter content
    AFTERWORD = "afterword"  # 后记 - Afterword/Epilogue
    EXTRA = "extra"  # 番外 - Extra/side story
    INTERLUDE = "interlude"  # 插曲 - Interlude
    SPECIAL = "special"  # 特别篇 - Special chapter
    BONUS = "bonus"  # 福利篇 - Bonus content
    QA = "qa"  # 问答 - Q&A section
    PROFILE = "profile"  # 人物志 - Character profiles

    def __str__(self) -> str:
        return self.text_by_lang()

    @staticmethod
    def get_trans() -> dict:
        _trans: dict = {
            StoryChapterType.PREFACE: {"zh": "序言", "en": "Preface"},
            StoryChapterType.NOTE: {"zh": "作者的话", "en": "Author's Notes"},
            StoryChapterType.CHAPTER: {"zh": "正文", "en": "Chapter"},
            StoryChapterType.AFTERWORD: {"zh": "后记", "en": "Afterword"},
            StoryChapterType.EXTRA: {"zh": "番外", "en": "Extra Story"},
            StoryChapterType.INTERLUDE: {"zh": "插曲", "en": "Interlude"},
            StoryChapterType.SPECIAL: {"zh": "特别篇", "en": "Special Chapter"},
            StoryChapterType.BONUS: {"zh": "福利篇", "en": "Bonus Content"},
            StoryChapterType.QA: {"zh": "问答", "en": "Q&A"},
            StoryChapterType.PROFILE: {"zh": "人物志", "en": "Character Profile"},
        }
        return _trans

    def text_by_lang(self, lang: str = "") -> str:
        """Get localized string for chapter type

        Args:
            lang: Language code ('zh' or 'en'). If empty, uses conf.LANG

        Returns:
            Localized string for the chapter type
        """
        lang = lang or conf.LANG
        return self.get_trans()[self.value][lang]


class StoryChapter(NovelModelBase, CreateTimeModelMixin, UpdateTimeMixin):
    """represents a chapter of a novel"""

    story = models.ForeignKey(
        "Story",
        on_delete=models.CASCADE,
        help_text=escape(d_column_helps["story"][conf.LANG]),
    )

    type = models.CharField(
        max_length=20,
        db_index=True,
        choices=((c[0], StoryChapterType.get_trans()[c[0]][conf.LANG]) for c in StoryChapterType.choices()),
        default=StoryChapterType.CHAPTER,
        help_text=escape(d_column_helps["type"][conf.LANG]),
    )

    volume = models.PositiveIntegerField(null=True, help_text=escape(d_column_helps["order"][conf.LANG]))
    order = models.PositiveIntegerField(db_index=True, help_text=escape(d_column_helps["order"][conf.LANG]))
    title = models.CharField(max_length=255, help_text=escape(d_column_helps["title"][conf.LANG]))
    content = models.TextField(default="", help_text=escape(d_column_helps["content"][conf.LANG]))
    characters = models.JSONField(default=list, help_text=escape(d_column_helps["characters"][conf.LANG]))
    summary = models.TextField(default="", help_text=escape(d_column_helps["summary"][conf.LANG]))

    class Meta:
        ordering = ["story", "volume", "order"]
        unique_together = ["story", "volume", "order"]

    def __str__(self):
        return f"{self.story} - {self.title} (Vol {self.volume}, Chapter {self.order})"

    @staticmethod
    def get_last_volume(story: "Story") -> int:
        """get the last volume number"""
        return StoryChapter.objects.filter(story=story).aggregate(models.Max("volume"))["volume__max"] or 1

    @staticmethod
    async def async_get_last_volume(story: "Story") -> int:
        """get the last volume number asynchronously"""
        return await sync_to_async(StoryChapter.get_last_volume)(story)

    @staticmethod
    def get_latest_chapter(story: "Story") -> Self:
        """get the last chapter object of a story"""
        return StoryChapter.objects.filter(story=story).order_by("-volume", "-order").first()

    @staticmethod
    async def async_get_latest_chapter(story: "Story") -> Self:
        """get the last chapter object of a story asynchronously"""
        return await sync_to_async(StoryChapter.get_latest_chapter)(story)
