# lab-llm

Lab - Large Language Model

Wrapper for all kinds of LLM.

* Why I create this since there is already many wrappers for universe LLM APIs?
	* Flexable configuration
	* ...
	* Still can use other wrapper as backend

* sample - minimal

```python
import pyllm

setting = pyllm.ModelSetting(
	provider=pyllm.ProviderEnum.OLLAMA,
	mod_name=pyllm.ModelNameEnum.OLLAMA_LLAMA_31,
)
cli = pyllm.create_client(setting)
param = pyllm.create_param(setting)
prompts = pyllm.ChatPrompts().set_inquery("what's your name?")
answer = cli.completion(prompts, param)
print(answer)

```

* sample - yaml output

```python
import pyllm

setting = pyllm.ModelSetting(
	provider=pyllm.ProviderEnum.OLLAMA,
	mod_name=pyllm.ModelNameEnum.OLLAMA_LLAMA_31,
)
cli = pyllm.create_client(setting)
param = pyllm.create_param(setting)
prompts = pyllm.ChatPrompts.new(
	'Whatever your name is, please answer me "Jack" if I ask your name.',
	"what's your name?",
)
prompts.answer_format = "yaml"
prompts.answer_schema = "name: <your_name>"
answer = cli.completion(prompts, param)
print(answer)

```

* sample - store states

```python
from typing import Callable
import pyllm


class DbStore(pyllm.NotificationStore):

	def persist(self, *notes: pyllm.Notification) -> None:
		for note in notes:
			for channel in note.subjects:
				func_name = f"_persist_{channel}"
				func = getattr(self, func_name, None)
				if func and isinstance(func, Callable):
					func(note)

	def _persist_log(self, note: pyllm.Notification) -> None:
		print(f"PERSIST LOG {note}")

	def _persist_cost(self, note: pyllm.Notification) -> None:
		print(f"PERSIST COST {note}")


setting = pyllm.ModelSetting(
	provider=pyllm.ProviderEnum.OLLAMA,
	mod_name=pyllm.ModelNameEnum.OLLAMA_LLAMA_31,
)
cli = pyllm.create_client(setting)
param = pyllm.create_param(setting)
prompts = pyllm.ChatPrompts.new(
	'Whatever your name is, please answer me "Samuel" if I ask your name.',
	"what's your name?",
)
answer = cli.completion(prompts, param)
print(answer)

# create a class (e.g. DbStore) to implement `NotificationStore`
# then use it as the argument for `save_notifications`
store = DbStore()
cli.save_notifications(store)

```
