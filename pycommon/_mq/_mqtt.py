"""
MQTT Client for server-client communication
"""

from typing import Optional
from paho.mqtt import client as mqtt_client
from paho.mqtt import subscribe, publish
from time import sleep

from logwrapper import getLogger

log = getLogger("common.mqtt")

from consts import (
    DEBUG,
    MQTT_BROKER,
    MQTT_PORT,
)
from definitions import I18N


CLIENT_ID_PREFIX = "sam-cm"
TOPIC_PREFIX = "/s/cm"


class CommClient(object):
    TOPIC_MASTER_COMMAND = f"{TOPIC_PREFIX}/10"
    TOPIC_NODE_REQUEST = f"{TOPIC_PREFIX}/20"

    def __init__(
        self,
        code: str,
        broker: str = MQTT_BROKER,
        port: int = MQTT_PORT,
        keepalive: int = 60,
        bind_address: str = "",
        auth: str = None,
        clean_session: bool = False,
        is_master: bool = False,
    ):
        self.__code: str = code
        self.__client_id: str = f"{CLIENT_ID_PREFIX}-{self.__code}"
        self.__clean_session: bool = clean_session
        self.__is_master: bool = is_master
        self.__broker: str = broker
        self.__port: int = port
        self.__keepalive: int = keepalive
        self.__bind_address: str = bind_address
        self.__auth: str = auth

        self.__client: mqtt_client = None
        self.__topic_callbacks: dict[str, callable] = {}

        # self.__running: bool = False
        # self.__thread: threading.Thread = None

    # @property
    # def is_running(self) -> bool:
    #     return self.__running

    @property
    def is_master(self) -> bool:
        return self.__is_master

    @property
    def code(self) -> str:
        return self.__code

    def subscribe_master_command(self, callback: callable):
        # assert not self.is_master, I18N.exc('Only node can listen to master.')

        self.__topic_callbacks[f"{self.TOPIC_MASTER_COMMAND}/{self.__code}"] = callback

    def subscribe_node_request(self, callback: callable):
        # assert self.is_master, I18N.exc('Only master can listen to node.')

        self.__topic_callbacks[f"{self.TOPIC_NODE_REQUEST}"] = callback

    def send_command_to_node(self, node_code: str, message: str):
        return self.send_message(f"{self.TOPIC_MASTER_COMMAND}/{node_code}", message)

    def send_request_to_master(self, message: str):
        return self.send_message(f"{self.TOPIC_NODE_REQUEST}", message)

    def send_message(self, topic: str, message: str, qos: int = 0, retain=False):
        if self.__client:
            msginfo = self.__client.publish(topic, message, qos, retain)
            rc = msginfo.rc
            if rc == mqtt_client.MQTT_ERR_SUCCESS:
                log.debug(f'"{message}" is sent to "{topic}" ({self.__client_id}).')
            else:
                log.error(f'Failed to send message to "{topic}" ({self.__client_id}). rc={rc}')
            return msginfo.rc
        else:
            raise RuntimeError("You must start() before sending message.")

    def on_connect(self, client, userdata, flags, rc):
        if rc == mqtt_client.MQTT_ERR_SUCCESS:
            log.info(f"Connected ({self.__client_id}).")
            if self.__topic_callbacks:
                for topic, cb in self.__topic_callbacks.items():
                    # client.message_callback_add(topic, cb)
                    result = client.subscribe(topic, qos=2)
                    rc, mid = result
                    if rc == mqtt_client.MQTT_ERR_SUCCESS:
                        log.info(f'Listening to "{topic}" ({self.__client_id}).')
                    else:
                        log.error(f'Fail to listen "{topic}" ({self.__client_id}). rc={rc} mid={mid}')

            else:
                log.warning(f"No listener specified for client {self.__client_id}.")
        else:
            log.error(f"Fail to connect ({self.__client_id}). flags={flags}  rc={rc}")

    def on_disconnect(self, client, userdata, rc):
        if rc == mqtt_client.MQTT_ERR_SUCCESS:
            log.info(f"Disconnected ({self.__client_id}).")
        else:
            log.error(f"Unexpected disconnect ({self.__client_id}).  rc={rc}")

    def on_message(self, client, userdata, msg):
        # msg <topic, payload, qos, retain>
        topic = msg.topic
        message = msg.payload.decode("utf-8")
        log.debug(f"received {topic} - {message}")
        cb: callable = self.__topic_callbacks.get(topic)
        if cb:
            # print(f'on message: {topic} - {message}')
            if topic.startswith(self.TOPIC_NODE_REQUEST):
                idx: int = message.index("|")
                code: str = message[:idx]
                cb(code, message[idx + 1 :])
            else:
                cb(message)
        else:
            log.Warning(f'No callback for "{topic}". Message: "{message}".')

    def on_log(self, client, userdata, level, buf):
        log.debug(f"{level}: {buf}")

    # __on_connected = None

    # @property
    # def on_connected(self):
    #     return self.__on_connected

    # @on_connected.setter
    # def on_connected(self, v: callable):
    #     self.__on_connected = v

    def start(self) -> bool:

        cli = self.__client = mqtt_client.Client(client_id=self.__client_id, clean_session=self.__clean_session)
        cli.on_connect = self.on_connect
        cli.on_message = self.on_message
        if DEBUG:
            cli.on_log = self.on_log

        cli.connect(
            host=self.__broker,
            port=self.__port,
            keepalive=self.__keepalive,
            bind_address=self.__bind_address,
        )
        cli.loop_start()
        log.info(f"Started {self.__client_id}.")
        return True

    def stop(self):
        if self.__client:
            cli = self.__client
            cli.on_disconnect = self.on_disconnect
            cli.loop_stop()
            cli.disconnect(mqtt_client.MQTT_ERR_SUCCESS)
            self.__client = None
            log.info(f"Stopped ({self.__client_id}).")


if __name__ == "__main__":
    import sys
    from logwrapper import config_logging

    config_logging("mqtt.log")

    def on_request(code: str, message: str):
        print(f"on request: {message} - from {code}")

    def on_command(message: str):
        print(f"on command: {message}")

    print("Starting")

    if len(sys.argv) > 1:
        # node
        comm = CommClient("m0", is_master=True)
        if comm.start():
            comm.send_request_to_master(sys.argv[1])
            comm.send_command_to_node("n1", "xxxxxx")
            comm.stop()
    else:
        comm = CommClient("n1")
        comm.subscribe_master_command(callback=on_command)
        comm.subscribe_node_request(callback=on_request)
        if comm.start():
            try:
                while True:
                    sleep(1)
            except KeyboardInterrupt:
                pass
            finally:
                comm.stop()
    print("Finished")
