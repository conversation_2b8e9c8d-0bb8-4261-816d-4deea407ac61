#!/usr/bin/python
# -*- coding=utf-8 -*-

__author__ = "samuel"

import os
import sys

import markdown
from flask import Flask, request, render_template_string, make_response, abort

from django_init import init_django

init_django()

from pycommon.logwrapper import get_log_wrapper
from novelai.engine.models.story import Story
from novelai.engine.models.chapter import StoryChapter

log = get_log_wrapper(__name__)

allowed_names = ["127.0.0.1", "localhost"]

base_path = os.path.dirname(__file__)
static_base_symbol_link = os.path.join(base_path, "static")

app = Flask(__name__, static_folder=static_base_symbol_link, static_url_path="/static")

MD_PREFIX = "<!--md-->"


# font_em = 0.9
album_w = 80
album_h = 100

template_page = """
<html>
<head>
<meta charset="{{ encoding }}" />
<meta http-equiv="content-type" content="text/html; charset={{ encoding }}" />
<style type="text/css">

  body, pre, p, div, input, h1,h2,h3,h4,h5 {
    font-family : MS Yahei, Consolas, Courier New;
  }

  body, pre, p, div, li, input, select, span {
    font-size: 0.9em;
    line-height: 150%;
    letter-spacing: 0.2;
  }

  table {
    font-family: SongTi;
    font-size: 1em;
  }

  .nav {
    font-size: 1em;
  }

  .article {
    font-family: KaiTi;
    // font-size: 26px;
    border: 0px;
    background-color: #eee;
    width: 90%;
    max-width: 976px;
    min-width: 320px;
    margin: auto;
  }

  .article p {
    font-family: KaiTi;
  }

  .article img {
    max-width: 90%;
  }

  .error {
    color: red;
  }

  .notice {
    color: blue;
  }
</style>
</head>
<body>
    <div id="wrapper">
        {{ content | safe }}
    </div>
</body>"""


@app.route("/", methods=["GET"])
def home():
    handle_client_ip()
    context = {"encoding": "utf-8"}

    colspan = request.args.get("col", default=6, type=int)
    i = 0
    odd = False
    sb = []

    try:
        sb.append('<h3 align="center">NovelAI 小说阅读器</h3>')

        # Get all stories
        stories = Story.objects.all().order_by("-created_at")

        sb.append('<h4 align="center">AI 生成小说 - <a href="/all">全部</a></h4>')
        sb.append('<table align="center" width="90%" cellpadding="5">')

        for story in stories:
            if i % colspan == 0:
                sb.append('<tr style="%s">' % ("background-color:#eee" if odd else ""))
                odd = not odd

            sb.append('<td><a href="/story/%s/">%s</a></td>' % (story.id, story.name))
            sb.append("<td>作者: %s</td>" % story.author)
            sb.append("<td>类型: %s</td>" % story.genres)
            sb.append("<td>%s</td>" % story.created_at.strftime("%Y-%m-%d"))

            i += 1
            if i % colspan == 0:
                sb.append("</tr>")

        if i % colspan != 0:
            sb.append("</tr>")

        sb.append("</table>")

        content = "\n".join(sb)
    except Exception as err:
        content = '<h2>Error</h2><br><p class="error">' + str(err) + "</p>"
        log.exception(err)

    context["content"] = content
    resp = make_response(render_template_string(template_page, **context))
    resp.headers["Content-Type"] = "text/html; charset=utf-8"
    return resp


@app.route("/all", methods=["GET"])
def all_stories():
    return render_story_list_page()


@app.route("/story/<int:story_id>/", methods=["GET"])
def story_home(story_id):
    return render_story_page(story_id)


@app.route("/story/<int:story_id>/<int:chapter_id>/", methods=["GET"])
def chapter_page(story_id, chapter_id):
    handle_client_ip()
    context = {"encoding": "utf-8"}

    try:
        # Get story and chapter information
        story = Story.objects.get(id=story_id)
        chapter = StoryChapter.objects.get(id=chapter_id, story=story)

        # Get previous and next chapters
        prev_chapter = (
            StoryChapter.objects.filter(story=story, volume=chapter.volume, order__lt=chapter.order)
            .order_by("-order")
            .first()
        )

        next_chapter = (
            StoryChapter.objects.filter(story=story, volume=chapter.volume, order__gt=chapter.order)
            .order_by("order")
            .first()
        )

        # Build navigation
        prev_url = f"/story/{story_id}/{prev_chapter.id}/" if prev_chapter else "#"
        next_url = f"/story/{story_id}/{next_chapter.id}/" if next_chapter else "#"

        nav = f'  <a href="/story/{story_id}/">返回目录</a>  '
        if prev_chapter:
            nav = f'<a href="{prev_url}">上一章</a>' + nav
        if next_chapter:
            nav += f'<a href="{next_url}">下一章</a>'
        nav = f'<div class="nav" align="center">{nav}</div>'

        # Process chapter content
        if chapter.content:
            chapter_content = chapter.content
            for code, c in story.get_characters().items():
                if c.is_name_confirmed:
                    chapter_content = chapter_content.replace(code, c.name)
                    code = "#" + code[1:]
                    chapter_content = chapter_content.replace(code, c.name)

        else:
            chapter_content = "内容暂未生成"

        if chapter_content.startswith(MD_PREFIX):
            chapter_content = markdown.markdown(chapter_content)
        else:
            chapter_content = chapter_content.replace("\r\n", "<br>").replace("\n", "<br>")

        sb = []
        sb.append(f'<h3 align="center">{chapter.title}</h3>')

        # Add font size selector
        sb.append('<div align="center" style="margin: 10px;">')
        sb.append("<label>快捷键：左、右键 翻页，-、=(+)键 字体大小: ")
        sb.append('<select id="fontSizeSelector" onchange="changeFontSize(this.value)" style="margin: 5px;">')
        font_sizes = [10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36]
        for size in font_sizes:
            selected = "selected" if size == 22 else ""
            sb.append(f'<option value="{size}" {selected}>{size}px</option>')
        sb.append("</select>")
        sb.append("</label>")
        sb.append("</div>")

        sb.append(nav)
        sb.append('<div style="width:100%;background-color:#eee;">')
        sb.append("<hr />")
        sb.append(f'<div id="content" class="article" style="font-size:22px">{chapter_content}</div>\n')
        sb.append("<hr />")
        sb.append("</div>")
        sb.append(nav)
        sb.append(
            f"""
        <script language="javascript">
        document.onkeydown = key_pressed;
        var prev_page="{prev_url}";
        var next_page="{next_url}";
        var size = getCookieSize() || 22;

        // Initialize font size
        window.onload = function() {{
            document.getElementById("content").style.fontSize = size + 'px';
            document.getElementById("fontSizeSelector").value = size;
        }};

        function getCookieSize() {{
            var cookies = document.cookie.split(';');
            for (var i = 0; i < cookies.length; i++) {{
                var cookie = cookies[i].trim();
                if (cookie.indexOf('fontSize=') === 0) {{
                    return parseInt(cookie.substring('fontSize='.length));
                }}
            }}
            return null;
        }}

        function changeFontSize(newSize) {{
            size = parseInt(newSize);
            document.getElementById("content").style.fontSize = size + 'px';
            document.cookie = "fontSize=" + size + "; path=/; expires=" +
                new Date(Date.now() + 365*24*60*60*1000).toUTCString();
        }}

        function key_pressed(event) {{
          if (event.keyCode==37 && prev_page !== "#") location=prev_page;
          if (event.keyCode==39 && next_page !== "#") location=next_page;

          if (event.keyCode==189 || event.keyCode == 109) {{
            size -= 2;
            if (size <= 10) size = 10;
            changeFontSize(size);
            document.getElementById("fontSizeSelector").value = size;
          }}
          if (event.keyCode==187 || event.keyCode == 107) {{
            size += 2;
            if (size >= 36) size = 36;
            changeFontSize(size);
            document.getElementById("fontSizeSelector").value = size;
          }}
          if (event.keyCode==48) {{
            size = 22;
            changeFontSize(size);
            document.getElementById("fontSizeSelector").value = size;
          }}
        }}
        </script>
        """
        )

        content = "\n".join(sb)
    except Story.DoesNotExist:
        content = '<h1>Error</h1><p class="error">故事不存在</p>'
    except StoryChapter.DoesNotExist:
        content = '<h1>Error</h1><p class="error">章节不存在</p>'
    except Exception as err:
        log.exception(err)
        content = '<h1>Error</h1><p class="error">' + str(err) + "</p>"

    context["content"] = content
    resp = make_response(render_template_string(template_page, **context))
    resp.headers["Content-Type"] = "text/html; charset=utf-8"
    return resp


def render_story_page(story_id):
    """Render story page with chapter list"""
    handle_client_ip()
    context = {"encoding": "utf-8"}

    try:
        story = Story.objects.get(id=story_id)
        chapters = StoryChapter.objects.filter(story=story).order_by("volume", "order")

        colspan = request.args.get("col", default=5, type=int)
        i = 0
        odd = True
        sb = []

        sb.append(f'<h3 align="center">{story.name}</h3>')
        sb.append('<p align="center"><a href="/">返回首页</a></p>\n')

        # Story info
        sb.append('<div align="center" style="margin: 20px;">')
        sb.append(f"<p><strong>作者:</strong> {story.author}</p>")
        sb.append(f"<p><strong>类型:</strong> {story.genres}</p>")
        sb.append(f"<p><strong>计划字数:</strong> {story.words_plan:,} 字</p>")
        if story.background:
            sb.append(f"<p><strong>背景:</strong> {story.background[:200]}...</p>")
        sb.append("</div>")

        sb.append('<table align="center" width="95%">')

        current_volume = None
        for chapter in chapters:
            # Volume header
            if chapter.volume != current_volume:
                if i % colspan != 0 and i > 0:
                    sb.append("</tr>")
                sb.append(
                    f'<tr style="background-color:silver"><td colspan="{colspan}" align="center"><h4>第{chapter.volume}卷</h4></td></tr>'
                )
                current_volume = chapter.volume
                i = 0
                odd = True

            if i % colspan == 0:
                sb.append('<tr style="%s">' % ("background-color:#eee" if odd else ""))
                odd = not odd

            sb.append(f'<td><a href="/story/{story_id}/{chapter.id}/">{chapter.title}</a></td>')
            i += 1

            if i % colspan == 0:
                sb.append("</tr>")

        if i % colspan != 0:
            sb.append("</tr>")

        if not chapters.exists():
            sb.append('<tr style="background-color:#eee"><td colspan="5" align="center">暂无章节</td></tr>')

        sb.append("</table>")

        content = "\n".join(sb)
    except Story.DoesNotExist:
        content = '<h1>Error</h1><p class="error">故事不存在</p>'
    except Exception as err:
        log.exception(err)
        content = '<h1>Error</h1><p class="error">' + str(err) + "</p>"

    context["content"] = content
    resp = make_response(render_template_string(template_page, **context))
    resp.headers["Content-Type"] = "text/html; charset=utf-8"
    return resp


def render_story_list_page():
    """Render all stories list page"""
    handle_client_ip()
    context = {"encoding": "utf-8"}

    try:
        page = request.args.get("p", default=0, type=int)
        per_page = request.args.get("c", default=20, type=int)

        stories = Story.objects.all().order_by("-created_at")
        total_count = stories.count()

        # Pagination
        start = page * per_page
        end = start + per_page
        stories_page = stories[start:end]

        sb = []
        sb.append('<h3 align="center">所有 AI 生成小说</h3>')
        sb.append('<p align="center"><a href="/">返回首页</a></p>')

        # Pagination controls
        if page > 0:
            sb.append(f'<a href="/all?p={page-1}&c={per_page}">上一页</a> | ')
        sb.append(f"第 {page + 1} 页 | ")
        if end < total_count:
            sb.append(f'<a href="/all?p={page+1}&c={per_page}">下一页</a>')

        sb.append('<table align="center" width="95%" cellpadding="5">')
        sb.append('<tr style="background-color:#ddd">')
        sb.append("<th>小说名称</th><th>作者</th><th>类型</th><th>计划字数</th><th>创建时间</th>")
        sb.append("</tr>")

        odd = False
        for story in stories_page:
            sb.append('<tr style="%s">' % ("background-color:#eee" if odd else ""))
            sb.append(f'<td><a href="/story/{story.id}/">{story.name}</a></td>')
            sb.append(f"<td>{story.author}</td>")
            sb.append(f"<td>{story.genres}</td>")
            sb.append(f"<td>{story.words_plan:,} 字</td>")
            sb.append(f'<td>{story.created_at.strftime("%Y-%m-%d")}</td>')
            sb.append("</tr>")
            odd = not odd

        if not stories_page:
            sb.append('<tr><td colspan="5" align="center">暂无小说</td></tr>')

        sb.append("</table>")

        # Pagination controls (bottom)
        sb.append('<div align="center" style="margin-top: 20px;">')
        if page > 0:
            sb.append(f'<a href="/all?p={page-1}&c={per_page}">上一页</a> | ')
        sb.append(f"第 {page + 1} 页 (共 {total_count} 本小说) | ")
        if end < total_count:
            sb.append(f'<a href="/all?p={page+1}&c={per_page}">下一页</a>')
        sb.append("</div>")

        content = "\n".join(sb)
    except Exception as err:
        log.exception(err)
        content = '<h1>Error</h1><p class="error">' + str(err) + "</p>"

    context["content"] = content
    resp = make_response(render_template_string(template_page, **context))
    resp.headers["Content-Type"] = "text/html; charset=utf-8"
    return resp


def handle_client_ip():
    """Check if client IP is allowed"""
    if request.remote_addr not in allowed_names:
        abort(502)


if __name__ == "__main__":
    try:
        port = sys.argv[1]
        port = port.split(":")
        host = port[0]
        port = int(port[1])
        if port <= 0 or port > 65535:
            port = 8080

        if len(sys.argv) > 2:
            allowed_names += sys.argv[2].split(",")
    except:
        host = "127.0.0.1"
        port = 8080

    log.warning("Allowed names: %s" % allowed_names)
    app.run(host=host, port=port, debug=True)
