import asyncio
import json
import sys
from logging import getLogger
from pathlib import Path
from typing import Any

import html2text
from bs4 import BeautifulSoup
from pydantic import Field, BaseModel

from pycommon.utils.datetimeutils import timeit

lib_paths = [
    Path(__file__).absolute().parent.parent.parent.parent / "lab" / "lab-llm",
    Path(__file__).absolute().parent.parent.parent.parent / "lab" / "lab-pycommon",
]
[sys.path.insert(0, str(p)) for p in lib_paths]

import pyllm
from pycommon.utils.confutils import get_bool_env, get_str_env

# from pycommon.utils.strutil import quick_hash

from scraper.spiders.social_contact import (
    SocialContactSpider,
    SocialContactSpiderConfig,
)

logger = getLogger(__name__)


class VisualSpiderAgentConfig(BaseModel):
    max_output_tokens: int = Field(default=1024)
    max_depths: int = Field(default=3, lt=10)


class VisualSpiderAgent(BaseModel):
    config: VisualSpiderAgentConfig

    _spider: SocialContactSpider | None = None
    _visited_pages: set[str] = set()
    _visit_stack: list = list()

    # _tasks_manager = RunningTasksManager()

    async def _init(self, start_url: str) -> None:
        proxy_str = get_str_env("PROXY")
        proxy = json.loads(proxy_str) if proxy_str else None
        config = SocialContactSpiderConfig(
            name="social_contact_spider",
            browser_type="chromium",
            start_url=start_url,
            proxy=proxy,
            is_headless=get_bool_env("HEADLESS", True),
            timeout=120,
            ignored_resources=["font", "image", "media"],
            pages=0,
            items=0,
        )
        self._spider = SocialContactSpider(config)
        self._visited_pages = set()

        llm_setting = pyllm.ModelSetting(
            provider=pyllm.ProviderEnum.OLLAMA,
            mod_name=pyllm.ModelNameEnum.OLLAMA_LLAMA_32_VISION,
            max_input_token=8092,
            max_output_token=1024,
            temperature=0.7,
            top_p=0.3,
        )

        self._llm_cli = pyllm.create_client(llm_setting)
        self._llm_cli.ob.clean_observers()
        self._llm_param = pyllm.create_param(llm_setting)

    # async def run(self, url_and_contents:list[tuple[str,str]])->Any:
    #     for url, content in url_and_contents:
    #         cfg = SocialContactAgentConfig()
    #         agent = SocialContactAgent(config=cfg, observer=Observer())
    #         task = asyncio.create_task(await agent.run_one(url, content), name=url)
    #         task.add_done_callback(functools.partial(self.__task_done, task.get_name(), task))
    #         await self._tasks_manager.add_task(task.get_name(), task)
    #
    # def __task_done(self, key: str, task:Task) -> None:
    #     if key in self._tasks_manager.running_tasks:
    #         _task = self._tasks_manager.running_tasks.pop(key)
    #         assert task == _task
    #
    #     try:
    #         ex = task.exception()
    #     except (CancelledError, InvalidStateError) as e:
    #         ex = e
    #     if ex is None:
    #         logger.info(f"Task {task.get_name()} finished.")
    #     else:
    #         if isinstance(ex, TimeoutError):
    #             logger.warning(f"Task {task.get_name()} timeout.")
    #         elif isinstance(ex, CancelledError):
    #             logger.warning(f"Task {task.get_name()} cancelled.")
    #         else:
    #             logger.exception(f"Task {task.get_name()} failed.", exc_info=ex)
    #
    #     rc = task.result()
    #     print(f'------  {task.get_name()} result  ------')
    #     print(rc or 'not found')
    #     print('----------------------')
    #     if rc:
    #         for _t in self._tasks_manager.running_tasks:
    #             _t.cancel()

    async def run(self, start_url: str, start_content: str = "", **kwargs: Any) -> Any:
        if not start_url:
            raise ValueError("'start_url' must be available.")

        await self._init(start_url)

        attempts = 0
        conversation = []

        if not start_content:
            start_content = await self.tool_go_page(start_url)
        user_msg = f"We captured content of {start_url} as below:\n=====\n" + start_content

        while True:
            # logger.info(user_msg)
            logger.debug(user_msg.replace("\n", "\\n"))

            data = await self.call_llm(user_msg)
            conversation.append(data)

            tool: str = data["tool"]
            arg: str = data["arg"]
            links: list[str] = data.get("links", [])
            reason: str = data.get("reason", "")
            logger.warning(f"{attempts:02d}: {tool}({arg}) - {reason}")
            for lnk in links:
                logger.warning(f"    {lnk}")

            if tool in ["found", "not_found"]:
                return arg
            else:
                if attempts >= self.config.max_depths:
                    break

                func = getattr(self, f"tool_{tool}", None)
                if func is None:
                    raise KeyError(f"unknown tool {tool}")
                user_msg = await func(arg)
                if tool == "go_page":
                    self._visit_stack.append((tool, arg, user_msg))

            attempts += 1

        logger.info(f"not found after {attempts} attempts.")
        return ""

    @timeit()
    async def call_llm(self, user_msg: str) -> Any:

        prompts = pyllm.ChatPrompts().set_system(SYS_PROMPT).set_inquery(user_msg)
        print(prompts._get_system_dict())
        ret = self._llm_cli._completion(prompts, self._llm_param)

        logger.debug(f"LLM output: {ret}")
        if ret.startswith("LLM Error:"):
            logger.error(ret)
            return ""

        data = self._llm_cli.try_parse_json(ret)
        logger.info(f"parsed LLM output: {data}")

        return data

    async def tool_go_page(self, url: str) -> str:
        if url in self._visited_pages:
            return "you have visited this url. Please try another url, tool or go back to last page"

        self._visited_pages.add(url)

        async def __cached_or_download(_url: str) -> str:
            # cache = caches["download"]
            # cache_key = f"html|{quick_hash(_url)}"
            _html = ""
            # _html = await cache.aget(cache_key)
            if not _html:
                assert self._spider
                await self._spider.async_go_to(_url)
                await self._spider.async_sleep(1, 2)
                page = await self._spider.async_get_page()
                platform = self._spider.get_platform(url)
                if platform == "instagram":
                    if (
                        await page.locator(
                            'body main[role="main"] > div > header > section:nth-child(4) button'
                        ).count()
                        > 0
                    ):
                        await page.click('body main[role="main"] > div > header > section:nth-child(4) button')
                        await page.wait_for_selector('h1:has-text("Links")')
                        logger.info("links loaded")
                _html = await page.content()
                # if _html:
                #     await cache.aset(cache_key, _html, timeout=3600 * 24)
                # with open(Path(__file__).parent / f'{normalize_uri(_url).replace("/", "_")}.html', "w") as f:
                #     f.write(_html)
            return _html

        html = await __cached_or_download(url)
        text = self.html_to_text(html)
        return text

    @staticmethod
    def html_to_text(html: str) -> str:
        """convert html to Markdown. If failed, convert it to cleaned HTML"""
        h = html2text.HTML2Text()
        h.ignore_links = False
        h.ignore_images = True
        h.ignore_emphasis = False
        h.body_width = 0
        text = h.handle(html)
        if len(text) < 30:
            logger.info("html2text failed. try parsing origin html")
            soup = BeautifulSoup(html, "html.parser")
            [s.extract() for s in soup.select("script")]
            [s.extract() for s in soup.select("link")]
            [s.extract() for s in soup.select("style")]
            [s.extract() for s in soup.select('img[src^="data:image/"]')]
            text = soup.prettify()
        return text

    async def tool_go_back(self) -> str:
        # remove failed last
        self._visit_stack.pop()
        # pop last
        last_action = self._visit_stack.pop()
        if last_action:
            tool, url, return_value = last_action
            return f"You are backed to last step.(tool='{tool}', url='{url}'). Please try another way. My response (return_value) as below:\n-----\n{return_value}"
        else:
            return "You are backed to start point. No more steps to go back."


SYS_PROMPT = """You are a influencer marketing manager who finds target influencers' contact information such as emails or phone numbers on social media.
Now Your job is to take a influencer profile page as start point to find his/her email or phone number.

Generally, the contact information can be found in the following places:
- the user(or channel) bio or description.
- A button or link on the social media page. click the button then contact info shows.
- A link in bio/desc to a composite page of all kinds of pages the user's want to show. Sometimes, the contact info is shows in this page.
    - Links in the composite page mostly are tiktok, instagram, youtube, facebook, twitter, linkedin, personal/official site, shopping page or etc. If can not found email on page, we may need to follow the links to find on them.
- A link in bio/desc to personal/official site (sometimes it's a shopping page).
- A comment of a video/image/thread of this user. You may need to read through the comments to find it. NOTE the comment must be left by the influencer self. Do NOT collect other user's contact.

Do not visit links as original order. You can visit the links by following priority:
1. personal/official site.
2. composite page (linktr.ee or similar)
3. facebook profile
4. tiktok profile
5. instagram or youtube page.
6. others

We have some tools you can use (all input & output are strings):
- "go_page": visit a page or link.
    - arg: url of the page
    - return_value: the content of the page.
- "found": You found the contact information. The job and conversation finished.
    - arg: the found contact information.
    - return_value: no
- "not_found": You have not found the contact information. The job and conversation finished.
    - arg: ""
    - return_value: no

We will finish the job by conversation. In each turn of the conversation:
- You output tools and arg in JSON format '{"tool": <tool_name>, "arg": <arg>, "links": <links_you_possible_want_to_visit_which_are_related_to_current_account_and_found_in_this_page>, "reason":<reason_why_you_use_this_tool_in_few_words>}'
- Then the tools will output the <return_value> in the turn.
- Once you found the contact information, use "found" tool to let me know.
- If you can not find the contact information AFTER SOME EFFECTIVE TURNS. use "not_found" tool to let me know.

We start the conversation the profile url or its content.
"""

# prompt backup
"""
- "go_back": go back to your last step. I will let you know last tool, arg and return_value.
    - arg: ""
    - return_value: my instruction with the return_value of last step.
- "click": click a button or link on this page.
    - arg: the xpath to this button or link
    - return_value: the content of the page.
"""

FEW_SHOTS: list[dict[str, str]] = list()

if __name__ == "__main__":
    import logging
    import colorlog

    colorlog.basicConfig(
        level=logging.DEBUG,
        format="%(log_color)s[%(levelname).05s] [%(asctime)s] [%(name)s:%(lineno)d] %(message)s",
    )

    # url = 'https://www.tiktok.com/@kqtiewhitney2'  # ✅personal home page
    url = "https://www.youtube.com/@CodeMiko/"  # ✅linktree
    # url = 'https://www.youtube.com/@candy.superstar' # no ground truth ❌recursive loop OR go incorrect website

    # ✅actually can be found in FB profile only (<EMAIL>). ❗️BUT the agent found another (<EMAIL>) in a video's desc.
    # url = 'https://www.youtube.com/@Tati' # can be found in fb profile

    # url = 'https://www.youtube.com/@SparkTVMovies' # ✅found on official site
    # url = 'https://www.youtube.com/@wewearcute' # ✅found on linktr.ee page
    # url = 'https://www.youtube.com/@KellyStrack' # ✅found in youtube bio
    # url = 'https://www.youtube.com/@pagesix'

    # url = 'https://www.instagram.com/iredefh'   # ✅ bio
    # url = 'https://www.instagram.com/verozazo'  # ❌ not found
    # url = 'https://www.instagram.com/alfarodiana04' # ❌ not found
    # url = 'https://www.instagram.com/xh_beaute' # ✅ found phone 078329449
    # url = 'https://www.instagram.com/c_makeup_' # ❌ links on linkr.bio are dynamic buttons.
    # url = 'https://www.instagram.com/thesecret365'

    # url = 'https://www.instagram.com/therisingcircle' # ✅found in personal site
    # url = "https://www.instagram.com/synchronistic"

    async def main() -> None:
        import os

        os.environ["PROXY"] = '{"server": "http://127.0.0.1:1087"}'
        os.environ["HEADLESS"] = "false"
        cfg = VisualSpiderAgentConfig(max_depths=4)
        agent = VisualSpiderAgent(config=cfg)
        rc = await agent.run(url)
        print("------  result  ------")
        print(rc or "not found")
        print("----------------------")

    asyncio.run(main())
