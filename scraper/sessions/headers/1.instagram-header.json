{"user_id": 71192957845, "username": "song.57388", "global_headers": {"X-Ig-App-Locale": "en_US", "X-Ig-Device-Locale": "en_US", "X-Ig-Mapped-Locale": "en_US", "X-Bloks-Version-Id": "16e9197b928710eafdf1e803935ed8c450a1a2e3eb696bff1184df088b900bcf", "X-Ig-Www-Claim": "hmac.AR0wlj3nvRmL0E6CljIi-aOKVdU_WiCqI65aDGNk7AARu_yl", "X-Ig-Device-Id": "f27f5688-8663-4e6a-ab0e-b40178f4af1b", "X-Ig-Family-Device-Id": "0f5147f4-642d-4b21-aa4e-281042116b2c", "X-Ig-Android-Id": "android-1f2eabae8f7ebf78", "X-Ig-Timezone-Offset": "28800", "X-Bloks-Prism-Button-Version": "CONTROL", "X-Bloks-Prism-Colors-Enabled": "false", "X-Bloks-Prism-Ax-Base-Colors-Enabled": "false", "X-Bloks-Prism-Font-Enabled": "false", "Accept-Encoding": "gzip, deflate, br", "X-Fb-Http-Engine": "<PERSON><PERSON>", "X-Fb-Client-Ip": "True", "X-Fb-Server-Cluster": "True", "X-Bloks-Is-Layout-Rtl": "false", "X-Ig-Capabilities": "3brTv10=", "Accept-Language": "en-US", "User-Agent": "Instagram 361.********* Android (34/14; 420dpi; 1080x2219; Google/google; sdk_gphone64_arm64; emu64a; ranchu; en_US; 674674275)", "Authorization": "Bearer IGT:2:eyJkc191c2VyX2lkIjoiNzExOTI5NTc4NDUiLCJzZXNzaW9uaWQiOiI3MTE5Mjk1Nzg0NSUzQTVjTTBYRmtxU3dpbUg1JTNBMCUzQUFZY29HOHEwa05WUDRMNTBsUWk5VldmZlVSOTVCZmJsY0JMNmdxeVUxdyJ9", "X-Mid": "Z4fC0gABAAHCSdImU4HG9IpKiK1r", "X-Fb-Connection-Type": "MOBILE.HSPA", "X-Ig-Connection-Type": "MOBILE(HSPA)", "X-Fb-Network-Properties": "DUN;Metered;Captive;LocalAddrs=/fe80::1416:3aff:fe56:e4e8,/*********,/fec0::1416:3aff:fe56:e4e8,/fec0::f55e:24c0:cc55:93d,;", "X-Ig-App-Id": "567067343352427", "Ig-U-Ds-User-Id": "71192957845", "Ig-Intended-User-Id": "71192957845"}, "fetch_user_info": {"headers": {"Priority": "u=3", "X-Ig-Transfer-Encoding": "chunked", "X-Ig-Client-Endpoint": "UserDetailFragment:profile", "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"}}, "get_profile_posts": {"headers": {"Priority": "u=3", "X-Ig-Client-Endpoint": "MainFeedFragment:feed_timeline"}}, "fetch_userid_by_username": {"headers": {"X-Ig-Transfer-Encoding": "chunked", "X-Ig-Client-Endpoint": "UserSerpGridFragment:serp_users"}}}