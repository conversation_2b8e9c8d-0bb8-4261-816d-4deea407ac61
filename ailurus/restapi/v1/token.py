import django
from asgiref.sync import sync_to_async
from django.contrib.auth import authenticate
from django.http import HttpRequest
from ninja.router import Router

from .schema import LoginSchema
from ..response import ApiResults, ApiResponse
from ...authentication import JwtSecret

auth_router = Router(tags=["authorization"])


@auth_router.post("/token/", auth=None, summary="to obtain token by login")
async def refresh_token(request: HttpRequest, payload: LoginSchema) -> ApiResponse:
    user = await sync_to_async(authenticate)(request, username=payload.username, password=payload.password)
    if user is not None:
        token = JwtSecret.refresh_token(user)
        return ApiResults.SUCCESS(data=token)
    else:
        return ApiResults.UNAUTHORIZED


@auth_router.post("/csrf_token/", auth=None, summary="to obtain CSRF token")
async def get_csrf_token(request: HttpRequest) -> ApiResponse:
    await sync_to_async(django.middleware.csrf.get_token)(request)
    return ApiResults.SUCCESS()
