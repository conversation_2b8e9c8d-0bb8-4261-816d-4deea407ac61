"""tools for agents"""

import asyncio
import inspect
import json
from abc import ABC, abstractmethod
from typing import Protocol, Any, Callable, Dict, List, Optional, AsyncGenerator, Iterator

from asgiref.sync import async_to_sync
from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client
from pydantic import BaseModel, Field

from pycommon.logwrapper import get_log_wrapper
from pylibs.pytable import PySimpleTable
from .base import LlmAgent, LlmAgentConfig, AgentResult

logger = get_log_wrapper(__name__)


class InvokeAble(Protocol):
    """interface for invokable objects"""

    def invoke(self, *args: Any, **kwargs: Any) -> Any:
        """invoke the object"""

    async def async_invoke(self, *args: Any, **kwargs: Any) -> Any:
        """async invoke the object"""


class DescribeAble(Protocol):
    """interface for objects that can be described"""

    def describe(self) -> str:
        """describe the object"""


class CategorizeAble(Protocol):
    """interface for objects that can be categorized"""

    def get_categories(self) -> set[str]:
        """return categories of the object"""


class Tool(InvokeAble, DescribeAble, CategorizeAble):
    """Tool base"""

    name: str
    description: str
    categories: set[str]
    is_async: bool

    def as_prompt(self) -> str:
        """return tool as a LLM prompt"""
        return f"{self.name}: {self.description}"

    @abstractmethod
    def as_schema(self) -> dict[str, Any]:
        """return tool's schema as a LLM prompt"""


class AgentTool(Tool, ABC):
    """Tool for LLM agent"""

    def __init__(self, name: str, description: str, categories: set[str] | None = None, **kwargs: Any):
        self.name = name
        self.description = description
        self.categories = categories or set()

    def describe(self) -> str:
        return self.description

    def get_categories(self) -> set[str]:
        return self.categories

    def as_schema(self) -> dict[str, Any]:
        """Convert tool to LLM function calling format"""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.get_parameters_schema(),
            },
        }

    @abstractmethod
    def get_parameters_schema(self) -> dict[str, Any]:
        """Get JSON schema for tool parameters"""


class LocalAgentTool(AgentTool):
    """Local agent tool implementation"""

    def __init__(self, name: str, description: str, categories: set[str] | None = None, **kwargs):
        """
        Args:
            name: Tool name
            description: Tool description
            categories: Tool categories
            func: Tool function
        """
        super().__init__(name, description, categories)
        self.func: Callable = kwargs.get("func")
        self.is_async = inspect.iscoroutinefunction(self.func)
        self._parameters_schema = self._generate_schema()

    def _generate_schema(self) -> dict[str, Any]:
        """Generate JSON schema from function signature"""
        sig = inspect.signature(self.func)
        parameters = {"type": "object", "properties": {}, "required": []}

        for param_name, param in sig.parameters.items():
            param_type = "string"  # Default type
            if param.annotation != inspect.Parameter.empty:
                if param.annotation == str:
                    param_type = "string"
                elif param.annotation == int:
                    param_type = "integer"
                elif param.annotation == float:
                    param_type = "number"
                elif param.annotation == bool:
                    param_type = "boolean"
                elif param.annotation == list or str(param.annotation).startswith("typing.List"):
                    param_type = "array"
                elif param.annotation == dict or str(param.annotation).startswith("typing.Dict"):
                    param_type = "object"

            parameters["properties"][param_name] = {"type": param_type}

            if param.default == inspect.Parameter.empty:
                parameters["required"].append(param_name)

        return parameters

    def get_parameters_schema(self) -> dict[str, Any]:
        return self._parameters_schema

    def invoke(self, *args: Any, **kwargs: Any) -> Any:
        """Synchronous invoke"""
        if self.is_async:
            raise ValueError(f"Tool {self.name} is async, use async_invoke instead")
        return self.func(*args, **kwargs)

    async def async_invoke(self, *args: Any, **kwargs: Any) -> Any:
        """Asynchronous invoke"""
        if self.is_async:
            return await self.func(*args, **kwargs)
        else:
            return await asyncio.to_thread(self.func, *args, **kwargs)


class McpAgentTool(AgentTool):
    """MCP agent tool implementation"""

    def __init__(
        self,
        name: str,
        description: str,
        categories: set[str] | None = None,
        **kwargs: Any,
    ):
        """
        Args:
            name: Tool name
            description: Tool description
            categories: Tool categories
            conf: MCP tool configuration
        """
        super().__init__(name, description, categories)
        self.conf = kwargs.get("conf", {})  # MCP tool config

        # Initialize parameters schema from MCP tool config
        self._parameters_schema = self.conf.get(
            "parameters_schema", {"type": "object", "properties": {}, "required": []}
        )

    def get_parameters_schema(self) -> Dict[str, Any]:
        return self._parameters_schema

    def invoke(self, *args: Any, **kwargs: Any) -> Any:
        """Synchronous invoke - not supported for MCP tools"""
        raise ValueError(f"MCP tool {self.name} only supports async invocation, use async_invoke instead")

    async def async_invoke(self, *args: Any, **kwargs: Any) -> Any:
        """Asynchronous invoke for MCP tools"""
        # MCP tools need to be called through the MCP session
        # This is a placeholder - actual implementation would need access to the MCP session
        # In practice, this would be handled by the ToolAgent which has access to the MCP client
        raise NotImplementedError(
            f"MCP tool {self.name} invocation must be handled by ToolAgent with MCP session access"
        )


class ToolRepo(Protocol):
    """interface for a tool repository"""

    def load(self, definition: str | dict) -> None:
        """load repo definitions from source.
        Source depends on different Repo types.
        """

    def list(self, page: int = 0, page_size: int = 100, **filters: Any) -> list[Tool]:
        """list all tools

        Args:
            page: page number
            page_size: page size
            filters: filter conditions
                e.g.
                adults = table.filter(age={'>=': 18})
                young = table.filter(name={'contains': 'A'})

        """

    def register(self, tool: Tool) -> bool:
        """register the tool to agent"""

    def unregister(self, name: str) -> None:
        """unregister the tool from agent"""

    def clear(self) -> None:
        """clear all tools in repo"""

    def as_prompt(self) -> str:
        """return tool list as a LLM prompt"""

    def __getitem__(self, name: str) -> Tool:
        """get tool by name"""

    def __setitem__(self, name: str, tool: Tool) -> None:
        """register tool by name"""

    def __delitem__(self, name: str):
        """unregister tool by name"""

    def __contains__(self, name: str) -> bool:
        """check if tool exists"""

    def __iter__(self) -> Iterator[Tool]:
        """iterate all tools"""

    def __len__(self) -> int:
        """return tool count"""


class BaseToolRepo(ToolRepo, ABC):
    """base class for tool repository"""

    _tools_table: PySimpleTable | None = None

    def __init__(self, **kwargs: Any):
        self._tools_table = PySimpleTable(columns=["name", "description", "categories", "tool"])

    def __getitem__(self, name: str) -> LocalAgentTool | None:
        """Get tool by name"""
        t = self._tools_table.filter(name=name)
        if len(t) > 0:
            return t["tool"][0]
        return None

    def __setitem__(self, name: str, tool: Tool) -> None:
        assert name == tool.name
        self.register(tool)

    def __delitem__(self, name: str):
        self.unregister(name)

    def __contains__(self, name: str) -> bool:
        """Check if tool exists"""
        tool = self.__getitem__(name=name)
        return tool is not None

    def __iter__(self) -> Iterator[LocalAgentTool]:
        """Iterate all tools"""
        return iter(self._tools_table["tool"])

    def __len__(self) -> int:
        """return tool count"""
        return len(self._tools_table)

    def as_prompt(self) -> str:
        """return tool list as a LLM prompt"""
        tools = self.list()
        tool_prompts = [f"{t.name}: {t.description}" for t in tools]
        return "\n".join(tool_prompts)

    def list(self, page: int = 0, page_size: int = 100, **filters: Any) -> list[Tool]:
        """List all tools with pagination"""

        table = self._tools_table
        if len(filters) > 0:
            table = table.filter(**filters)
        if page > 0 and 0 < page_size < 100:
            table = table.paginate(page, page_size)

        tools = list(table["tool"])

        return tools

    def register(self, tool: Tool) -> bool:
        """Register a tool"""
        table = self._tools_table
        name = tool.name

        # Check if name already exists
        if name in table["name"]:
            logger.warning(f"Tool '{name}' already exists, registration failed")
            return False

        table.add_row(
            {"name": name, "description": tool.describe(), "categories": tool.get_categories() or set(), "tool": tool}
        )
        logger.debug(f"Registered tool: {name}")
        return True

    def unregister(self, name: str) -> None:
        """Unregister a tool"""
        self._tools_table.delete_rows(lambda row: row["name"] == name)
        logger.debug(f"Unregistered tool: {name}")

    def clear(self) -> None:
        """Clear all tools in repo"""
        self._tools_table.clear()
        logger.debug("Cleared all tools from local repository")


class LocalToolRepo(ToolRepo):
    """local tool repository"""

    def load(self, definition: str | dict) -> None:
        """Load tool definitions from file or dict"""
        if isinstance(definition, str):
            raise NotImplementedError(
                "Local agent tool repo can not load from string due to function can not be serialized."
            )
        elif isinstance(definition, dict):
            pass
        else:
            logger.error(f"Invalid definition type: {type(definition)}")
            return

        table = self._tools_table

        # Load each tool definition
        for tool_def in definition.get("tools", []):
            name = tool_def.get("name")
            desc = tool_def.get("description", "")
            categories = set(tool_def.get("categories", []))
            func = tool_def.get("func")

            if not name:
                logger.warning("Tool definition missing name, skipping")
                continue
            if not func:
                logger.warning("Tool definition missing function, skipping")
                continue

            tool = LocalAgentTool(name, desc, categories, func=func)
            self.register(tool)

        logger.info(f"Loaded {len(table)} local tools definition: {table['name']}")


class McpToolRepo(BaseToolRepo):
    """MCP tool repository"""

    def __init__(self):
        super().__init__()
        self._mcp_server_config: Dict[str, Any] = {}
        self._mcp_resources: List[Dict[str, Any]] = []
        self._mcp_prompts: List[Dict[str, Any]] = []

    def load(self, definition: str | dict) -> None:
        """Load MCP server configuration and tools"""
        if isinstance(definition, str):
            definition = json.loads(definition)

        # Store MCP server configuration
        self._mcp_server_config = definition

        # Load tools from MCP server
        try:
            # Try to run in existing event loop
            import asyncio

            loop = asyncio.get_running_loop()
            # If we're in an async context, we can't use async_to_sync
            logger.warning("Cannot load MCP tools synchronously in async context. Use async_load() instead.")
        except RuntimeError:
            # No running event loop, use async_to_sync
            async_to_sync(self._async_load_mcp_tools)()

    async def async_load(self, definition: str | dict) -> None:
        """Async version of load method for use in async contexts"""
        if isinstance(definition, str):
            definition = json.loads(definition)

        # Store MCP server configuration
        self._mcp_server_config = definition

        # Load tools from MCP server
        await self._async_load_mcp_tools()

    async def _async_load_mcp_tools(self) -> None:
        """Load and cache MCP tools, resources, and prompts from MCP server configuration"""
        config = self._mcp_server_config
        server_url = config.get("server_url", "")

        if not server_url:
            logger.warning("No server_url provided in MCP configuration")
            return

        try:
            logger.info(f"Connecting to MCP server: {server_url}")

            async with streamablehttp_client(server_url) as (read, write, _):
                async with ClientSession(read, write) as session:
                    await session.initialize()

                    # Load tools
                    tools_response = await session.list_tools()
                    tools = tools_response.tools if tools_response else []
                    logger.info(f"Found {len(tools)} tools from MCP server")

                    # # Load resources
                    # resources_response = await session.list_resources()
                    # resources = resources_response.resources if resources_response else []
                    # logger.info(f"Found {len(resources)} resources from MCP server")
                    #
                    # # Load prompts
                    # prompts_response = await session.list_prompts()
                    # prompts = prompts_response.prompts if prompts_response else []
                    # logger.info(f"Found {len(prompts)} prompts from MCP server")
                    #
                    # # Store resources and prompts for later use
                    # self._mcp_resources = [
                    #     {
                    #         "uri": str(resource.uri),
                    #         "name": resource.name,
                    #         "description": resource.description,
                    #         "mimeType": resource.mimeType,
                    #     }
                    #     for resource in resources
                    # ]
                    #
                    # self._mcp_prompts = [
                    #     {
                    #         "name": prompt.name,
                    #         "description": prompt.description,
                    #         "arguments": [
                    #             {
                    #                 "name": arg.name,
                    #                 "description": arg.description,
                    #                 "required": arg.required,
                    #             }
                    #             for arg in (prompt.arguments or [])
                    #         ],
                    #     }
                    #     for prompt in prompts
                    # ]

                    # Register tools
                    for tool in tools:
                        name = tool.name
                        desc = tool.description or ""
                        categories = set()  # MCP tools don't have categories by default

                        if not name:
                            logger.warning("MCP tool missing name, skipping")
                            continue

                        # Check if name already exists
                        if name in self:
                            logger.warning(f"MCP tool '{name}' already exists, skipping")
                            continue

                        # Convert MCP tool schema to our format
                        tool_config = {
                            "name": name,
                            "description": desc,
                            "parameters_schema": tool.inputSchema
                            or {"type": "object", "properties": {}, "required": []},
                            "mcp_tool": tool,  # Store original MCP tool for later use
                        }

                        # Create MCP tool instance
                        mcp_tool = McpAgentTool(name, desc, categories, conf=tool_config)
                        self.register(mcp_tool)
                        logger.debug(f"Loaded MCP tool: {name}")

        except Exception as e:
            logger.error(f"Failed to load MCP tools from {server_url}: {e}")
            raise

    def get_resources(self) -> List[Dict[str, Any]]:
        """Get all loaded MCP resources"""
        return self._mcp_resources.copy()

    def get_prompts(self) -> List[Dict[str, Any]]:
        """Get all loaded MCP prompts"""
        return self._mcp_prompts.copy()

    def get_resource_by_uri(self, uri: str) -> Dict[str, Any] | None:
        """Get a specific resource by URI"""
        for resource in self._mcp_resources:
            if resource["uri"] == uri:
                return resource
        return None

    def get_prompt_by_name(self, name: str) -> Dict[str, Any] | None:
        """Get a specific prompt by name"""
        for prompt in self._mcp_prompts:
            if prompt["name"] == name:
                return prompt
        return None


# Global repositories
_local_repo = LocalToolRepo()
_mcp_repo = McpToolRepo()


def agent_tool(
    func: Optional[Callable] = None,
    *,
    repo: ToolRepo = _local_repo,
    name: Optional[str] = None,
    description: Optional[str] = None,
    categories: set[str] | None = None,
):
    """Decorator to register a function as an agent tool.

    Args:
        func: Function to be decorated (when used without parentheses).
        repo: Tool repository to register the tool. Default is local repository.
        name: Tool name. If None, uses function name.
        description: Tool description. If None, uses function docstring.
        categories: Tool categories for organization.

    Example:
        # Simple usage without parameters
        @agent_tool
        def get_weather(city: str) -> str:
            return f"Weather in {city}: sunny, 25°C"

        # Usage with parameters
        @agent_tool(description="Get current weather")
        def get_weather(city: str) -> str:
            return f"Weather in {city}: sunny, 25°C"

        @agent_tool(categories={"search", "web"})
        async def async_search(query: str) -> List[str]:
            '''Search for information'''
            return ["result1", "result2"]

        @agent_tool(repo=my_custom_repo, name="custom_name")
        def my_function():
            pass
    """

    def decorator(f: Callable) -> Callable:
        tool_name = name or f.__name__
        tool_desc = description or (f.__doc__ or "").strip()

        # Register the tool in repository
        tool = LocalAgentTool(tool_name, tool_desc, categories, func=f)
        success = repo.register(tool)
        if not success:
            logger.warning(f"Failed to register tool '{tool_name}' - name may already exist")

        return f

    # If func is provided, it means the decorator was used without parentheses
    if func is not None:
        return decorator(func)

    # Otherwise, return the decorator function for use with parentheses
    return decorator


class ToolCallResult(BaseModel):
    """Result of a tool call."""

    tool_name: str
    success: bool
    result: Any = None
    error: Optional[str] = None


class ToolAgentConfig(LlmAgentConfig):
    """Configuration for ToolAgent."""

    name: str = Field(default="ToolAgent")
    max_tool_calls: int = Field(default=10)
    tool_call_timeout: int = Field(default=30)
    available_tools: Optional[List[str]] = Field(default=None)
    use_local_tools: bool = Field(default=True)
    use_mcp_tools: bool = Field(default=True)


class ToolAgent(LlmAgent):
    """Agent with tool calling capabilities using the tool repository system."""

    config: ToolAgentConfig

    def __init__(self, config: Optional[ToolAgentConfig] = None, **kwargs):
        super().__init__(config, **kwargs)
        self.local_repo = get_local_tool_repo()
        self.mcp_repo = get_mcp_tool_repo()
        self._mcp_session: Optional[ClientSession] = None

    def _get_available_tools(self) -> Dict[str, AgentTool]:
        """Get all available tools from repositories"""
        tools = {}

        # Add local tools
        if self.config.use_local_tools:
            for tool in self.local_repo.list():
                if self.config.available_tools is None or tool.name in self.config.available_tools:
                    tools[tool.name] = tool

        # Add MCP tools
        if self.config.use_mcp_tools:
            for tool in self.mcp_repo.list():
                if self.config.available_tools is None or tool.name in self.config.available_tools:
                    tools[tool.name] = tool

        return tools

    def _get_tools_for_llm(self) -> List[Dict[str, Any]]:
        """Format tools for LLM function calling"""
        tools = self._get_available_tools()
        return [tool.as_schema() for tool in tools.values()]

    async def _execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> ToolCallResult:
        """Execute a tool call safely"""
        available_tools = self._get_available_tools()

        if tool_name not in available_tools:
            return ToolCallResult(tool_name=tool_name, success=False, error=f"Tool '{tool_name}' not found")

        tool = available_tools[tool_name]

        try:
            if isinstance(tool, LocalAgentTool):
                result = await asyncio.wait_for(tool.async_invoke(**arguments), timeout=self.config.tool_call_timeout)
            elif isinstance(tool, McpAgentTool):
                # Handle MCP tool call through MCP session
                result = await asyncio.wait_for(
                    self._execute_mcp_tool(tool, arguments), timeout=self.config.tool_call_timeout
                )
            else:
                result = await asyncio.wait_for(
                    asyncio.to_thread(tool.invoke, **arguments), timeout=self.config.tool_call_timeout
                )

            return ToolCallResult(tool_name=tool_name, success=True, result=result)

        except asyncio.TimeoutError:
            return ToolCallResult(
                tool_name=tool_name,
                success=False,
                error=f"Tool execution timed out after {self.config.tool_call_timeout} seconds",
            )
        except Exception as e:
            return ToolCallResult(tool_name=tool_name, success=False, error=f"Tool execution failed: {str(e)}")

    async def _execute_mcp_tool(self, tool: McpAgentTool, arguments: Dict[str, Any]) -> Any:
        """Execute an MCP tool call through the MCP session"""
        # Get MCP server configuration
        mcp_config = self.mcp_repo._mcp_server_config
        server_url = mcp_config.get("server_url", "")

        if not server_url:
            raise ValueError("No MCP server URL configured")

        # Create a new MCP session for this tool call
        # In a production system, you might want to reuse sessions or pool them
        async with streamablehttp_client(server_url) as (read, write, _):
            async with ClientSession(read, write) as session:
                await session.initialize()

                # Call the MCP tool
                result = await session.call_tool(tool.name, arguments)

                # Extract result content
                if result.content:
                    # Handle different content types
                    content_results = []
                    for content in result.content:
                        if hasattr(content, "text"):
                            content_results.append(content.text)
                        elif hasattr(content, "data"):
                            content_results.append(str(content.data))
                        else:
                            content_results.append(str(content))

                    # Return combined content or structured data if available
                    if hasattr(result, "structuredContent") and result.structuredContent:
                        return {"content": content_results, "structured": result.structuredContent}
                    else:
                        return "\n".join(content_results) if content_results else "Tool executed successfully"
                else:
                    return "Tool executed successfully"

    async def _async_run(self, user_query: str, **kwargs: Any) -> AgentResult:
        """Run the agent with tool calling capability"""
        tools = self._get_tools_for_llm()

        if not tools:
            return await self.async_invoke_llm(user_query, resp_type="text")

        system_prompt = (
            "You are an AI assistant with access to tools. "
            "Use the available tools to help answer the user's question. "
            "Call tools when needed and provide a comprehensive response based on the results."
        )

        messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_query}]

        tool_calls_count = 0
        max_calls = self.config.max_tool_calls

        while tool_calls_count < max_calls:
            response = await self.async_invoke_llm(prompt=messages, resp_type="object")

            if not isinstance(response, dict) or "tool_calls" not in response:
                return response

            tool_calls = response["tool_calls"]
            if not tool_calls:
                return response

            messages.append({"role": "assistant", "content": response.get("content", ""), "tool_calls": tool_calls})

            for tool_call in tool_calls:
                tool_calls_count += 1

                if tool_calls_count >= max_calls:
                    logger.warning(f"Reached maximum tool calls limit: {max_calls}")
                    break

                function = tool_call.get("function", {})
                tool_name = function.get("name")
                arguments = function.get("arguments", {})

                if isinstance(arguments, str):
                    try:
                        arguments = json.loads(arguments)
                    except json.JSONDecodeError:
                        arguments = {}

                result = await self._execute_tool(tool_name, arguments)

                tool_result = {
                    "role": "tool",
                    "tool_call_id": tool_call.get("id", f"call_{tool_calls_count}"),
                    "name": tool_name,
                    "content": json.dumps({"success": result.success, "result": result.result, "error": result.error}),
                }
                messages.append(tool_result)

                self.ob.carry(
                    "tool_calls", {"tool_name": tool_name, "arguments": arguments, "result": result.model_dump()}
                )

            if tool_calls_count >= max_calls:
                break

        final_response = await self.async_invoke_llm(
            prompt=messages
            + [{"role": "user", "content": "Please provide a final response based on the tool results above."}],
            resp_type="text",
        )

        return final_response

    async def _async_iter(self, **kwargs: Any) -> AsyncGenerator:
        """Iterate agent results - not implemented for tool agent"""
        raise NotImplementedError("Streaming not supported for ToolAgent yet")


def get_local_tool_repo() -> LocalToolRepo:
    """Get local agent tool repo"""
    return _local_repo


def get_mcp_tool_repo() -> McpToolRepo:
    """Get local MCP agent tool repo"""
    return _mcp_repo
