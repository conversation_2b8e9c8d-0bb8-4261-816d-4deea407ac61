import random
from typing import Any

from agent.models import (
    Profile,
)
from django import forms
from django.contrib import admin
from django.db.models import QuerySet, Sum
from django.http import HttpRequest
from django.utils.safestring import mark_safe
from django_json_widget.widgets import JSONEditorWidget
from pycommon.django_ext.model import CreateTimeMixin, UpdateTimeMixin

from pycommon.utils.strutil import ellipse


def text_widget(cols: int = 150, rows: int = 25) -> forms.Textarea:
    return forms.Textarea(attrs={"readonly": "readonly", "cols": str(cols), "rows": str(rows)})


@admin.display(description="created at")
def created_at_disp(obj: CreateTimeMixin) -> str:
    # TODO(data): Timezone
    return mark_safe(obj.created_at.strftime("%y-%m-%d<br/>%H:%M:%S"))


@admin.display(description="updated at")
def updated_at_disp(obj: UpdateTimeMixin) -> str:
    # TODO(data): Timezone
    return mark_safe(obj.updated_at.strftime("%y-%m-%d<br/>%H:%M:%S"))


class ChatMsgAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "related_disp",
        "role",
        # "user",
        "text_disp",
        created_at_disp,
    ]
    list_display_links = ["id", "text_disp"]
    search_fields = ["text"]
    list_filter = ["user", "role", "chat__platform", "chat_turn__message_source"]
    date_hierarchy = "created_at"
    raw_id_fields = ["user", "chat", "chat_turn"]
    readonly_fields = ["role", "created_at"]

    @admin.display(description="text")
    def text_disp(self, obj: ChatMsg) -> str:
        message_source = obj.chat_turn.message_source if obj.role == "user" else ""
        message_source = (
            f"<br /><span style='color: goldenrod;'>{message_source}</span>" if message_source == "example" else ""
        )
        return mark_safe(f"{ellipse(obj.text, 100)} {message_source}")

    @admin.display(description="created_at")
    def created_at_disp(self, obj: ChatMsg) -> str:
        return obj.created_at.strftime("%Y-%m-%d<br/>%H-%M-%S")  # TODO: TZ

    @admin.display(description="related")
    def related_disp(self, obj: ChatMsg) -> str:
        return mark_safe(f"chat: {obj.chat.id} | turn: {obj.chat_turn.id}<br/>{obj.user}")


class ChatTurnAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "user_msg_str",
    ]

    list_filter = ["message_source"]

    list_display_links = ["id", "user_msg_str"]

    @admin.display(description="text")
    def user_msg_str(self, chat_turn: ChatTurn) -> str:
        message_source = chat_turn.message_source
        message_source = (
            f'<br /><span style="color: yellow">{message_source}</span>' if message_source == "example" else ""
        )
        return mark_safe(
            f"{chat_turn.created_at.strftime('%Y-%m-%d %H-%M-%S')} : {ellipse(chat_turn.user_msg, 100)} {message_source}"
        )


class ChatTurnStatesForm(forms.ModelForm):
    user_msg = forms.CharField(widget=text_widget(rows=3))
    ai_resp = forms.CharField(widget=text_widget())
    extra_data = forms.JSONField(widget=JSONEditorWidget)
    llm_prompts = forms.JSONField(widget=JSONEditorWidget)
    llm_outputs = forms.JSONField(widget=JSONEditorWidget)
    llm_cost = forms.CharField(widget=text_widget(rows=5))
    retrieval = forms.JSONField(widget=JSONEditorWidget)
    perf = forms.JSONField(widget=JSONEditorWidget(height=200))
    citations = forms.JSONField(widget=JSONEditorWidget)
    errors = forms.CharField(widget=text_widget())

    def __init__(self, *args: Any, **kwargs: Any):
        super(ChatTurnStatesForm, self).__init__(*args, **kwargs)
        instance = kwargs.get("instance")
        if instance:
            self.fields["ai_resp"].initial = instance.ai_resp
            self.fields["user_msg"].initial = instance.chat_turn.user_msg
            self.fields["citations"].initial = [
                {
                    "number": c.number,
                    "source": c.key,
                    "quotes": c.quotes,
                    "annotated_parts": c.shortened_annotated_doc,
                }
                for c in instance.citations
            ]


class ChatTurnStatesAdmin(admin.ModelAdmin):
    form = ChatTurnStatesForm
    fields = (
        "user_msg",
        "ai_resp",
        "retrieval",
        "perf",
        "citations",
        "extra_data",
        "llm_prompts",
        "llm_outputs",
        "llm_cost",
        "errors",
    )
    list_display = [
        "id",
        "related_disp",
        "user_msg_disp",
        "perf_disp",
        "cost_disp",
        "succeed_disp",
        created_at_disp,
    ]
    list_display_links = ["id", "user_msg_disp"]
    search_fields = [
        "chat_turn__user_msg",
        "retrieval",
        "sources",
        "extra_data",
        "errors",
        "llm_prompts",
        "llm_outputs",
    ]
    list_filter = [
        "chat_turn__chat__user__is_staff",
        "chat_turn__chat__platform",
        # "chat_turn__chat__user",
        "chat_turn__message_source",
    ]

    date_hierarchy = "created_at"
    raw_id_fields = ["chat_turn"]
    readonly_fields = ["created_at"]

    @admin.display(description="user_msg")
    def user_msg_disp(self, obj: ChatTurnStates) -> str:
        message_source = obj.chat_turn.message_source
        message_source = (
            f'<br /><span style="color: yellow">{message_source}</span>' if message_source == "example" else ""
        )
        return mark_safe(f"{ellipse(obj.user_msg, 100)} {message_source}")

    @admin.display(description="related")
    def related_disp(self, obj: ChatTurnStates) -> str:
        return mark_safe(f"chat: {obj.chat_turn.chat.id} | turn: {obj.chat_turn.id}<br/>{obj.chat_turn.chat.user}")

    @admin.display(description="platform")
    def platform_disp(self, obj: ChatTurnStates) -> str:
        return obj.chat_turn.chat.platform

    @admin.display(description="succeed?", boolean=True)
    def succeed_disp(self, obj: ChatTurnStates) -> bool:
        return obj.errors is None or len(obj.errors.strip()) == 0

    @admin.display(description="perf")
    def perf_disp(self, obj: ChatTurnStates) -> str:
        return mark_safe(
            "<br/>".join(
                [
                    (
                        f'<span style="white-space:nowrap">{"_".join(k.split("_")[1:])}:'
                        f'<span style="color:{"green" if v < 15 else "goldenrod" if v < 30 else "red"}">{v}</span>'
                    )
                    for k, v in obj.perf.items()
                ]
            )
        )

    @admin.display(description="LLM cost")
    def cost_disp(self, obj: ChatTurnStates) -> str:
        def _cost_html(v: Any) -> str:
            try:
                v = float(v)
                color = "red" if v > 0.05 else "blue" if v > 0.02 else "green"
            except:  # noqa: E722
                v = f"{v}(ERR)"
                color = "red"
            return f"<span style='color:{color};'>${v}</span>"

        costs = [c.split(":") for c in obj.llm_cost.splitlines()]

        if len(costs) < 5:
            rc = obj.llm_cost
        else:
            for c in costs:
                if len(c) != 2:
                    c = ("", "".join(c))
            lines = [
                f"<span style='white-space:nowrap;'>{costs[0][1]} calls</span>",
                f"<span style='white-space:nowrap;'>{costs[3][1]} tokens</span>",
                _cost_html(costs[4][1]),
            ]
            rc = mark_safe("<br/>".join(lines))
        return rc


class AgentStatesForm(forms.ModelForm):
    inputs = forms.JSONField(widget=JSONEditorWidget)
    extra_data = forms.JSONField(widget=JSONEditorWidget)
    llm_prompts = forms.JSONField(widget=JSONEditorWidget)
    llm_outputs = forms.JSONField(widget=JSONEditorWidget)
    llm_cost = forms.CharField(widget=text_widget(rows=5))
    retrieval = forms.JSONField(widget=JSONEditorWidget)
    perf = forms.JSONField(widget=JSONEditorWidget(height=200))
    citations = forms.JSONField(widget=JSONEditorWidget)
    errors = forms.CharField(widget=text_widget())


class AgentStatesAdmin(admin.ModelAdmin):
    form = AgentStatesForm
    fields = (
        "inputs",
        "retrieval",
        "perf",
        "extra_data",
        "llm_prompts",
        "llm_outputs",
        "llm_cost",
        "errors",
    )
    list_display = [
        "id",
        "agent_disp",
        "inputs_disp",
        "perf_disp",
        "cost_disp",
        "succeed_disp",
        created_at_disp,
    ]

    date_hierarchy = "created_at"
    readonly_fields = ["created_at"]

    @admin.display(description="agent")
    def agent_disp(self, obj: AgentStates) -> str:
        return obj.agent

    @admin.display(description="inputs")
    def inputs_disp(self, obj: AgentStates) -> str:
        return mark_safe("<br/>".join([f"{k}: {v}" for k, v in obj.inputs.items()]))

    @admin.display(description="succeed?", boolean=True)
    def succeed_disp(self, obj: AgentStates) -> bool:
        return obj.errors is None or len(obj.errors.strip()) == 0

    @admin.display(description="perf")
    def perf_disp(self, obj: AgentStates) -> str:
        return mark_safe(
            "<br/>".join(
                [
                    (
                        f'<span style="white-space:nowrap">{"_".join(k.split("_")[1:])}:'
                        f'<span style="color:{"green" if v < 15 else "goldenrod" if v < 30 else "red"}">{v}</span>'
                    )
                    for k, v in obj.perf.items()
                ]
            )
        )

    @admin.display(description="LLM cost")
    def cost_disp(self, obj: AgentStates) -> str:
        def _cost_html(v: Any) -> str:
            try:
                v = float(v)
                color = "red" if v > 0.05 else "blue" if v > 0.02 else "green"
            except:  # noqa: E722
                v = f"{v}(ERR)"
                color = "red"
            return f"<span style='color:{color};'>${v}</span>"

        costs = [c.split(":") for c in obj.llm_cost.splitlines()]

        if len(costs) < 5:
            rc = obj.llm_cost
        else:
            for c in costs:
                if len(c) != 2:
                    c = ("", "".join(c))
            lines = [
                f"<span style='white-space:nowrap;'>{costs[0][1]} calls</span>",
                f"<span style='white-space:nowrap;'>{costs[3][1]} tokens</span>",
                _cost_html(costs[4][1]),
            ]
            rc = mark_safe("<br/>".join(lines))
        return rc


# class SecFilingForm(forms.ModelForm):
#     content = forms.CharField(widget=text_widget(rows=5))


def create_mock_data(modeladmin: admin.ModelAdmin, request: HttpRequest, queryset: QuerySet) -> None:
    for _ in range(100):
        InvitationCode.objects.create(
            code="".join([random.choice("0123456789ABCDEFGHJKMNPQRSTUVWXYZ") for _ in range(6)])
        )


create_mock_data.short_description = "Generate batch invitation code"  # type: ignore


class InvitationCodeAdmin(admin.ModelAdmin):
    actions = [create_mock_data]
    list_filter = ["is_super_code"]
    list_display = [
        "id",
        "code",
        "is_super_code_str",
        "user_count",
        "user",
        "used_at",
        "created_at",
    ]

    @admin.display(description="is_super_code")
    def is_super_code_str(self, obj: InvitationCode) -> str:
        return "Yes" if obj.is_super_code else "No"

    @admin.display(description="user_count")
    def user_count(self, obj: InvitationCode) -> str:
        if obj.is_super_code:
            return str(len(obj.users))
        return "-"

    list_display_links = ["id", "code"]


class ExampleQuestionAdmin(admin.ModelAdmin):
    list_display = [
        "order",
        "text_str",
    ]

    list_display_links = ["text_str"]

    @admin.display(description="text")
    def text_str(self, obj: ExampleQuestion) -> str:
        return mark_safe(f"<div style='font-size: 110%;font-weight:bold;'>{obj.title}</div>{obj.text}")


class LlmCostAdmin(admin.ModelAdmin):
    change_list_template = "admin/llm_cost_change_list.html"
    list_display = [
        "id",
        "related_disp",
        "llm_model_name",
        "amount_disp",
        "token_disp",
        created_at_disp,
    ]
    list_display_links = ["id", "amount_disp"]
    search_fields = ["llm_model_name"]
    list_filter = [
        "chat_turn__chat__platform",
        "subject",
        "category",
        "llm_model_name",
        "user",
    ]
    date_hierarchy = "created_at"
    raw_id_fields = ["user", "chat_turn"]

    @admin.display(description="amount")
    def amount_disp(self, obj: LlmCost) -> str:
        return mark_safe(f"${obj.amount}")

    @admin.display(description="amount/token")
    def token_disp(self, obj: LlmCost) -> str:
        return mark_safe(
            f"in : ${obj.input_amount} / {obj.prompt_tokens}<br/>out: ${obj.output_amount} / {obj.completion_tokens}"
        )

    @admin.display(description="related")
    def related_disp(self, obj: LlmCost) -> str:
        if not obj.chat_turn:
            return mark_safe(f"turn: {obj.chat_turn}")
        return mark_safe(f"chat: {obj.chat_turn.chat.id} | turn: {obj.chat_turn.id}<br/>{obj.user}")

    def get_readonly_fields(self, request, obj=None):  # type: ignore
        return list(
            set(
                [field.name for field in self.opts.local_fields]
                + [field.name for field in self.opts.local_many_to_many]
            )
        )

    def changelist_view(self, request, extra_context=None):  # type: ignore
        response = super().changelist_view(request, extra_context)
        try:
            qs = response.context_data["cl"].queryset
        except (AttributeError, KeyError):
            return response

        response.context_data["summary"] = qs.aggregate(total=Sum("amount"))
        return response


# Register your models here.
admin.site.register(Chat)
admin.site.register(ChatMsg, ChatMsgAdmin)
admin.site.register(ChatTurn, ChatTurnAdmin)
admin.site.register(FetchedSource)
admin.site.register(Citation)
admin.site.register(ChatTurnStates, ChatTurnStatesAdmin)
admin.site.register(AgentStates, AgentStatesAdmin)
admin.site.register(Profile)
admin.site.register(Chart)
admin.site.register(InvitationCode, InvitationCodeAdmin)
admin.site.register(ContactUs)
admin.site.register(ExampleQuestion, ExampleQuestionAdmin)
admin.site.register(LlmCost, LlmCostAdmin)
admin.site.register(Metric)
admin.site.register(RelevantSnippet)
admin.site.register(History)
admin.site.register(CompanyWatchList)
