from abc import abstractmethod
from datetime import datetime
from typing import Union, Optional, runtime_checkable, Protocol, Literal

from pydantic import BaseModel

INFLUENCER_PLATFORMS = Literal["instagram", "tiktok", "youtube", "douyin"]

default_header_template = {
    "User-Agent": "",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*" ";q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Referer": "https://www.google.com/",
    "DNT": "1",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
}


class DownloadResult(BaseModel):
    url: str
    data: Union[str, bytes] = ""
    content_type: str = ""
    ex: Optional[Exception] = None


# -----  protocol  -----


@runtime_checkable
class Downloader(Protocol):
    """the abstracted interface for all downloader"""

    @abstractmethod
    def download(self, url: str) -> DownloadResult:
        """download from a url"""

    @abstractmethod
    def download_all(self, urls: list[str]) -> list[DownloadResult]:
        """download multiple urls in batch"""

    @abstractmethod
    async def async_download(self, url: str) -> DownloadResult:
        """download from a url"""

    @abstractmethod
    async def async_download_all(self, urls: list[str]) -> list[DownloadResult]:
        """download multiple urls in batch"""


@runtime_checkable
class CacheStore(Protocol):
    """the abstracted interface for downloader cache"""

    @abstractmethod
    def get(self, key: str) -> str | bytes | None:
        """get a value by key"""

    @abstractmethod
    def set(self, key: str, value: str | bytes, expires: int | datetime | None = None) -> None:
        """set a value by key"""

    @abstractmethod
    def delete(self, key: str) -> None:
        """delete a value by key"""

    @abstractmethod
    async def async_get(self, key: str) -> str | bytes | None:
        """get a value by key"""

    @abstractmethod
    async def async_set(self, key: str, value: str | bytes, expires: int | datetime | None = None) -> None:
        """set a value by key"""

    @abstractmethod
    async def async_delete(self, key: str) -> None:
        """delete a value by key"""


# ----  config  -----


class DownloaderConfig(BaseModel):
    """the base config for all downloader"""

    # name used to identify a Selenium/Playwright browser instance (cookie related)
    name: str = "default_downloader"

    # batch download concurrency
    concurrency: int = 4

    # configuration for cache
    cache: Optional[CacheStore] = None

    # timeout seconds for a single download process
    timeout: int = 30

    def check(self) -> bool:
        return True
