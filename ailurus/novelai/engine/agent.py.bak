import asyncio
import json
import logging
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Self

from asgiref.sync import sync_to_async
from pydantic import BaseModel

import pyllm
from pycommon.observation import PythonLogObserver
from pycommon.utils.confutils import get_str_env
from pycommon.utils.lang import DictMixin
from pyllm import LlmClient
from .author import Author, AUTHOR_DICT
from .consts import WEB_NOVEL_THEME_DICT, WebNovelLength
from .models.base import AgentStat
from .models.character import StoryCharacter
from .models.outline import StoryOutline
from .models.story import Story
from .models.world import StoryWorld

logger = logging.getLogger(__name__)
LLM_TIMEOUT = 60


# class AgentStateObserver(StatObserver):
#     def _observe(self, note: Notification) -> None:
#         AgentStat.objects.create(
#             agent=self.config.agent_name,
#             inputs=inputs,
#             outputs=outputs or {},
#             extra_data=ob.states,
#             retrieval=ob.retrieval,
#             recorded_function_calls=self.observer.function_calls,
#             errors=self.observer.format_error_admin(),
#             llm_cost=self.observer.get_llm_costs_str(),
#             perf=self.observer.perf,
#             llm_prompts=self.observer.llm_prompts,
#             llm_outputs=self.observer.llm_outputs,
#         )


# ==========   Base Agent Configs   ==========


class AgentConfig(BaseModel, DictMixin):
    """base config for all agents"""

    name: str = "agent"  # agent name
    streaming: bool = False
    llm_model: pyllm.ModelNameEnum = pyllm.ModelNameEnum.OLLAMA_DEEPSEEK_R1_8B

    class Config:
        arbitrary_types_allowed = True

    @staticmethod
    def get_agent_config_path() -> Path:
        return Path(get_str_env("AGENT_CONFIG_PATH") or "agent_configs")

    def __str__(self) -> str:
        return str(self.to_dict())

    @classmethod
    def load(cls, name: str, group: str) -> Self:
        """load from pre-defined json file
        NOTE: LLM_MODEL overriding env will not be effective if configs are loaded from json files.
        """
        config = cls()
        agent_config_path = cls.get_agent_config_path()
        path: Path = agent_config_path / group / f"{name}.json"
        if path.exists():
            try:
                with path.open() as fp:
                    config_dict = json.load(fp)
                config.update_from_dict(config_dict)
                logger.info(f'Agent config "{name}" ({cls.__name__}) loaded from group "{group}".')
            except Exception as e:
                logger.error(e)
                logger.error(f'Fail to load agent config "{name}" ({cls.__name__}) from {path}.')
        else:
            logger.debug(f'Agent config "{name}" ({cls.__name__}) not exists.')
        return config

    @classmethod
    def load_all(cls, group: str) -> dict[str, Self]:
        configs = {}
        agent_config_path = cls.get_agent_config_path()
        group_dir: Path = agent_config_path / group
        for p in group_dir.iterdir():
            if p.is_file() and p.suffix.lower() == ".json":
                name = p.stem
                try:
                    cfg = cls.load(name, group)
                    configs[name] = cfg
                except Exception as e:
                    logger.debug(f"{group}/{p} is not a config of {Self.__class__.__name__}. {e}")
        return configs


class NovelWritingConfig(AgentConfig, ABC):
    """base agent for all novel writing related agents"""

    author: Author | str
    novel_genres: str  # 小说体裁
    words_plan: int  # 计划字数. (中文,字数；英文:词数) planned word count, actually token count;
    novel_lang: str = "zh-Hans"  # 小说语言. default zh-Hans(简体中文 ISO 639)

    def __init__(self, **kwargs: Any):
        author = kwargs.get("author")
        if author and isinstance(author, str):
            author = AUTHOR_DICT.get(author)
            if author:
                kwargs["author"] = author
        super().__init__(**kwargs)

    @property
    def sys_prompt(self) -> str:
        """system prompt for writing the novel by acting as the author"""
        return f"""{self.author.as_prompt}
在写作中，对于未确定的角色你喜欢使用<角色代码(code)>代替<角色名字(name)>，便于后期整体替换为确认的正式名字。角色代码格式是使用'$['和']$'包围，例如: '$[张三]$', '$[ZhangSan]$', '$[extra1]$' 等
出版社约你写一本{self.novel_genres}类型的网络小说，因为极丰厚的报酬以及会带来非常大的影响力，你非常看重这本小说，因此进行了认真的构思。
"""


# ==========   Base Agents   ==========


class Agent(ABC):
    """base for agents"""

    config: AgentConfig

    _llm_cli: LlmClient
    _stop_event: asyncio.Event

    class Config:
        arbitrary_types_allowed = True

    def __init__(self, config: AgentConfig) -> None:
        self.config = config
        self._stop_event = asyncio.Event()
        llm_setting = pyllm.get_pre_defined_settings().get(self.config.llm_model).duplicate()
        llm_setting.max_input_token = 1_000_000
        llm_setting.max_output_token = 8192
        llm_setting.temperature = 1.3  # 暂时所有的agent。理论上不同的agent，不同故事，用不同temperature
        self._llm_cli = pyllm.create_client(llm_setting, fallback_to_default=True)
        self._llm_cli.ob.register_observer(PythonLogObserver(level="warning", format_json=True))

    @abstractmethod
    async def async_run(self, **kwargs: Any) -> Any:
        pass

    def stop(self) -> None:
        """stop the running agent"""
        self._stop_event.set()

    def shall_stop(self) -> bool:
        """check whether the running agent should stop"""
        return self._stop_event.is_set()

    def save_state(self, llm_prompts: dict | pyllm.ChatPrompts | list | str, llm_outputs: str) -> None:

        ob = self._llm_cli.ob
        try:
            AgentStat.objects.create(
                agent=f"{self.__class__.__name__}/{self.config.name}",
                config=self.config.to_dict(),
                llm_cost=ob.bag.get("cost") or {},
                perf=ob.bag.get("perf") or {},
                errors=ob.bag.get("errors") or "",
                llm_prompts=llm_prompts,
                llm_outputs=llm_outputs,
                extra_data={},
            )
        except Exception as e:
            logger.error(f"error saving agent state {self.config.name}: {e}")

    def save_state_async_no_wait(self, llm_prompts: dict | pyllm.ChatPrompts | list | str, llm_outputs: str) -> None:
        asyncio.create_task(self.async_save_state(llm_prompts=llm_prompts, llm_outputs=llm_outputs))

    async def async_save_state(self, llm_prompts: dict | pyllm.ChatPrompts | list | str, llm_outputs: str) -> None:
        return await sync_to_async(self.save_state)(llm_prompts=llm_prompts, llm_outputs=llm_outputs)


# ==========  Complete Web Novel Writing   ==========


class NovelAgentConfig(NovelWritingConfig):
    """config for NovelAgent"""

    pass


class NovelAgent(Agent):
    """撰写一本小说的智能体。An agent to write a complete novel automatically"""

    async def async_run(self, **kwargs: Any) -> Any:
        raise NotImplementedError


# ==========   Story Conceiving (for a novel)   ==========


class StoryAgentConfig(NovelWritingConfig):
    """config for StoryAgent"""

    name: str = "story-agent"
    less_themes: bool = True  # 是否使用较少的主题数, default True.


class StoryAgent(Agent):
    """为写小说构思一个故事的智能体。An agent to conceive a story for writing a novel"""

    config: StoryAgentConfig

    _theme_prompt: str = ""

    async def async_run(self, **kwargs: Any) -> Story:
        user_prompt = self.get_user_prompt()
        prompts = (
            pyllm.ChatPrompts(output_format="json", output_schema=Story.as_schema(), output_lang=self.config.novel_lang)
            .set_system(self.config.sys_prompt)
            .set_inquery(user_prompt)
        )
        llm_param = pyllm.create_param(
            self._llm_cli.setting, fallback_to_default=True, streaming=True, timeout=LLM_TIMEOUT
        )
        answer = await self._llm_cli.async_completion(prompts, llm_param)
        d = json.loads(answer)
        story = Story(
            **d,
            key=Story.generate_key(d["name"]),
            lang=self.config.novel_lang,
            author=self.config.author.name,
        )
        await story.asave()
        return story

    def get_user_prompt(self) -> str:
        major_theme_count, minor_theme_count = self.get_theme_counts()

        return f"""这本小说你计划写约{self.config.words_plan}字，并有如下构思：

# 主题
{self.get_theme_prompt()}
你从中选择了{major_theme_count}个主要主题{'' if major_theme_count>1 else '交织'}贯通主线{'，'+str(minor_theme_count)+'个次要主题穿插其间' if minor_theme_count>0 else ''}。
经过认真的思考、设计，你已经确定了主题、背景、情节以及几个主要人物（人物名称使用代码,格式 $[张三]$）。输出如下：
"""

    def get_theme_counts(self) -> tuple[int, int]:
        """Get major and minor counts of themes depending on the word count.

        Returns:
            tuple[int, int]: A tuple containing the counts of major themes and minor themes
        """
        less = self.config.less_themes
        if self.config.words_plan < WebNovelLength.NOVELLA.max_words:
            return (1, 0) if less else (1, 1)
        elif self.config.words_plan < WebNovelLength.ENTRY_LEVEL_NOVEL.max_words:
            return (1, 2) if less else (2, 4)
        elif self.config.words_plan < WebNovelLength.STANDARD_NOVEL.max_words:
            return (2, 3) if less else (3, 5)
        else:
            return (3, 5) if less else (4, 7)

    def get_theme_prompt(self) -> str:
        if not self._theme_prompt:
            s = "现在流行的故事主题主要有如下这些（当然你也可以选择其他主题）：\n"
            for k, v in WEB_NOVEL_THEME_DICT.items():
                s += f"- {k}：{v['desc']}。例如:{', '.join(v['examples'])}。流行度:{v['popularity']}/10\n"
            self._theme_prompt = s
        return self._theme_prompt


# ==========   Outline Design (for a novel)   ==========


class StoryOutlineAgentConfig(NovelWritingConfig):
    """config for StoryOutlineAgent"""

    name: str = "outline-agent"
    story: Story


class StoryOutlineAgent(Agent):
    """设计小说故事大纲的智能体。An agent to design the outline of a story for writing a novel"""

    config: StoryOutlineAgentConfig

    async def async_run(self, **kwargs: Any) -> StoryOutline:
        story = self.config.story
        if not story:
            raise ValueError("Story must be conceived before designing outline")

        # Create prompt for outline
        outline_prompt = f"""
#### 1. 故事概述(story_overview)
- 定义基调和风格（例如 [tone]，融入 [style]）。
- 明确目标读者（例如 [target_audience]）。
- 设置预计字数（约 [word_count] 字，分为 [chapter_count] 章，每章约 [words_per_chapter] 字）。
- 选择叙述视角（例如 [perspective]）。
- 概述主要冲突：
  - 外部：主角面对 [external_conflict]。
  - 内部：主角挣扎于 [internal_conflict]。
- 提供5-8句话的故事概述，涵盖开端、中段和结局。

#### 2. 世界观设定(world_schema)
- 描述设定（例如 [world_setting]）。
- 详述类型特定体系（例如 [supernatural/social/technological_system]，包括 [specific_mechanics]）。
- 列出关键派系或组织（例如 [faction_1]、[faction_2] 等）。
- 概述世界的历史和文化（例如 [historical_events]、[cultural_traits]）。
- 定义核心元素或物品（例如 [core_item]）和世界规则（例如 [world_rules]）。

#### 3. 人物设计
- 塑造主角（姓名、背景、性格、类型特定特征、动机、成长弧线）。
- 描述关键配角（例如 [supporting_character_type]，如盟友、反派、导师等）。
- 加入其他推动剧情的角色。
- 提供人物关系图（例如 [relationship_description]）。

#### 4. 情节框架
- 根据小说长度({story.length_of_story.cn}, {story.length_of_story.min_words}到{story.length_of_story.max_words}字) 确定有多少卷/幕(volumes, epics, acts)
- 各幕计划多少章节，多少字数。
- 定义各幕的：目标(objective),关键事件(events),激发事件或者转折点(???)，情感基调

#### 5. 章节规划
- 提供前10章的详细规划（每章约 [words_per_chapter] 字），包括：
  - 场景/setting。
  - 关键事件。
  - 涉及人物。
  - 后续剧情的伏笔。
- 概述后续章节的发展，每 [small_arc_chapters] 章为一个小型故事弧，每 [major_arc_chapters] 章为一个重大转折，确保节奏均衡。

#### 6. 主题与象征
- 明确核心主题。
- 突出象征元素。
- 概述情感主线。

#### 7. 其他要素
- 列出关键伏笔和线索。
- 建议节奏控制（例如，每 [minor_climax_chapters] 章一个小高潮，每 [major_climax_chapters] 章一个大高潮）。
- 强调类型特定特征：
  - **类型特定描述**：（例如 [description_type]）。
  - **类型特定情节**：（例如 [plot_type]）。
  - **类型特定情感**：（例如 [emotion_type]）。
- 推荐写作风格：[style_description]。

#### 8. 写作建议
- 提供准备建议（例如 [preparation_content]）。
- 建议节奏策略（例如 [pacing_strategy]）。
- 推荐灵活性（例如 [flexibility_advice]）。
- 考虑读者期待（例如 [reader_expectations]）。
"""
        format_prompt = """- 以详细的JSON文档呈现提纲。
- 确保提纲全面、灵活且分段清晰。
- 使用符合 [genre] 氛围的命名和术语。
- 平衡动作、情感和世界观，体现 [genre] 的精神。"""
        user_prompt = (
            f"# 故事设定\n{story.as_prompt()}\n\n"
            f"# 提纲生成\n为上面故事设定，生成一份详细小说提纲。需结构清晰、内容全面，面向 [target_audience]，包含以下要素：\n{outline_prompt}\n\n"
            f"# 格式\n{format_prompt}\n\n"
            "输出如下：\n"
        )

        # Generate character profiles using LLM
        prompts = (
            pyllm.ChatPrompts(
                output_format="json", output_schema=StoryOutline.as_schema(), output_lang=self.config.novel_lang
            )
            .set_system(self.config.sys_prompt)
            .set_inquery(user_prompt)
        )
        llm_param = pyllm.create_param(
            self._llm_cli.setting, steaming=False, fallback_to_default=True, timeout=LLM_TIMEOUT * 2
        )
        answer = await self._llm_cli.async_completion(prompts, llm_param)
        return answer


# ==========   Worlds Schema/Settings Design (for a novel)   ==========


class StoryWorldAgentConfig(NovelWritingConfig):
    """config for StoryWorldAgent"""

    name: str = "world-agent"
    story: Story


class StoryWorldAgent(Agent):
    """设计小说世界观的智能体。An agent to design the world building of a story for writing a novel"""

    config: StoryWorldAgentConfig

    async def async_run(self, **kwargs: Any) -> list[StoryWorld]:
        story = self.config.story
        if not story:
            raise ValueError("Story must be conceived before designing outline")

        # Create prompt for outline
        #         world_prompt = f"""- 描述设定（例如 [world_setting]）。
        # - 详述类型特定体系（例如 [supernatural/social/technological_system]，包括 [specific_mechanics]）。
        # - 列出关键派系或组织（例如 [faction_1]、[faction_2] 等）。
        # - 概述世界的历史和文化（例如 [historical_events]、[cultural_traits]）。
        # - 定义核心元素或物品（例如 [core_item]）和世界规则（例如 [world_rules]）。
        # - 根据不同的故事体裁，可以有多个不同的世界（比如 仙侠、穿越、科幻、...），各个世界有不同纬度或者级别，不同生态和世界观。
        # """
        world_prompt = f"""- **分层展开**：描述设定（[world_setting]，如凡人界与仙界、未来星际社会），初期展示基础设定，逐步揭示深层秘密（如隐藏势力、宇宙规则）。
- **类型特定体系**：详述 [supernatural/social/technological_system]（如修真境界、星际科技），包括 [specific_mechanics]（如灵根、量子核心）。
- **关键派系**：列出主要势力或组织（[faction_1]、[faction_2]等，如宗门、星际联盟），突出其文化和动机。
- **历史与文化**：概述世界历史（[historical_events]，如万年仙魔大战）与文化（[cultural_traits]，如尊师重道、科技崇拜）。
- **核心元素与规则**：定义核心物品（[core_item]，如上古仙器、量子装置）与世界规则（[world_rules]，如天道、熵增法则），并设计动态变化（如势力更迭、世界重塑）。
- **多样性与探索感**：融入多样的文化、种族或星域，增强世界深度和冒险吸引力。

"""
        format_prompt = f"""- 使用符合 {story.genres} 氛围的命名和术语。
- 平衡动作、情感和世界观，体现 {story.genres} 的精神。"""
        user_prompt = (
            "\n\n"
            f"# 故事设定\n{story.as_prompt()}\n\n"
            # f"# 框架设定\n{story.outline.as_prompt()}\n\n"
            f"# 世界观设定\n根据以上“故事设定”，符合故事类型、主题、背景以及长度({story.length_of_story.desc})，生成详细世界观(1到多个世界，根据故事设计)，各世界包含以下要素：\n{world_prompt}\n\n"
            f"# 格式\n{format_prompt}\n\n"
            "输出如下：\n"
        )

        prompts = (
            pyllm.ChatPrompts(
                output_format="json", output_schema=StoryWorld.as_array_schema(), output_lang=self.config.novel_lang
            )
            .set_system(self.config.sys_prompt)
            .set_inquery(user_prompt)
        )
        llm_param = pyllm.create_param(
            self._llm_cli.setting, steaming=False, fallback_to_default=True, timeout=LLM_TIMEOUT * 2
        )
        answer = await self._llm_cli.async_completion(prompts, llm_param)
        self.save_state_async_no_wait(llm_prompts=prompts, llm_outputs=answer)

        # Create and save StoryWorld objects
        worlds_data = json.loads(answer)
        worlds = []
        for d in worlds_data["array"]:
            d["story"] = story
            world = StoryWorld(**d)
            await world.asave()
            worlds.append(world)

        return worlds


# ==========   Characters Design (for a novel)   ==========


class StoryCharacterAgentConfig(NovelWritingConfig):
    """config for StoryCharacterAgent"""

    name: str = "character-agent"
    story: Story
    count: int = 3  # 创建的角色数量


class StoryCharacterAgent(Agent):
    """设计小说角色的智能体。An agent to design the characters of a story for writing a novel"""

    config: StoryCharacterAgentConfig

    async def async_run(self, **kwargs: Any) -> list[StoryCharacter]:
        """Create detailed character profiles for the story's main characters.

        This method generates detailed descriptions and backgrounds for each character
        mentioned in the story's initial conception, including their:
        - Personality traits
        - Physical appearance
        - Background/origin story
        - Motivations and goals
        - Relationships with other characters

        Returns:
            list[StoryCharacter]: A list of StoryCharacter objects containing the
            detailed character profiles
        """
        story = self.config.story
        if not story:
            raise ValueError("Story must be conceived before creating characters")

        # Create prompt for existed characters
        char_prompt = "\n".join(
            [f"## {c.name}\n{c.as_prompt(includes=['code', 'bio'])}" async for c in story.storycharacter_set.all()]
        )
        user_prompt = (
            f'为小说"{story.name}"创建{self.config.count}个详细的人物设定。\n\n'
            f"# 故事设定(人物未创建)\n{story.as_prompt()}\n\n"
            f"# 已创建的人物(不要再创建)\n{char_prompt or '无'}\n\n"
            "经过认真的思考以及参考流行的网络小说，你根据小说构思创建数个还未创建的角色，且已为每个角色创建详细的背景设定，"
            "包括：性格特征、外貌描写、背景故事、动机目标、与其他角色的关系等等(描述中不要包含角色名字)。输出如下：\n"
        )

        # Generate character profiles using LLM
        prompts = (
            pyllm.ChatPrompts(
                output_format="json", output_schema=StoryCharacter.as_array_schema(), output_lang=self.config.novel_lang
            )
            .set_system(self.config.sys_prompt)
            .set_inquery(user_prompt)
        )
        llm_param = pyllm.create_param(
            self._llm_cli.setting, streaming=False, fallback_to_default=True, timeout=LLM_TIMEOUT
        )
        answer = await self._llm_cli.async_completion(prompts, llm_param)
        characters_data = json.loads(answer)

        # Create and save StoryCharacter objects
        characters = []
        for char_d in characters_data["array"]:
            char_key = StoryCharacter.generate_key(char_d["code"], story.key)
            char_d.pop("story", None)
            character = StoryCharacter(story=self.config.story, key=char_key, **char_d)
            await character.asave()
            characters.append(character)

        return characters


# ==========   Chapter Writing (for a novel)   ==========


class StoryChapterAgentConfig(NovelWritingConfig):
    """config for StoryChapterAgent"""

    name: str = "chapter-agent"


class StoryChapterAgent(Agent):
    """撰写小说章节的智能体。An agent to write chapters for writing a novel"""

    config: StoryChapterAgentConfig

    async def async_run(self, **kwargs: Any) -> list[StoryCharacter]:
        pass
