"""agent to write chapters for a novel"""

from typing import Any, AsyncGenerator

import pyllm
from novelai import conf
from novelai.engine.agents.base import NovelWritingConfig, NovelWritingAgent
from novelai.engine.models.chapter import StoryChapter, StoryChapterType
from novelai.engine.models.character import <PERSON><PERSON>haracter
from novelai.engine.models.story import Story
from pycommon.logwrapper import get_log_wrapper

logger = get_log_wrapper(__name__)


class StoryChapterAgentConfig(NovelWritingConfig):
    """config for StoryChapterAgent"""

    name: str = "chapter-agent"
    story: Story
    count: int = 1  # 创建的章节数量，<=0 表示根据设定自动选择
    llm_timeout: int = 180
    token_limit: int = 8192


class StoryChapterAgent(NovelWritingAgent):
    """撰写小说章节的智能体。An agent to write chapters for writing a novel"""

    config: StoryChapterAgentConfig

    async def _async_run(self, **kwargs: Any) -> list[<PERSON><PERSON><PERSON><PERSON>]:
        story = self.config.story
        if not story:
            raise ValueError("Story must be conceived before designing outline")

        # worlds prompts
        worlds = await story.async_get_worlds()
        if len(worlds) > 0:
            worlds_prompts = "\n".join([f"## {w.name}\n{w.as_prompt()}" for w in worlds])
        else:
            worlds_prompts = "无"

        # Create prompt for existed characters
        characters = await story.async_get_characters()
        if len(characters) > 0:
            char_prompt = "\n".join(
                [f"## {c.code}\n{c.as_prompt(includes=['name', 'level', 'bio'])}" for code, c in characters.items()]
            )
        else:
            char_prompt = "无"

        # 前情
        chapter = None
        chapters_prompts = ""
        last_5_chapters = StoryChapter.objects.filter(story=story).order_by("-volume", "-order")[:5]
        async for c in last_5_chapters:
            if chapter is None:
                chapter = c
            chapters_prompts += f"- 卷{c.volume}/章{c.order}「{c.title}」 - {c.summary}\n"
        chapters_prompts = "无" if not chapters_prompts else chapters_prompts

        detail_prompt = f"""
        """
        user_prompt = (
            "\n\n"
            f"# 故事设定\n{story.as_prompt()}\n\n"
            f"# 世界观设定\n{worlds_prompts}\n\n"
            f"# 角色设定\n{char_prompt}\n\n"
            f"# 前情章节摘要\n{chapters_prompts}\n\n"
            f"# 撰写从第{chapter.volume if chapter else 1}卷第{chapter.order+1 if chapter else 1}章开始({StoryChapterType.CHAPTER.text_by_lang(conf.LANG)}, 约{story.words_per_chapter_plan}字/章)\n\n根据以上设定、框架、世界观 和 前情“，撰写接下来{self.config.count if self.config.count > 0 else '数'}个章节，请多使用对话推进情节。如果有需要，你可以增加新的角色或世界。\n{detail_prompt}\n\n"
            f"# 注意题目和内容**包括标点符号**都使用{self.config.novel_lang}"
            "输出如下：\n"
        )

        prompts = (
            pyllm.ChatPrompts(
                output_format="json", output_schema=StoryChapter.as_array_schema(), output_lang=self.config.novel_lang
            )
            .set_system(self.config.sys_prompt)
            .set_inquery(user_prompt)
        )
        chapters_data = await self.async_invoke_llm(prompt=prompts.to_list(), resp_type="object")

        # Create and save StoryChapter objects
        chapters = []
        for d in chapters_data["array"]:
            d["story"] = story
            chapter = StoryChapter(
                tech_detail={
                    "llm_model": self.config.llm_model,
                    "llm_temperature": self.config.llm_temperature,
                    "llm_output_tokens": self.config.llm_output_tokens,
                    "llm_timeout": self.config.llm_timeout,
                },
                **d,
            )
            await chapter.asave()
            chapters.append(chapter)
        return chapters

    async def _async_iter(self, **kwargs: Any) -> AsyncGenerator:
        raise NotImplementedError
