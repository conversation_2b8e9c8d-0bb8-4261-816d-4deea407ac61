import json
import re
from abc import abstractmethod, ABC
from json import <PERSON><PERSON><PERSON>ecoder
from logging import getLogger
from typing import Any, Type, Optional

import litellm  # type: ignore
from asgiref.sync import sync_to_async
from pydantic import BaseModel, ConfigDict

from pycommon.observation import (
    Notification,
    ObserverManager,
    Observable,
)
from .observation import LlmCostTracker
from .params import ModelSetting, ChatParam
from .prompts import ChatPrompts

logger = getLogger(__name__)


class LlmClient(BaseModel, ABC):
    """Abstracted base class of wrapper for all LLM clients.

    Attributes:
        setting (params.ModelSetting): Setting to set up a LLM client.
    """

    setting: ModelSetting
    ob: Observable

    model_config = ConfigDict(arbitrary_types_allowed=True)

    def __int__(self, **kwargs: Any) -> None:
        super().__int__(**kwargs)
        self.ob = ObserverManager(LlmCostTracker())

    @abstractmethod
    def _completion(self, prompts: Chat<PERSON>rom<PERSON>, params: Chat<PERSON>aram, **kwargs: Any) -> str:
        """abstract implementation of completion for children."""

    def completion(self, prompts: ChatPrompts, params: Chat<PERSON>aram, **kwargs: Any) -> str:
        """completely finish a turn of conversation with LLM.

        Returns (str):
             A string of the answer text from LLM
        """
        try:
            s = self._completion(prompts, params, **kwargs)
        except Exception as e:
            logger.error(f"LLM Client Error: {e}")
            s = ""
        if s.endswith("LLM Error:"):
            logger.error(s)
            s = ""
        return s

    async def async_completion(self, prompts: ChatPrompts, params: ChatParam, **kwargs: Any) -> str:
        return await sync_to_async(self._completion)(prompts, params, **kwargs)

    @staticmethod
    def try_parse_json(
        s: str | bytes | bytearray, cls: Optional[Type[JSONDecoder]] = None, *args: Any, **kwargs: Any
    ) -> Any:
        try:
            data = json.loads(s, cls=cls, *args, **kwargs)
        except json.decoder.JSONDecodeError as e:
            m = re.match(r"[^\{\[]*(?P<jsonstr>[\{\[].*[\}\]]).*", s, flags=re.I | re.S)
            if m:
                s = m["jsonstr"]
                data = json.loads(s, cls=cls, *args, **kwargs)
            else:
                raise e
        return data


class LlmGeneralClient(LlmClient):
    """An client implemented with `litellm`"""

    @property
    def name_observable(self) -> str:
        return "litellm_cli"

    def _completion(self, prompts: ChatPrompts, params: ChatParam, **kwargs: Any) -> str | litellm.CustomStreamWrapper:
        # assert prompts.inquery, "No user prompt."

        message_list = prompts.to_list()
        self.ob.observe(Notification(message="prompts", data=prompts.to_str()).to("state", "log"))
        self.ob.observe(Notification(message="setting", data=self.setting).to("state"))
        self.ob.observe(Notification(message="params", data=params).to("state"))
        resp = litellm.completion(
            model=f"{self.setting.provider.value}/{self.setting.mod_name.value}",
            max_tokens=self.setting.max_output_token,
            messages=message_list,
            response_format=({"type": "json_object"} if prompts.output_format == "json" else None),
            **params.to_dict(),
            **kwargs,
        )
        return self._handle_output(resp)

    def _handle_output(
        self, resp: litellm.ModelResponse | litellm.CustomStreamWrapper
    ) -> str | litellm.CustomStreamWrapper:
        if isinstance(resp, litellm.CustomStreamWrapper):
            return resp

        self.ob.observe(message="answer", data=resp.json(), subjects=["cost"])
        s = resp.choices[0].message.content
        s = re.sub(r"^```\w*\n", "", s)
        s = re.sub(r"```$", "", s)
        self.ob.observe(message="answer", data=s, subjects=["log"])
        return s

    def _litellm_log_callback(
        self,
        kwargs,  # kwargs to completion
        completion_response,  # response from completion
        start_time,
        end_time,  # start/end time
    ):
        pass


class OpenAIClient(LlmGeneralClient):
    pass


class OllamaClient(LlmGeneralClient):
    pass


class GeminiClient(LlmGeneralClient):
    pass


class DeepSeekClient(LlmGeneralClient):
    pass
