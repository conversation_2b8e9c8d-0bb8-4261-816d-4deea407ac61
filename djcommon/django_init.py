from pycommon.utils.lang import get_caller_loc
from pycommon.utils.terminal import tip


def init_django(root: str = "") -> None:
    """A script that's needed to setup django if it's not already running on a server.
    Without this, you won't be able to import django modules
    Run this at very beginning before importing django related modules.

    :param root: The django project root. Default is current folder.
    :return:
    """

    import os
    import sys

    import django

    # Find the project base directory
    loc = get_caller_loc()
    fpath, line = loc.split(":")
    django_project_root = os.path.dirname(os.path.abspath(fpath))

    # Add the project base directory to the sys.path for package lookup
    sys.path.insert(0, django_project_root)

    # The DJANGO_SETTINGS_MODULE has to be set to allow us to access django imports
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "app.settings")

    #  Allow queryset filtering asynchronously when running in a Jupyter notebook
    os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"

    # This is for setting up django
    django.setup()

    tip(f"Django Initialized for {django_project_root}.")
