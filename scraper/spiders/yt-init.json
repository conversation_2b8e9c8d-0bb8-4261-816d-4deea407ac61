{
	"responseContext": {
		"serviceTrackingParams": [
			{
				"service": "GUIDED_HELP",
				"params": [
					{
						"key": "context",
						"value": "yt_web_search"
					},
					{
						"key": "logged_in",
						"value": "0"
					}
				]
			},
			{
				"service": "GFEEDBACK",
				"params": [
					{
						"key": "has_unlimited_entitlement",
						"value": "False"
					},
					{
						"key": "has_premium_lite_entitlement",
						"value": "False"
					},
					{
						"key": "logged_in",
						"value": "0"
					},
					{
						"key": "e",
						"value": "9405995,23804281,23986030,24004644,24077241,24166867,24181174,24241378,24299873,24439361,24453989,24466622,24499534,24548629,24566687,24619146,24699899,39325854,39326986,51009781,51010235,51017346,51020570,51025415,51030101,51037342,51037349,51043774,51050361,51053689,51057842,51057857,51063643,51072748,51091058,51095478,51098299,51105628,51111738,51115184,51124104,51141472,51144925,51145218,51151423,51152050,51153490,51157411,51160545,51169118,51176421,51176511,51178314,51178329,51178344,51178351,51178982,51183910,51184990,51194137,51199253,51204329,51208678,51217504,51222382,51222973,51226860,51227037,51227776,51228850,51230478,51236019,51237842,51239093,51241028,51242448,51248255,51248734,51255676,51255743,51256074,51256084,51263449,51272458,51274583,51275785,51276343,51276557,51276565,51281227,51282792,51285052,51285717,51287196,51289920,51289931,51289938,51289956,51289963,51289972,51292055,51294322,51296439,51298020,51299710,51299724,51299981,51300001,51300014,51300176,51300241,51302680,51303432,51303667,51303669,51303789,51304155,51305839,51306259,51307301,51308710,51310742,51311029,51311040,51311505,51313109,51313765,51313767,51313802,51314158,51315044,51315912,51315921,51315928,51315931,51315938,51315949,51315954,51315963,51315968,51315979,51318845,51320245,51322669,51326208,51326932,51327146,51327169,51327186,51327613,51327615,51327636,51330194,51331485,51331502,51331520,51331535,51331538,51331545,51331552,51331561,51332896,51333541,51333878,51334535,51335365,51335392,51335594,51335928,51336633,51337186,51337349,51338524,51340662,51341214,51341226,51341730,51341974,51342615,51342857,51343368,51343796,51345295,51345629,51346352,51346801,51346806,51346827,51346842,51346857,51346866,51346891,51346902,51347325,51347575,51348208,51348482,51348672,51348754,51349439,51349880,51351446,51352297,51352762,51353231,51353361,51353393,51354114,51354507,51354569,51355266,51355271,51355291,51355305,51355312,51355339,51355344,51355417,51355679,51355802,51355912,51357477,51358764,51359177,51360104,51360121,51360130,51360208,51360215,51361727,51361830,51362071,51362455,51362643,51362674,51362857,51362879,51363729,51363732,51363741,51363750,51363761,51363768,51365460,51365716,51365751,51365987,51366214,51366246,51366423,51366620,51366864,51367318,51367487,51367600,51367664,51367993,51368933,51369559,51370738,51370986,51370997,51371010,51371044,51371294,51371522,51372663,51372698,51374439,51375074,51375168,51375719,51375867,51376651"
					},
					{
						"key": "visitor_data",
						"value": "CgstVFd2SFMyLTNoQSi5js-7BjIKCgJVUxIEGgAgTg%3D%3D"
					}
				]
			},
			{
				"service": "CSI",
				"params": [
					{
						"key": "yt_ad",
						"value": "1"
					},
					{
						"key": "c",
						"value": "WEB"
					},
					{
						"key": "cver",
						"value": "2.20241219.01.01"
					},
					{
						"key": "yt_li",
						"value": "0"
					},
					{
						"key": "GetSearch_rid",
						"value": "0x855cbb65d7e84f28"
					}
				]
			},
			{
				"service": "ECATCHER",
				"params": [
					{
						"key": "client.version",
						"value": "2.20241219"
					},
					{
						"key": "client.name",
						"value": "WEB"
					}
				]
			}
		],
		"mainAppWebResponseContext": {
			"loggedOut": true,
			"trackingParam": "kx_fmPxhoPZR3066ob97gHPBc0NIAceM4arKwtVPwOp2sUzRgkuMwSLBwOcCE59TDtslLKPQ-SS"
		},
		"webResponseContextExtensionData": {
			"ytConfigData": {
				"visitorData": "CgstVFd2SFMyLTNoQSi5js-7BjIKCgJVUxIEGgAgTg%3D%3D",
				"rootVisualElementType": 4724
			},
			"hasDecorated": true
		}
	},
	"estimatedResults": "73917518",
	"contents": {
		"twoColumnSearchResultsRenderer": {
			"primaryContents": {
				"sectionListRenderer": {
					"contents": [
						{
							"itemSectionRenderer": {
								"contents": [
									{
										"videoRenderer": {
											"videoId": "M3hw9jSpkNI",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/M3hw9jSpkNI/hq720.jpg?sqp=-oaymwEnCOgCEMoBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLDV7htBSVL3t78WrmBJNf7ysVGzRg",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/M3hw9jSpkNI/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLDpXc2pNVZmJS7YTUOpes0OYPrm0A",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "Elon Musk \\u2018melts down\\u2019 after underestimating wrath of MAGA trolls"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "Elon Musk \\u2018melts down\\u2019 after underestimating wrath of MAGA trolls by MSNBC 929,597 views 8 hours ago 9 minutes, 32 seconds"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "MSNBC",
														"navigationEndpoint": {
															"clickTrackingParams": "CLwBENwwGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@msnbc",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCaXkIU1QidjPwiAYu6GcHjg",
																"canonicalBaseUrl": "/@msnbc"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "8 hours ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "9 minutes, 32 seconds"
													}
												},
												"simpleText": "9:32"
											},
											"viewCountText": {
												"simpleText": "929,597 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CLwBENwwGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=M3hw9jSpkNI&pp=ygUJRWxvbiBNdXNr",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "M3hw9jSpkNI",
													"params": "qgMJRWxvbiBNdXNrugMKCLCTkLOa9smWQboDCgikhLXtsuTp31i6AwsIk8Tlm_edyMeyAboDCgjymfrw6Zbjjyi6AwsIuurjobnXudu6AboDCgi0x-WCv6z-mGa6AwoIlPGtw_O748ABugMKCMDC5uqOjNChY7oDCwiBqOuX9N_B298BugMKCIvJndycyPySF7oDCwiElsq_oZL875sBugMKCKzO1rSx773HWboDCgi04a2R5vyWkHa6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "ygUJRWxvbiBNdXNr",
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr1---sn-5uaeznez.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=337870f634a990d2&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"badges": [
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "New",
														"trackingParams": "CLwBENwwGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "CC",
														"trackingParams": "CLwBENwwGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"accessibilityData": {
															"label": "Closed captions"
														}
													}
												}
											],
											"ownerBadges": [
												{
													"metadataBadgeRenderer": {
														"icon": {
															"iconType": "CHECK_CIRCLE_THICK"
														},
														"style": "BADGE_STYLE_TYPE_VERIFIED",
														"tooltip": "Verified",
														"trackingParams": "CLwBENwwGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"accessibilityData": {
															"label": "Verified"
														}
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "MSNBC",
														"navigationEndpoint": {
															"clickTrackingParams": "CLwBENwwGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@msnbc",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCaXkIU1QidjPwiAYu6GcHjg",
																"canonicalBaseUrl": "/@msnbc"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "MSNBC",
														"navigationEndpoint": {
															"clickTrackingParams": "CLwBENwwGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@msnbc",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCaXkIU1QidjPwiAYu6GcHjg",
																"canonicalBaseUrl": "/@msnbc"
															}
														}
													}
												]
											},
											"trackingParams": "CLwBENwwGAAiEwjog9GV5tGKAxUTPq0GHfeMBx9A0qGmpeOenLwz",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "929K views"
													}
												},
												"simpleText": "929K views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CMEBEP6YBBgQIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CMEBEP6YBBgQIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "M3hw9jSpkNI",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CMEBEP6YBBgQIhMI6IPRlebRigMVEz6tBh33jAcf",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"M3hw9jSpkNI"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"M3hw9jSpkNI"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CMEBEP6YBBgQIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CMABENGqBRgRIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"offlineVideoEndpoint": {
																		"videoId": "M3hw9jSpkNI",
																		"onAddCommand": {
																			"clickTrackingParams": "CMABENGqBRgRIhMI6IPRlebRigMVEz6tBh33jAcf",
																			"getDownloadActionCommand": {
																				"videoId": "M3hw9jSpkNI",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CMABENGqBRgRIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CLwBENwwGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgtNM2h3OWpTcGtOSQ%3D%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CLwBENwwGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CL8BEI5iIhMI6IPRlebRigMVEz6tBh33jAcf",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CLwBENwwGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CLwBENwwGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/Kn00Sgjs6hlFbGtbYmE2bqMrGr_Gbz062eFK_fnVfziUN4HZ2C6I988wsfARNEcFwBB_5rARdw=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CLwBENwwGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@msnbc",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCaXkIU1QidjPwiAYu6GcHjg",
															"canonicalBaseUrl": "/@msnbc"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "9 minutes, 32 seconds"
																}
															},
															"simpleText": "9:32"
														},
														"style": "DEFAULT"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CL4BEPnnAxgEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "M3hw9jSpkNI",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CL4BEPnnAxgEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "M3hw9jSpkNI"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CL4BEPnnAxgEIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CL0BEMfsBBgFIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CL0BEMfsBBgFIhMI6IPRlebRigMVEz6tBh33jAcf",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "M3hw9jSpkNI",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CL0BEMfsBBgFIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"M3hw9jSpkNI"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"M3hw9jSpkNI"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CL0BEMfsBBgFIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/M3hw9jSpkNI/mqdefault_6s.webp?du=3000&sqp=CODqzrsG&rs=AOn4CLD0Gi2-Hk5Obv9on4OjQaWr2MrfTA",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "Those two factions of the Republican Party with their profoundly different views are at each other\'s throats about what to do with\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": false
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CLwBENwwGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=M3hw9jSpkNI&pp=YAHIAQE%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "M3hw9jSpkNI",
													"params": "qgMJRWxvbiBNdXNrugMKCLCTkLOa9smWQboDCgikhLXtsuTp31i6AwsIk8Tlm_edyMeyAboDCgjymfrw6Zbjjyi6AwsIuurjobnXudu6AboDCgi0x-WCv6z-mGa6AwoIlPGtw_O748ABugMKCMDC5uqOjNChY7oDCwiBqOuX9N_B298BugMKCIvJndycyPySF7oDCwiElsq_oZL875sBugMKCKzO1rSx773HWboDCgi04a2R5vyWkHa6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "YAHIAQE%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr1---sn-5uaeznez.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=337870f634a990d2&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgtNM2h3OWpTcGtOSSDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/Kn00Sgjs6hlFbGtbYmE2bqMrGr_Gbz062eFK_fnVfziUN4HZ2C6I988wsfARNEcFwBB_5rARdw=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CLwBENwwGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@msnbc",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UCaXkIU1QidjPwiAYu6GcHjg",
																		"canonicalBaseUrl": "/@msnbc"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "QS0nsaZkCbA",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/QS0nsaZkCbA/hq720.jpg?sqp=-oaymwEnCOgCEMoBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLB-Y16R3BMem67GjflANTIfuWxT2A",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/QS0nsaZkCbA/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLBnUFE-naJGW8h-NBVMU0cWnDbbgQ",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "Elon Musk LOSES IT, suspends MAGA influencers"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "Elon Musk LOSES IT, suspends MAGA influencers by David Pakman Show 116,271 views 9 hours ago 5 minutes, 7 seconds"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "David Pakman Show",
														"navigationEndpoint": {
															"clickTrackingParams": "CLYBENwwGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@thedavidpakmanshow",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCvixJtaXuNdMPUGdOPcY8Ag",
																"canonicalBaseUrl": "/@thedavidpakmanshow"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "9 hours ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "5 minutes, 7 seconds"
													}
												},
												"simpleText": "5:07"
											},
											"viewCountText": {
												"simpleText": "116,271 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CLYBENwwGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=QS0nsaZkCbA&pp=ygUJRWxvbiBNdXNr",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "QS0nsaZkCbA",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgikhLXtsuTp31i6AwsIk8Tlm_edyMeyAboDCgjymfrw6Zbjjyi6AwsIuurjobnXudu6AboDCgi0x-WCv6z-mGa6AwoIlPGtw_O748ABugMKCMDC5uqOjNChY7oDCwiBqOuX9N_B298BugMKCIvJndycyPySF7oDCwiElsq_oZL875sBugMKCKzO1rSx773HWboDCgi04a2R5vyWkHa6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "ygUJRWxvbiBNdXNr",
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr5---sn-5uaezne6.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=412d27b1a66409b0&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"badges": [
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "New",
														"trackingParams": "CLYBENwwGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "CC",
														"trackingParams": "CLYBENwwGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"accessibilityData": {
															"label": "Closed captions"
														}
													}
												}
											],
											"ownerBadges": [
												{
													"metadataBadgeRenderer": {
														"icon": {
															"iconType": "CHECK_CIRCLE_THICK"
														},
														"style": "BADGE_STYLE_TYPE_VERIFIED",
														"tooltip": "Verified",
														"trackingParams": "CLYBENwwGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"accessibilityData": {
															"label": "Verified"
														}
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "David Pakman Show",
														"navigationEndpoint": {
															"clickTrackingParams": "CLYBENwwGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@thedavidpakmanshow",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCvixJtaXuNdMPUGdOPcY8Ag",
																"canonicalBaseUrl": "/@thedavidpakmanshow"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "David Pakman Show",
														"navigationEndpoint": {
															"clickTrackingParams": "CLYBENwwGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@thedavidpakmanshow",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCvixJtaXuNdMPUGdOPcY8Ag",
																"canonicalBaseUrl": "/@thedavidpakmanshow"
															}
														}
													}
												]
											},
											"trackingParams": "CLYBENwwGAEiEwjog9GV5tGKAxUTPq0GHfeMBx9AsJOQs5r2yZZB",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "116K views"
													}
												},
												"simpleText": "116K views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CLsBEP6YBBgSIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CLsBEP6YBBgSIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "QS0nsaZkCbA",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CLsBEP6YBBgSIhMI6IPRlebRigMVEz6tBh33jAcf",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"QS0nsaZkCbA"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"QS0nsaZkCbA"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CLsBEP6YBBgSIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CLoBENGqBRgTIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"offlineVideoEndpoint": {
																		"videoId": "QS0nsaZkCbA",
																		"onAddCommand": {
																			"clickTrackingParams": "CLoBENGqBRgTIhMI6IPRlebRigMVEz6tBh33jAcf",
																			"getDownloadActionCommand": {
																				"videoId": "QS0nsaZkCbA",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CLoBENGqBRgTIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CLYBENwwGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgtRUzBuc2Faa0NiQQ%3D%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CLYBENwwGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CLkBEI5iIhMI6IPRlebRigMVEz6tBh33jAcf",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CLYBENwwGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CLYBENwwGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/nE7ERss6yZkFHxoWueiKPFXVtPzc3QINaTdA2Eh5oDGVXFtpZzE85UBKjv4Bkf8YH21rOzzZ18E=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CLYBENwwGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@thedavidpakmanshow",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCvixJtaXuNdMPUGdOPcY8Ag",
															"canonicalBaseUrl": "/@thedavidpakmanshow"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "5 minutes, 7 seconds"
																}
															},
															"simpleText": "5:07"
														},
														"style": "DEFAULT"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CLgBEPnnAxgEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "QS0nsaZkCbA",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CLgBEPnnAxgEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "QS0nsaZkCbA"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CLgBEPnnAxgEIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CLcBEMfsBBgFIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CLcBEMfsBBgFIhMI6IPRlebRigMVEz6tBh33jAcf",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "QS0nsaZkCbA",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CLcBEMfsBBgFIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"QS0nsaZkCbA"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"QS0nsaZkCbA"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CLcBEMfsBBgFIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/QS0nsaZkCbA/mqdefault_6s.webp?du=3000&sqp=CKDyzrsG&rs=AOn4CLCzfFnTm6uZeUSoyAGNJTjr1OYrtA",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "Incogni lets you control your personal data! Get 60% off their annual plan: http://incogni.com/pakman -- "
															},
															{
																"text": "Elon Musk",
																"bold": true
															},
															{
																"text": " loses it over the\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": false
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CLYBENwwGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=QS0nsaZkCbA&pp=YAHIAQE%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "QS0nsaZkCbA",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgikhLXtsuTp31i6AwsIk8Tlm_edyMeyAboDCgjymfrw6Zbjjyi6AwsIuurjobnXudu6AboDCgi0x-WCv6z-mGa6AwoIlPGtw_O748ABugMKCMDC5uqOjNChY7oDCwiBqOuX9N_B298BugMKCIvJndycyPySF7oDCwiElsq_oZL875sBugMKCKzO1rSx773HWboDCgi04a2R5vyWkHa6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "YAHIAQE%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr5---sn-5uaezne6.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=412d27b1a66409b0&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgtRUzBuc2Faa0NiQSDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/nE7ERss6yZkFHxoWueiKPFXVtPzc3QINaTdA2Eh5oDGVXFtpZzE85UBKjv4Bkf8YH21rOzzZ18E=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CLYBENwwGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@thedavidpakmanshow",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UCvixJtaXuNdMPUGdOPcY8Ag",
																		"canonicalBaseUrl": "/@thedavidpakmanshow"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "WL-nIy2tQiQ",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/WL-nIy2tQiQ/hq720.jpg?sqp=-oaymwEnCOgCEMoBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLAkVdj8i4BdbaXauup8oEzAUKQ-Lw",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/WL-nIy2tQiQ/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLCM2UUytLGEygnw7282MBadTwsnvw",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "OpenAI Whistleblower Suchir Balaji Death: Elon Musk Wants FBI Probe | Vantage with Palki Sharma"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "OpenAI Whistleblower Suchir Balaji Death: Elon Musk Wants FBI Probe | Vantage with Palki Sharma by Firstpost 647,130 views 17 hours ago 6 minutes, 12 seconds"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "Firstpost",
														"navigationEndpoint": {
															"clickTrackingParams": "CLABENwwGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@Firstpost",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCz8QaiQxApLq8sLNcszYyJw",
																"canonicalBaseUrl": "/@Firstpost"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "17 hours ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "6 minutes, 12 seconds"
													}
												},
												"simpleText": "6:12"
											},
											"viewCountText": {
												"simpleText": "647,130 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CLABENwwGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=WL-nIy2tQiQ&pp=ygUJRWxvbiBNdXNr",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "WL-nIy2tQiQ",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwsIk8Tlm_edyMeyAboDCgjymfrw6Zbjjyi6AwsIuurjobnXudu6AboDCgi0x-WCv6z-mGa6AwoIlPGtw_O748ABugMKCMDC5uqOjNChY7oDCwiBqOuX9N_B298BugMKCIvJndycyPySF7oDCwiElsq_oZL875sBugMKCKzO1rSx773HWboDCgi04a2R5vyWkHa6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "ygUJRWxvbiBNdXNr",
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr5---sn-5ualdnze.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=58bfa7232dad4224&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"badges": [
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "New",
														"trackingParams": "CLABENwwGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												}
											],
											"ownerBadges": [
												{
													"metadataBadgeRenderer": {
														"icon": {
															"iconType": "CHECK_CIRCLE_THICK"
														},
														"style": "BADGE_STYLE_TYPE_VERIFIED",
														"tooltip": "Verified",
														"trackingParams": "CLABENwwGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"accessibilityData": {
															"label": "Verified"
														}
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "Firstpost",
														"navigationEndpoint": {
															"clickTrackingParams": "CLABENwwGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@Firstpost",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCz8QaiQxApLq8sLNcszYyJw",
																"canonicalBaseUrl": "/@Firstpost"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "Firstpost",
														"navigationEndpoint": {
															"clickTrackingParams": "CLABENwwGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@Firstpost",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCz8QaiQxApLq8sLNcszYyJw",
																"canonicalBaseUrl": "/@Firstpost"
															}
														}
													}
												]
											},
											"trackingParams": "CLABENwwGAIiEwjog9GV5tGKAxUTPq0GHfeMBx9ApIS17bLk6d9Y",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "647K views"
													}
												},
												"simpleText": "647K views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CLUBEP6YBBgRIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CLUBEP6YBBgRIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "WL-nIy2tQiQ",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CLUBEP6YBBgRIhMI6IPRlebRigMVEz6tBh33jAcf",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"WL-nIy2tQiQ"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"WL-nIy2tQiQ"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CLUBEP6YBBgRIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CLQBENGqBRgSIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"offlineVideoEndpoint": {
																		"videoId": "WL-nIy2tQiQ",
																		"onAddCommand": {
																			"clickTrackingParams": "CLQBENGqBRgSIhMI6IPRlebRigMVEz6tBh33jAcf",
																			"getDownloadActionCommand": {
																				"videoId": "WL-nIy2tQiQ",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CLQBENGqBRgSIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CLABENwwGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgtXTC1uSXkydFFpUQ%3D%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CLABENwwGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CLMBEI5iIhMI6IPRlebRigMVEz6tBh33jAcf",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CLABENwwGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CLABENwwGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/ECkjlnETOWq_v2UuToBHeR_UY42J47UNashpSkdQpcMEMh0eBdBag-81bRs65BOhZzsbGxUW=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CLABENwwGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@Firstpost",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCz8QaiQxApLq8sLNcszYyJw",
															"canonicalBaseUrl": "/@Firstpost"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "6 minutes, 12 seconds"
																}
															},
															"simpleText": "6:12"
														},
														"style": "DEFAULT"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CLIBEPnnAxgDIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "WL-nIy2tQiQ",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CLIBEPnnAxgDIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "WL-nIy2tQiQ"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CLIBEPnnAxgDIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CLEBEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CLEBEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "WL-nIy2tQiQ",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CLEBEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"WL-nIy2tQiQ"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"WL-nIy2tQiQ"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CLEBEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/WL-nIy2tQiQ/mqdefault_6s.webp?du=3000&sqp=CISMz7sG&rs=AOn4CLAU4pSLDV3m0ehOrCgQfC87aSxx5Q",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "OpenAI Whistleblower Suchir Balaji Death: "
															},
															{
																"text": "Elon Musk",
																"bold": true
															},
															{
																"text": " Wants FBI Probe | Vantage with Palki Sharma Just months ago, Suchir\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": false
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CLABENwwGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=WL-nIy2tQiQ&pp=YAHIAQE%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "WL-nIy2tQiQ",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwsIk8Tlm_edyMeyAboDCgjymfrw6Zbjjyi6AwsIuurjobnXudu6AboDCgi0x-WCv6z-mGa6AwoIlPGtw_O748ABugMKCMDC5uqOjNChY7oDCwiBqOuX9N_B298BugMKCIvJndycyPySF7oDCwiElsq_oZL875sBugMKCKzO1rSx773HWboDCgi04a2R5vyWkHa6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "YAHIAQE%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr5---sn-5ualdnze.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=58bfa7232dad4224&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgtXTC1uSXkydFFpUSDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/ECkjlnETOWq_v2UuToBHeR_UY42J47UNashpSkdQpcMEMh0eBdBag-81bRs65BOhZzsbGxUW=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CLABENwwGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@Firstpost",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UCz8QaiQxApLq8sLNcszYyJw",
																		"canonicalBaseUrl": "/@Firstpost"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "so8g73N5YhM",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/so8g73N5YhM/hq720.jpg?sqp=-oaymwEnCOgCEMoBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLALld6K2jAP30M3AokAyeWk93hdRQ",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/so8g73N5YhM/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLAfq5Aj1UuD683VWmwURCUAJr3gSg",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "Trump sides with Elon Musk on H-1B visas following criticism: \'A great program\'"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "Trump sides with Elon Musk on H-1B visas following criticism: \'A great program\' by Fox News 236,518 views 1 day ago 7 minutes, 7 seconds"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "Fox News",
														"navigationEndpoint": {
															"clickTrackingParams": "CKoBENwwGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@FoxNews",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCXIJgqnII2ZOINSWNOGFThA",
																"canonicalBaseUrl": "/@FoxNews"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "1 day ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "7 minutes, 7 seconds"
													}
												},
												"simpleText": "7:07"
											},
											"viewCountText": {
												"simpleText": "236,518 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CKoBENwwGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=so8g73N5YhM&pp=ygUJRWxvbiBNdXNr",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "so8g73N5YhM",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMKCPKZ-vDpluOPKLoDCwi66uOhude527oBugMKCLTH5YK_rP6YZroDCgiU8a3D87vjwAG6AwoIwMLm6o6M0KFjugMLCIGo65f038Hb3wG6AwoIi8md3JzI_JIXugMLCISWyr-hkvzvmwG6AwoIrM7WtLHvvcdZugMKCLThrZHm_JaQdroDCgjx6pnSxeW873O6AwsIpZTD-O3i4cnLAboDCgiKq4-d-uuhqEu6AwoI0dPpv9zLz_t2ugMKCKTBueCirqzbHroDCgizlZ-RzrnfhU7yAwUNJcr0Pg%3D%3D",
													"playerParams": "ygUJRWxvbiBNdXNr",
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr5---sn-5uaeznss.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=b28f20ef73796213&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"badges": [
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "New",
														"trackingParams": "CKoBENwwGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "CC",
														"trackingParams": "CKoBENwwGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"accessibilityData": {
															"label": "Closed captions"
														}
													}
												}
											],
											"ownerBadges": [
												{
													"metadataBadgeRenderer": {
														"icon": {
															"iconType": "CHECK_CIRCLE_THICK"
														},
														"style": "BADGE_STYLE_TYPE_VERIFIED",
														"tooltip": "Verified",
														"trackingParams": "CKoBENwwGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"accessibilityData": {
															"label": "Verified"
														}
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "Fox News",
														"navigationEndpoint": {
															"clickTrackingParams": "CKoBENwwGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@FoxNews",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCXIJgqnII2ZOINSWNOGFThA",
																"canonicalBaseUrl": "/@FoxNews"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "Fox News",
														"navigationEndpoint": {
															"clickTrackingParams": "CKoBENwwGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@FoxNews",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCXIJgqnII2ZOINSWNOGFThA",
																"canonicalBaseUrl": "/@FoxNews"
															}
														}
													}
												]
											},
											"trackingParams": "CKoBENwwGAMiEwjog9GV5tGKAxUTPq0GHfeMBx9Ak8Tlm_edyMeyAQ==",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "236K views"
													}
												},
												"simpleText": "236K views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CK8BEP6YBBgQIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CK8BEP6YBBgQIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "so8g73N5YhM",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CK8BEP6YBBgQIhMI6IPRlebRigMVEz6tBh33jAcf",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"so8g73N5YhM"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"so8g73N5YhM"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CK8BEP6YBBgQIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CK4BENGqBRgRIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"offlineVideoEndpoint": {
																		"videoId": "so8g73N5YhM",
																		"onAddCommand": {
																			"clickTrackingParams": "CK4BENGqBRgRIhMI6IPRlebRigMVEz6tBh33jAcf",
																			"getDownloadActionCommand": {
																				"videoId": "so8g73N5YhM",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CK4BENGqBRgRIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CKoBENwwGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgtzbzhnNzNONVloTQ%3D%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CKoBENwwGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CK0BEI5iIhMI6IPRlebRigMVEz6tBh33jAcf",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CKoBENwwGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CKoBENwwGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/xrMXuR2ARUKOvvMKeB2XAFMt6rchyUkiEn2G2J1DtWjL5zVxKW9H4jlkQdBdBoqTi_WmU5_dGQ=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CKoBENwwGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@FoxNews",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCXIJgqnII2ZOINSWNOGFThA",
															"canonicalBaseUrl": "/@FoxNews"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "7 minutes, 7 seconds"
																}
															},
															"simpleText": "7:07"
														},
														"style": "DEFAULT"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CKwBEPnnAxgEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "so8g73N5YhM",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CKwBEPnnAxgEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "so8g73N5YhM"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CKwBEPnnAxgEIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CKsBEMfsBBgFIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CKsBEMfsBBgFIhMI6IPRlebRigMVEz6tBh33jAcf",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "so8g73N5YhM",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CKsBEMfsBBgFIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"so8g73N5YhM"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"so8g73N5YhM"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CKsBEMfsBBgFIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/so8g73N5YhM/mqdefault_6s.webp?du=3000&sqp=COmFz7sG&rs=AOn4CLB3A3C-gDT4P8goyGiQEsIDV9t-Nw",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "\'Fox & Friends\' co-hosts weigh in on the debate over H-1B visas, which allow U.S. companies to hire foreign workers for specialty\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": false
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CKoBENwwGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=so8g73N5YhM&pp=YAHIAQE%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "so8g73N5YhM",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMKCPKZ-vDpluOPKLoDCwi66uOhude527oBugMKCLTH5YK_rP6YZroDCgiU8a3D87vjwAG6AwoIwMLm6o6M0KFjugMLCIGo65f038Hb3wG6AwoIi8md3JzI_JIXugMLCISWyr-hkvzvmwG6AwoIrM7WtLHvvcdZugMKCLThrZHm_JaQdroDCgjx6pnSxeW873O6AwsIpZTD-O3i4cnLAboDCgiKq4-d-uuhqEu6AwoI0dPpv9zLz_t2ugMKCKTBueCirqzbHroDCgizlZ-RzrnfhU7yAwUNJcr0Pg%3D%3D",
													"playerParams": "YAHIAQE%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr5---sn-5uaeznss.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=b28f20ef73796213&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgtzbzhnNzNONVloTSDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/xrMXuR2ARUKOvvMKeB2XAFMt6rchyUkiEn2G2J1DtWjL5zVxKW9H4jlkQdBdBoqTi_WmU5_dGQ=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CKoBENwwGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@FoxNews",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UCXIJgqnII2ZOINSWNOGFThA",
																		"canonicalBaseUrl": "/@FoxNews"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "KB-Mtp4ejPI",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/KB-Mtp4ejPI/hq720.jpg?sqp=-oaymwEnCOgCEMoBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLBgfT9sLh60s4O3QFgasyUSpoycdg",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/KB-Mtp4ejPI/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLBj_aGgJB90KKwKf0njKb0NCtnTxg",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "MAGA TURNS on Elon Musk, DISASTER"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "MAGA TURNS on Elon Musk, DISASTER by David Pakman Show 204,511 views 13 hours ago 10 minutes, 17 seconds"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "David Pakman Show",
														"navigationEndpoint": {
															"clickTrackingParams": "CKQBENwwGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@thedavidpakmanshow",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCvixJtaXuNdMPUGdOPcY8Ag",
																"canonicalBaseUrl": "/@thedavidpakmanshow"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "13 hours ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "10 minutes, 17 seconds"
													}
												},
												"simpleText": "10:17"
											},
											"viewCountText": {
												"simpleText": "204,511 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CKQBENwwGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=KB-Mtp4ejPI&pp=ygUJRWxvbiBNdXNr",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "KB-Mtp4ejPI",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwsIuurjobnXudu6AboDCgi0x-WCv6z-mGa6AwoIlPGtw_O748ABugMKCMDC5uqOjNChY7oDCwiBqOuX9N_B298BugMKCIvJndycyPySF7oDCwiElsq_oZL875sBugMKCKzO1rSx773HWboDCgi04a2R5vyWkHa6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "ygUJRWxvbiBNdXNr",
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr5---sn-5uaeznes.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=281f8cb69e1e8cf2&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"badges": [
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "New",
														"trackingParams": "CKQBENwwGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "CC",
														"trackingParams": "CKQBENwwGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"accessibilityData": {
															"label": "Closed captions"
														}
													}
												}
											],
											"ownerBadges": [
												{
													"metadataBadgeRenderer": {
														"icon": {
															"iconType": "CHECK_CIRCLE_THICK"
														},
														"style": "BADGE_STYLE_TYPE_VERIFIED",
														"tooltip": "Verified",
														"trackingParams": "CKQBENwwGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"accessibilityData": {
															"label": "Verified"
														}
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "David Pakman Show",
														"navigationEndpoint": {
															"clickTrackingParams": "CKQBENwwGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@thedavidpakmanshow",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCvixJtaXuNdMPUGdOPcY8Ag",
																"canonicalBaseUrl": "/@thedavidpakmanshow"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "David Pakman Show",
														"navigationEndpoint": {
															"clickTrackingParams": "CKQBENwwGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@thedavidpakmanshow",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCvixJtaXuNdMPUGdOPcY8Ag",
																"canonicalBaseUrl": "/@thedavidpakmanshow"
															}
														}
													}
												]
											},
											"trackingParams": "CKQBENwwGAQiEwjog9GV5tGKAxUTPq0GHfeMBx9A8pn68OmW448o",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "204K views"
													}
												},
												"simpleText": "204K views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CKkBEP6YBBgSIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CKkBEP6YBBgSIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "KB-Mtp4ejPI",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CKkBEP6YBBgSIhMI6IPRlebRigMVEz6tBh33jAcf",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"KB-Mtp4ejPI"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"KB-Mtp4ejPI"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CKkBEP6YBBgSIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CKgBENGqBRgTIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"offlineVideoEndpoint": {
																		"videoId": "KB-Mtp4ejPI",
																		"onAddCommand": {
																			"clickTrackingParams": "CKgBENGqBRgTIhMI6IPRlebRigMVEz6tBh33jAcf",
																			"getDownloadActionCommand": {
																				"videoId": "KB-Mtp4ejPI",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CKgBENGqBRgTIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CKQBENwwGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgtLQi1NdHA0ZWpQSQ%3D%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CKQBENwwGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CKcBEI5iIhMI6IPRlebRigMVEz6tBh33jAcf",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CKQBENwwGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CKQBENwwGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/nE7ERss6yZkFHxoWueiKPFXVtPzc3QINaTdA2Eh5oDGVXFtpZzE85UBKjv4Bkf8YH21rOzzZ18E=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CKQBENwwGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@thedavidpakmanshow",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCvixJtaXuNdMPUGdOPcY8Ag",
															"canonicalBaseUrl": "/@thedavidpakmanshow"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "10 minutes, 17 seconds"
																}
															},
															"simpleText": "10:17"
														},
														"style": "DEFAULT"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CKYBEPnnAxgEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "KB-Mtp4ejPI",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CKYBEPnnAxgEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "KB-Mtp4ejPI"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CKYBEPnnAxgEIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CKUBEMfsBBgFIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CKUBEMfsBBgFIhMI6IPRlebRigMVEz6tBh33jAcf",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "KB-Mtp4ejPI",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CKUBEMfsBBgFIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"KB-Mtp4ejPI"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"KB-Mtp4ejPI"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CKUBEMfsBBgFIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/KB-Mtp4ejPI/mqdefault_6s.webp?du=3000&sqp=CIrzzrsG&rs=AOn4CLA4hiLAvt6v9ehvxOQRn3T7YHGtKw",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "Lumen lets you master your metabolism. GET 20% OFF at https://lumen.me/pakman -- MAGA turns on "
															},
															{
																"text": "Elon Musk",
																"bold": true
															},
															{
																"text": " on the topic of\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": false
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CKQBENwwGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=KB-Mtp4ejPI&pp=YAHIAQE%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "KB-Mtp4ejPI",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwsIuurjobnXudu6AboDCgi0x-WCv6z-mGa6AwoIlPGtw_O748ABugMKCMDC5uqOjNChY7oDCwiBqOuX9N_B298BugMKCIvJndycyPySF7oDCwiElsq_oZL875sBugMKCKzO1rSx773HWboDCgi04a2R5vyWkHa6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "YAHIAQE%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr5---sn-5uaeznes.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=281f8cb69e1e8cf2&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgtLQi1NdHA0ZWpQSSDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/nE7ERss6yZkFHxoWueiKPFXVtPzc3QINaTdA2Eh5oDGVXFtpZzE85UBKjv4Bkf8YH21rOzzZ18E=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CKQBENwwGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@thedavidpakmanshow",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UCvixJtaXuNdMPUGdOPcY8Ag",
																		"canonicalBaseUrl": "/@thedavidpakmanshow"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "urbmu5Q49To",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/urbmu5Q49To/hq720.jpg?sqp=-oaymwEnCOgCEMoBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLDdBumt3zdNvuw0czBrT-Sd0-Qfaw",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/urbmu5Q49To/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLAzWD6UeeAEZH61Iu_JF_ehnp63yA",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "Donald Trump speaks out amid visa row between Elon Musk and MAGA base"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "Donald Trump speaks out amid visa row between Elon Musk and MAGA base by Sky News Australia 7,742 views 8 hours ago 3 minutes, 11 seconds"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "Sky News Australia",
														"navigationEndpoint": {
															"clickTrackingParams": "CJ4BENwwGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@SkyNewsAustralia",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCO0akufu9MOzyz3nvGIXAAw",
																"canonicalBaseUrl": "/@SkyNewsAustralia"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "8 hours ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "3 minutes, 11 seconds"
													}
												},
												"simpleText": "3:11"
											},
											"viewCountText": {
												"simpleText": "7,742 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CJ4BENwwGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=urbmu5Q49To&pp=ygUJRWxvbiBNdXNr",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "urbmu5Q49To",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMKCLTH5YK_rP6YZroDCgiU8a3D87vjwAG6AwoIwMLm6o6M0KFjugMLCIGo65f038Hb3wG6AwoIi8md3JzI_JIXugMLCISWyr-hkvzvmwG6AwoIrM7WtLHvvcdZugMKCLThrZHm_JaQdroDCgjx6pnSxeW873O6AwsIpZTD-O3i4cnLAboDCgiKq4-d-uuhqEu6AwoI0dPpv9zLz_t2ugMKCKTBueCirqzbHroDCgizlZ-RzrnfhU7yAwUNJcr0Pg%3D%3D",
													"playerParams": "ygUJRWxvbiBNdXNr",
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr1---sn-5uaeznze.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=bab6e6bb9438f53a&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"badges": [
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "New",
														"trackingParams": "CJ4BENwwGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												}
											],
											"ownerBadges": [
												{
													"metadataBadgeRenderer": {
														"icon": {
															"iconType": "CHECK_CIRCLE_THICK"
														},
														"style": "BADGE_STYLE_TYPE_VERIFIED",
														"tooltip": "Verified",
														"trackingParams": "CJ4BENwwGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"accessibilityData": {
															"label": "Verified"
														}
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "Sky News Australia",
														"navigationEndpoint": {
															"clickTrackingParams": "CJ4BENwwGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@SkyNewsAustralia",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCO0akufu9MOzyz3nvGIXAAw",
																"canonicalBaseUrl": "/@SkyNewsAustralia"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "Sky News Australia",
														"navigationEndpoint": {
															"clickTrackingParams": "CJ4BENwwGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@SkyNewsAustralia",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCO0akufu9MOzyz3nvGIXAAw",
																"canonicalBaseUrl": "/@SkyNewsAustralia"
															}
														}
													}
												]
											},
											"trackingParams": "CJ4BENwwGAUiEwjog9GV5tGKAxUTPq0GHfeMBx9AuurjobnXudu6AQ==",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "7.7K views"
													}
												},
												"simpleText": "7.7K views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CKMBEP6YBBgPIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CKMBEP6YBBgPIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "urbmu5Q49To",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CKMBEP6YBBgPIhMI6IPRlebRigMVEz6tBh33jAcf",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"urbmu5Q49To"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"urbmu5Q49To"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CKMBEP6YBBgPIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CKIBENGqBRgQIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"offlineVideoEndpoint": {
																		"videoId": "urbmu5Q49To",
																		"onAddCommand": {
																			"clickTrackingParams": "CKIBENGqBRgQIhMI6IPRlebRigMVEz6tBh33jAcf",
																			"getDownloadActionCommand": {
																				"videoId": "urbmu5Q49To",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CKIBENGqBRgQIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CJ4BENwwGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "Cgt1cmJtdTVRNDlUbw%3D%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CJ4BENwwGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CKEBEI5iIhMI6IPRlebRigMVEz6tBh33jAcf",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CJ4BENwwGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CJ4BENwwGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/Q-ry-JNiIiQtu7V8C3PdBFyOI-i4_bR_v8jpnkvKSAlgrLaZa40Zu1TQyfGVCGm9-49JxlOjng=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CJ4BENwwGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@SkyNewsAustralia",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCO0akufu9MOzyz3nvGIXAAw",
															"canonicalBaseUrl": "/@SkyNewsAustralia"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "3 minutes, 11 seconds"
																}
															},
															"simpleText": "3:11"
														},
														"style": "DEFAULT"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CKABEPnnAxgDIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "urbmu5Q49To",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CKABEPnnAxgDIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "urbmu5Q49To"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CKABEPnnAxgDIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CJ8BEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CJ8BEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "urbmu5Q49To",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CJ8BEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"urbmu5Q49To"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"urbmu5Q49To"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CJ8BEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/urbmu5Q49To/mqdefault_6s.webp?du=3000&sqp=CMLvzrsG&rs=AOn4CLDAsPTNzYd9gnQCJSsg60ZNMCVxOw",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "President-elect Donald Trump has spoken out amid a row over the H-1B visas for foreign tech industry workers which has\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": false
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CJ4BENwwGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=urbmu5Q49To&pp=YAHIAQE%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "urbmu5Q49To",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMKCLTH5YK_rP6YZroDCgiU8a3D87vjwAG6AwoIwMLm6o6M0KFjugMLCIGo65f038Hb3wG6AwoIi8md3JzI_JIXugMLCISWyr-hkvzvmwG6AwoIrM7WtLHvvcdZugMKCLThrZHm_JaQdroDCgjx6pnSxeW873O6AwsIpZTD-O3i4cnLAboDCgiKq4-d-uuhqEu6AwoI0dPpv9zLz_t2ugMKCKTBueCirqzbHroDCgizlZ-RzrnfhU7yAwUNJcr0Pg%3D%3D",
													"playerParams": "YAHIAQE%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr1---sn-5uaeznze.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=bab6e6bb9438f53a&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "Egt1cmJtdTVRNDlUbyDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/Q-ry-JNiIiQtu7V8C3PdBFyOI-i4_bR_v8jpnkvKSAlgrLaZa40Zu1TQyfGVCGm9-49JxlOjng=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CJ4BENwwGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@SkyNewsAustralia",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UCO0akufu9MOzyz3nvGIXAAw",
																		"canonicalBaseUrl": "/@SkyNewsAustralia"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "ZjH5Y_BZY7Q",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/ZjH5Y_BZY7Q/hq720.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGDkgYChlMA8=&rs=AOn4CLD2mIY7zkOJg8Qyeo6kXPexYAxLXw",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/ZjH5Y_BZY7Q/hq720.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGDkgYChlMA8=&rs=AOn4CLBAkuOVOCgevqlreSOE4t2q0d5-Ew",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "Exposing Elon Musk\'s Dark Politics Scam"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "Exposing Elon Musk\'s Dark Politics Scam by Progressive Property 5,534 views 11 hours ago 44 seconds - play Short"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "Progressive Property",
														"navigationEndpoint": {
															"clickTrackingParams": "CJcBEJ2kBxgGIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@ProgressiveProperty",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UC0g1KuusONVStjY_XjdXy6g",
																"canonicalBaseUrl": "/@ProgressiveProperty"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "11 hours ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "44 seconds"
													}
												},
												"simpleText": "0:44"
											},
											"viewCountText": {
												"simpleText": "5,534 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CJcBEJ2kBxgGIhMI6IPRlebRigMVEz6tBh33jAcfUglFbG9uIE11c2uaAQUIMhD0JA==",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/shorts/ZjH5Y_BZY7Q",
														"webPageType": "WEB_PAGE_TYPE_SHORTS",
														"rootVe": 37414
													}
												},
												"reelWatchEndpoint": {
													"videoId": "ZjH5Y_BZY7Q",
													"playerParams": "8AEByAMyuAQUogYVAePzwRNsuuA7bSuKDTbUaAYQ22ijkAcC",
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/vi/ZjH5Y_BZY7Q/frame0.jpg",
																"width": 1080,
																"height": 1920
															}
														],
														"isOriginalAspectRatio": true
													},
													"overlay": {
														"reelPlayerOverlayRenderer": {
															"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
															"trackingParams": "CJ0BELC1BCITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
															"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
														}
													},
													"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
													"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
													"sequenceParams": "Cgtaakg1WV9CWlk3USocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
													"loggingContext": {
														"vssLoggingContext": {
															"serializedContextData": "CgIIDA%3D%3D"
														},
														"qoeLoggingContext": {
															"serializedContextData": "CgIIDA%3D%3D"
														}
													},
													"ustreamerConfig": "CAw="
												}
											},
											"badges": [
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "New",
														"trackingParams": "CJcBEJ2kBxgGIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "Progressive Property",
														"navigationEndpoint": {
															"clickTrackingParams": "CJcBEJ2kBxgGIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@ProgressiveProperty",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UC0g1KuusONVStjY_XjdXy6g",
																"canonicalBaseUrl": "/@ProgressiveProperty"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "Progressive Property",
														"navigationEndpoint": {
															"clickTrackingParams": "CJcBEJ2kBxgGIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@ProgressiveProperty",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UC0g1KuusONVStjY_XjdXy6g",
																"canonicalBaseUrl": "/@ProgressiveProperty"
															}
														}
													}
												]
											},
											"trackingParams": "CJcBEJ2kBxgGIhMI6IPRlebRigMVEz6tBh33jAcfQLTH5YK_rP6YZg==",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "5.5K views"
													}
												},
												"simpleText": "5.5K views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CJwBEP6YBBgLIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CJwBEP6YBBgLIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "ZjH5Y_BZY7Q",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CJwBEP6YBBgLIhMI6IPRlebRigMVEz6tBh33jAcf",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"ZjH5Y_BZY7Q"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"ZjH5Y_BZY7Q"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CJwBEP6YBBgLIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CJsBENGqBRgMIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"offlineVideoEndpoint": {
																		"videoId": "ZjH5Y_BZY7Q",
																		"onAddCommand": {
																			"clickTrackingParams": "CJsBENGqBRgMIhMI6IPRlebRigMVEz6tBh33jAcf",
																			"getDownloadActionCommand": {
																				"videoId": "ZjH5Y_BZY7Q",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CJsBENGqBRgMIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CJcBEJ2kBxgGIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "Cgtaakg1WV9CWlk3UVICCAI%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CJcBEJ2kBxgGIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CJoBEI5iIhMI6IPRlebRigMVEz6tBh33jAcf",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CJcBEJ2kBxgGIhMI6IPRlebRigMVEz6tBh33jAcf",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CJcBEJ2kBxgGIhMI6IPRlebRigMVEz6tBh33jAcf",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/KCcilCm6zF6bohfH9N6ozk9N1kfgOJlhMUoP3lAEn_4nos5geRZnrMs0KrM1gCfJ3uiuATQL=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CJcBEJ2kBxgGIhMI6IPRlebRigMVEz6tBh33jAcf",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@ProgressiveProperty",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UC0g1KuusONVStjY_XjdXy6g",
															"canonicalBaseUrl": "/@ProgressiveProperty"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "Shorts"
																}
															},
															"simpleText": "SHORTS"
														},
														"style": "SHORTS",
														"icon": {
															"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
														}
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CJkBEPnnAxgCIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "ZjH5Y_BZY7Q",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CJkBEPnnAxgCIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "ZjH5Y_BZY7Q"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CJkBEPnnAxgCIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CJgBEMfsBBgDIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CJgBEMfsBBgDIhMI6IPRlebRigMVEz6tBh33jAcf",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "ZjH5Y_BZY7Q",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CJgBEMfsBBgDIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"ZjH5Y_BZY7Q"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"ZjH5Y_BZY7Q"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CJgBEMfsBBgDIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/ZjH5Y_BZY7Q/mqdefault_6s.webp?du=3000&sqp=CKDszrsG&rs=AOn4CLDM6tPfdbAmRuoP6KlgYrrjNVkwkA",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CJcBEJ2kBxgGIhMI6IPRlebRigMVEz6tBh33jAcfMgZzZWFyY2hSCUVsb24gTXVza5oBBQgyEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=ZjH5Y_BZY7Q&pp=YAHIAQHwAQHoBQGiBhUB4_PBE2y64DttK4oNNtRoBhDbaKOQBwI%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "ZjH5Y_BZY7Q",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoIlPGtw_O748ABugMKCMDC5uqOjNChY7oDCwiBqOuX9N_B298BugMKCIvJndycyPySF7oDCwiElsq_oZL875sBugMKCKzO1rSx773HWboDCgi04a2R5vyWkHa6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE2y64DttK4oNNtRoBhDbaKOQBwI%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr3---sn-5uaeznls.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=6631f963f05963b4&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "Egtaakg1WV9CWlk3USDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/KCcilCm6zF6bohfH9N6ozk9N1kfgOJlhMUoP3lAEn_4nos5geRZnrMs0KrM1gCfJ3uiuATQL=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CJcBEJ2kBxgGIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@ProgressiveProperty",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UC0g1KuusONVStjY_XjdXy6g",
																		"canonicalBaseUrl": "/@ProgressiveProperty"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "AYGN3zhreJQ",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/AYGN3zhreJQ/hq720.jpg?sqp=-oaymwEnCOgCEMoBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLDbGrBFyjNRVQSvwYut_-eVI7abKA",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/AYGN3zhreJQ/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLByZ6foiatZqLkeGfzwH2fWMyB4CA",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "Berlin accuses Elon Musk of trying to influence German election"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "Berlin accuses Elon Musk of trying to influence German election by Guardian News 24,437 views 18 hours ago 1 minute, 45 seconds"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "Guardian News",
														"navigationEndpoint": {
															"clickTrackingParams": "CJEBENwwGAciEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@guardiannews",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCIRYBXDze5krPDzAEOxFGVA",
																"canonicalBaseUrl": "/@guardiannews"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "18 hours ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "1 minute, 45 seconds"
													}
												},
												"simpleText": "1:45"
											},
											"viewCountText": {
												"simpleText": "24,437 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CJEBENwwGAciEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=AYGN3zhreJQ&pp=ygUJRWxvbiBNdXNr",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "AYGN3zhreJQ",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCMDC5uqOjNChY7oDCwiBqOuX9N_B298BugMKCIvJndycyPySF7oDCwiElsq_oZL875sBugMKCKzO1rSx773HWboDCgi04a2R5vyWkHa6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "ygUJRWxvbiBNdXNr",
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr1---sn-5uaeznez.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=01818ddf386b7894&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"badges": [
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "New",
														"trackingParams": "CJEBENwwGAciEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												}
											],
											"ownerBadges": [
												{
													"metadataBadgeRenderer": {
														"icon": {
															"iconType": "CHECK_CIRCLE_THICK"
														},
														"style": "BADGE_STYLE_TYPE_VERIFIED",
														"tooltip": "Verified",
														"trackingParams": "CJEBENwwGAciEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"accessibilityData": {
															"label": "Verified"
														}
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "Guardian News",
														"navigationEndpoint": {
															"clickTrackingParams": "CJEBENwwGAciEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@guardiannews",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCIRYBXDze5krPDzAEOxFGVA",
																"canonicalBaseUrl": "/@guardiannews"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "Guardian News",
														"navigationEndpoint": {
															"clickTrackingParams": "CJEBENwwGAciEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@guardiannews",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCIRYBXDze5krPDzAEOxFGVA",
																"canonicalBaseUrl": "/@guardiannews"
															}
														}
													}
												]
											},
											"trackingParams": "CJEBENwwGAciEwjog9GV5tGKAxUTPq0GHfeMBx9AlPGtw_O748AB",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "24K views"
													}
												},
												"simpleText": "24K views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CJYBEP6YBBgRIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CJYBEP6YBBgRIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "AYGN3zhreJQ",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CJYBEP6YBBgRIhMI6IPRlebRigMVEz6tBh33jAcf",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"AYGN3zhreJQ"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"AYGN3zhreJQ"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CJYBEP6YBBgRIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CJUBENGqBRgSIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"offlineVideoEndpoint": {
																		"videoId": "AYGN3zhreJQ",
																		"onAddCommand": {
																			"clickTrackingParams": "CJUBENGqBRgSIhMI6IPRlebRigMVEz6tBh33jAcf",
																			"getDownloadActionCommand": {
																				"videoId": "AYGN3zhreJQ",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CJUBENGqBRgSIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CJEBENwwGAciEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgtBWUdOM3pocmVKUQ%3D%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CJEBENwwGAciEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CJQBEI5iIhMI6IPRlebRigMVEz6tBh33jAcf",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CJEBENwwGAciEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CJEBENwwGAciEwjog9GV5tGKAxUTPq0GHfeMBx8=",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/ytc/AIdro_nDo1i4jEe90wqB2OCDy57NjTMVUHo1o7vFBjLzVzLeBQI=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CJEBENwwGAciEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@guardiannews",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCIRYBXDze5krPDzAEOxFGVA",
															"canonicalBaseUrl": "/@guardiannews"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "1 minute, 45 seconds"
																}
															},
															"simpleText": "1:45"
														},
														"style": "DEFAULT"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CJMBEPnnAxgDIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "AYGN3zhreJQ",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CJMBEPnnAxgDIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "AYGN3zhreJQ"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CJMBEPnnAxgDIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CJIBEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CJIBEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "AYGN3zhreJQ",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CJIBEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"AYGN3zhreJQ"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"AYGN3zhreJQ"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CJIBEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/AYGN3zhreJQ/mqdefault_6s.webp?du=3000&sqp=CMTzzrsG&rs=AOn4CLCIKebUFFFLQ2X6QJSBGHLFgthAeQ",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "The German government has accused "
															},
															{
																"text": "Elon Musk",
																"bold": true
															},
															{
																"text": " of trying to meddle in the country\'s election campaign with repeated\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": false
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CJEBENwwGAciEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=AYGN3zhreJQ&pp=YAHIAQE%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "AYGN3zhreJQ",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCMDC5uqOjNChY7oDCwiBqOuX9N_B298BugMKCIvJndycyPySF7oDCwiElsq_oZL875sBugMKCKzO1rSx773HWboDCgi04a2R5vyWkHa6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "YAHIAQE%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr1---sn-5uaeznez.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=01818ddf386b7894&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgtBWUdOM3pocmVKUSDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/ytc/AIdro_nDo1i4jEe90wqB2OCDy57NjTMVUHo1o7vFBjLzVzLeBQI=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CJEBENwwGAciEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@guardiannews",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UCIRYBXDze5krPDzAEOxFGVA",
																		"canonicalBaseUrl": "/@guardiannews"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "Y0NAYO1ZoUA",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/Y0NAYO1ZoUA/hq720.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGGUgWChLMA8=&rs=AOn4CLDyU8lRyqQ8VkOHmFTTCivyds8xSQ",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/Y0NAYO1ZoUA/hq720.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGGUgWChLMA8=&rs=AOn4CLD1Fs8Fx0dBhYmKUaV-6b-b3B_vjg",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "This Chinese chemist is beating Elon Musk!!"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "This Chinese chemist is beating Elon Musk!! by Aevy TV 272,340 views 1 day ago 1 minute - play Short"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "Aevy TV",
														"navigationEndpoint": {
															"clickTrackingParams": "CIoBEJ2kBxgIIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@aevytv",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCA295QVkf9O1RQ8_-s3FVXg",
																"canonicalBaseUrl": "/@aevytv"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "1 day ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "1 minute"
													}
												},
												"simpleText": "1:00"
											},
											"viewCountText": {
												"simpleText": "272,340 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CIoBEJ2kBxgIIhMI6IPRlebRigMVEz6tBh33jAcfUglFbG9uIE11c2uaAQUIMhD0JA==",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/shorts/Y0NAYO1ZoUA",
														"webPageType": "WEB_PAGE_TYPE_SHORTS",
														"rootVe": 37414
													}
												},
												"reelWatchEndpoint": {
													"videoId": "Y0NAYO1ZoUA",
													"playerParams": "8AEByAMyuAQUogYVAePzwRO7o2uXlGu6oCV81Sm6-pOikAcC",
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/vi/Y0NAYO1ZoUA/frame0.jpg",
																"width": 1080,
																"height": 1920
															}
														],
														"isOriginalAspectRatio": true
													},
													"overlay": {
														"reelPlayerOverlayRenderer": {
															"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
															"trackingParams": "CJABELC1BCITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
															"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
														}
													},
													"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
													"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
													"sequenceParams": "CgtZME5BWU8xWm9VQSocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
													"loggingContext": {
														"vssLoggingContext": {
															"serializedContextData": "CgIIDA%3D%3D"
														},
														"qoeLoggingContext": {
															"serializedContextData": "CgIIDA%3D%3D"
														}
													},
													"ustreamerConfig": "CAw="
												}
											},
											"badges": [
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "New",
														"trackingParams": "CIoBEJ2kBxgIIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												}
											],
											"ownerBadges": [
												{
													"metadataBadgeRenderer": {
														"icon": {
															"iconType": "CHECK_CIRCLE_THICK"
														},
														"style": "BADGE_STYLE_TYPE_VERIFIED",
														"tooltip": "Verified",
														"trackingParams": "CIoBEJ2kBxgIIhMI6IPRlebRigMVEz6tBh33jAcf",
														"accessibilityData": {
															"label": "Verified"
														}
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "Aevy TV",
														"navigationEndpoint": {
															"clickTrackingParams": "CIoBEJ2kBxgIIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@aevytv",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCA295QVkf9O1RQ8_-s3FVXg",
																"canonicalBaseUrl": "/@aevytv"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "Aevy TV",
														"navigationEndpoint": {
															"clickTrackingParams": "CIoBEJ2kBxgIIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@aevytv",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCA295QVkf9O1RQ8_-s3FVXg",
																"canonicalBaseUrl": "/@aevytv"
															}
														}
													}
												]
											},
											"trackingParams": "CIoBEJ2kBxgIIhMI6IPRlebRigMVEz6tBh33jAcfQMDC5uqOjNChYw==",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "272K views"
													}
												},
												"simpleText": "272K views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CI8BEP6YBBgPIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CI8BEP6YBBgPIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "Y0NAYO1ZoUA",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CI8BEP6YBBgPIhMI6IPRlebRigMVEz6tBh33jAcf",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"Y0NAYO1ZoUA"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"Y0NAYO1ZoUA"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CI8BEP6YBBgPIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CI4BENGqBRgQIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"offlineVideoEndpoint": {
																		"videoId": "Y0NAYO1ZoUA",
																		"onAddCommand": {
																			"clickTrackingParams": "CI4BENGqBRgQIhMI6IPRlebRigMVEz6tBh33jAcf",
																			"getDownloadActionCommand": {
																				"videoId": "Y0NAYO1ZoUA",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CI4BENGqBRgQIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CIoBEJ2kBxgIIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgtZME5BWU8xWm9VQVICCAI%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CIoBEJ2kBxgIIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CI0BEI5iIhMI6IPRlebRigMVEz6tBh33jAcf",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CIoBEJ2kBxgIIhMI6IPRlebRigMVEz6tBh33jAcf",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CIoBEJ2kBxgIIhMI6IPRlebRigMVEz6tBh33jAcf",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/m_e5-z-5EPsn5-qRZkx9amJhVdZk_jJxwDY_GeQbO0HDoJZF7TCtdZZEWWFFYT3nFFE7paNT=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CIoBEJ2kBxgIIhMI6IPRlebRigMVEz6tBh33jAcf",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@aevytv",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCA295QVkf9O1RQ8_-s3FVXg",
															"canonicalBaseUrl": "/@aevytv"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "Shorts"
																}
															},
															"simpleText": "SHORTS"
														},
														"style": "SHORTS",
														"icon": {
															"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
														}
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CIwBEPnnAxgDIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "Y0NAYO1ZoUA",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CIwBEPnnAxgDIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "Y0NAYO1ZoUA"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CIwBEPnnAxgDIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CIsBEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CIsBEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "Y0NAYO1ZoUA",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CIsBEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"Y0NAYO1ZoUA"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"Y0NAYO1ZoUA"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CIsBEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/Y0NAYO1ZoUA/mqdefault_6s.webp?du=3000&sqp=CIDxzrsG&rs=AOn4CLCCNw70ekHlOIO3CzJ-E3EmB6vtBw",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "Follow us on Instagram here: https://www.instagram.com/aevytvdaily/ https://www.instagram.com/aevyvideoschool/\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": false
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CIoBEJ2kBxgIIhMI6IPRlebRigMVEz6tBh33jAcfMgZzZWFyY2hSCUVsb24gTXVza5oBBQgyEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=Y0NAYO1ZoUA&pp=YAHIAQHwAQHoBQGiBhUB4_PBE7uja5eUa7qgJXzVKbr6k6KQBwI%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "Y0NAYO1ZoUA",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCwiBqOuX9N_B298BugMKCIvJndycyPySF7oDCwiElsq_oZL875sBugMKCKzO1rSx773HWboDCgi04a2R5vyWkHa6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE7uja5eUa7qgJXzVKbr6k6KQBwI%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr4---sn-5uaezned.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=63434060ed59a140&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgtZME5BWU8xWm9VQSDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/m_e5-z-5EPsn5-qRZkx9amJhVdZk_jJxwDY_GeQbO0HDoJZF7TCtdZZEWWFFYT3nFFE7paNT=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CIoBEJ2kBxgIIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@aevytv",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UCA295QVkf9O1RQ8_-s3FVXg",
																		"canonicalBaseUrl": "/@aevytv"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "37cG_0L61AE",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/37cG_0L61AE/hq720.jpg?sqp=-oaymwEnCOgCEMoBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLCHp1CClqhjGxzR3As8uf1HXUlY0w",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/37cG_0L61AE/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLBC1bTn9VnELUrXYYQ2nn4dJwdDSg",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "Elon Musk calls some Trump supporters \'fools\' | 9 News Australia"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "Elon Musk calls some Trump supporters \'fools\' | 9 News Australia by 9 News Australia 74,810 views 2 days ago 3 minutes, 16 seconds"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "9 News Australia",
														"navigationEndpoint": {
															"clickTrackingParams": "CIQBENwwGAkiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@9NewsAUS",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCIYLOcEUX6TbBo7HQVF2PKA",
																"canonicalBaseUrl": "/@9NewsAUS"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "2 days ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "3 minutes, 16 seconds"
													}
												},
												"simpleText": "3:16"
											},
											"viewCountText": {
												"simpleText": "74,810 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CIQBENwwGAkiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=37cG_0L61AE&pp=ygUJRWxvbiBNdXNr",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "37cG_0L61AE",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCgjAwubqjozQoWO6AwoIi8md3JzI_JIXugMLCISWyr-hkvzvmwG6AwoIrM7WtLHvvcdZugMKCLThrZHm_JaQdroDCgjx6pnSxeW873O6AwsIpZTD-O3i4cnLAboDCgiKq4-d-uuhqEu6AwoI0dPpv9zLz_t2ugMKCKTBueCirqzbHroDCgizlZ-RzrnfhU7yAwUNJcr0Pg%3D%3D",
													"playerParams": "ygUJRWxvbiBNdXNr",
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr3---sn-5uaeznel.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=dfb706ff42fad401&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"badges": [
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "New",
														"trackingParams": "CIQBENwwGAkiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												}
											],
											"ownerBadges": [
												{
													"metadataBadgeRenderer": {
														"icon": {
															"iconType": "CHECK_CIRCLE_THICK"
														},
														"style": "BADGE_STYLE_TYPE_VERIFIED",
														"tooltip": "Verified",
														"trackingParams": "CIQBENwwGAkiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"accessibilityData": {
															"label": "Verified"
														}
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "9 News Australia",
														"navigationEndpoint": {
															"clickTrackingParams": "CIQBENwwGAkiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@9NewsAUS",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCIYLOcEUX6TbBo7HQVF2PKA",
																"canonicalBaseUrl": "/@9NewsAUS"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "9 News Australia",
														"navigationEndpoint": {
															"clickTrackingParams": "CIQBENwwGAkiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@9NewsAUS",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCIYLOcEUX6TbBo7HQVF2PKA",
																"canonicalBaseUrl": "/@9NewsAUS"
															}
														}
													}
												]
											},
											"trackingParams": "CIQBENwwGAkiEwjog9GV5tGKAxUTPq0GHfeMBx9Agajrl_TfwdvfAQ==",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "74K views"
													}
												},
												"simpleText": "74K views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CIkBEP6YBBgRIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CIkBEP6YBBgRIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "37cG_0L61AE",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CIkBEP6YBBgRIhMI6IPRlebRigMVEz6tBh33jAcf",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"37cG_0L61AE"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"37cG_0L61AE"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CIkBEP6YBBgRIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CIgBENGqBRgSIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"offlineVideoEndpoint": {
																		"videoId": "37cG_0L61AE",
																		"onAddCommand": {
																			"clickTrackingParams": "CIgBENGqBRgSIhMI6IPRlebRigMVEz6tBh33jAcf",
																			"getDownloadActionCommand": {
																				"videoId": "37cG_0L61AE",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CIgBENGqBRgSIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CIQBENwwGAkiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgszN2NHXzBMNjFBRQ%3D%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CIQBENwwGAkiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CIcBEI5iIhMI6IPRlebRigMVEz6tBh33jAcf",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CIQBENwwGAkiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CIQBENwwGAkiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/W18QaF_dsJ5DaZBBiGY0mFlg0gxhaUGmu8W8rdltnRFUgDsRSD76mI9j25Seb3q5GMM3aHmC=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CIQBENwwGAkiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@9NewsAUS",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCIYLOcEUX6TbBo7HQVF2PKA",
															"canonicalBaseUrl": "/@9NewsAUS"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "3 minutes, 16 seconds"
																}
															},
															"simpleText": "3:16"
														},
														"style": "DEFAULT"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CIYBEPnnAxgDIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "37cG_0L61AE",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CIYBEPnnAxgDIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "37cG_0L61AE"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CIYBEPnnAxgDIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CIUBEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CIUBEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "37cG_0L61AE",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CIUBEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"37cG_0L61AE"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"37cG_0L61AE"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CIUBEMfsBBgEIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/37cG_0L61AE/mqdefault_6s.webp?du=3000&sqp=CI7vzrsG&rs=AOn4CLA_PSBWjSjID8lSS-xHhEvxeehxAw",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "Immigration policies are causing a divide in the Republican party as "
															},
															{
																"text": "Elon Musk",
																"bold": true
															},
															{
																"text": " labels some Trump supporters \\"
																fools
																\
																\
																". FOLLOW\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": false
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CIQBENwwGAkiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=37cG_0L61AE&pp=YAHIAQE%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "37cG_0L61AE",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCgjAwubqjozQoWO6AwoIi8md3JzI_JIXugMLCISWyr-hkvzvmwG6AwoIrM7WtLHvvcdZugMKCLThrZHm_JaQdroDCgjx6pnSxeW873O6AwsIpZTD-O3i4cnLAboDCgiKq4-d-uuhqEu6AwoI0dPpv9zLz_t2ugMKCKTBueCirqzbHroDCgizlZ-RzrnfhU7yAwUNJcr0Pg%3D%3D",
													"playerParams": "YAHIAQE%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr3---sn-5uaeznel.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=dfb706ff42fad401&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgszN2NHXzBMNjFBRSDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/W18QaF_dsJ5DaZBBiGY0mFlg0gxhaUGmu8W8rdltnRFUgDsRSD76mI9j25Seb3q5GMM3aHmC=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CIQBENwwGAkiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@9NewsAUS",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UCIYLOcEUX6TbBo7HQVF2PKA",
																		"canonicalBaseUrl": "/@9NewsAUS"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "FyXyQcuHZIs",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/FyXyQcuHZIs/hq720.jpg?sqp=-oaymwEnCOgCEMoBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLD0wGF3zLBzji3grh0RkYnHaK9kyQ",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/FyXyQcuHZIs/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLDyxf0pahiwMluO7N-BN_1nPcfj7Q",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "Musk calls some Trump supporters \\u2018contemptible fools,\\u2019 escalating tension with MAGA hardliners"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "Musk calls some Trump supporters \\u2018contemptible fools,\\u2019 escalating tension with MAGA hardliners by CNN 1,031,509 views 2 days ago 10 minutes, 55 seconds"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "CNN",
														"navigationEndpoint": {
															"clickTrackingParams": "CH4Q3DAYCiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@CNN",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCupvZG-5ko_eiXAupbDfxWw",
																"canonicalBaseUrl": "/@CNN"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "2 days ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "10 minutes, 55 seconds"
													}
												},
												"simpleText": "10:55"
											},
											"viewCountText": {
												"simpleText": "1,031,509 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CH4Q3DAYCiITCOiD0ZXm0YoDFRM-rQYd94wHHzIGc2VhcmNoUglFbG9uIE11c2uaAQMQ9CQ=",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=FyXyQcuHZIs&pp=ygUJRWxvbiBNdXNr",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "FyXyQcuHZIs",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCgjAwubqjozQoWO6AwsIgajrl_TfwdvfAboDCwiElsq_oZL875sBugMKCKzO1rSx773HWboDCgi04a2R5vyWkHa6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "ygUJRWxvbiBNdXNr",
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr2---sn-5uaeznld.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=1725f241cb87648b&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"badges": [
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "New",
														"trackingParams": "CH4Q3DAYCiITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
													}
												},
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "CC",
														"trackingParams": "CH4Q3DAYCiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
														"accessibilityData": {
															"label": "Closed captions"
														}
													}
												}
											],
											"ownerBadges": [
												{
													"metadataBadgeRenderer": {
														"icon": {
															"iconType": "CHECK_CIRCLE_THICK"
														},
														"style": "BADGE_STYLE_TYPE_VERIFIED",
														"tooltip": "Verified",
														"trackingParams": "CH4Q3DAYCiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
														"accessibilityData": {
															"label": "Verified"
														}
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "CNN",
														"navigationEndpoint": {
															"clickTrackingParams": "CH4Q3DAYCiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@CNN",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCupvZG-5ko_eiXAupbDfxWw",
																"canonicalBaseUrl": "/@CNN"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "CNN",
														"navigationEndpoint": {
															"clickTrackingParams": "CH4Q3DAYCiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@CNN",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCupvZG-5ko_eiXAupbDfxWw",
																"canonicalBaseUrl": "/@CNN"
															}
														}
													}
												]
											},
											"trackingParams": "CH4Q3DAYCiITCOiD0ZXm0YoDFRM-rQYd94wHH0CLyZ3cnMj8khc=",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "1 million views"
													}
												},
												"simpleText": "1M views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CIMBEP6YBBgWIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CIMBEP6YBBgWIhMI6IPRlebRigMVEz6tBh33jAcf",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "FyXyQcuHZIs",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CIMBEP6YBBgWIhMI6IPRlebRigMVEz6tBh33jAcf",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"FyXyQcuHZIs"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"FyXyQcuHZIs"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CIMBEP6YBBgWIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CIIBENGqBRgXIhMI6IPRlebRigMVEz6tBh33jAcf",
																	"offlineVideoEndpoint": {
																		"videoId": "FyXyQcuHZIs",
																		"onAddCommand": {
																			"clickTrackingParams": "CIIBENGqBRgXIhMI6IPRlebRigMVEz6tBh33jAcf",
																			"getDownloadActionCommand": {
																				"videoId": "FyXyQcuHZIs",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CIIBENGqBRgXIhMI6IPRlebRigMVEz6tBh33jAcf"
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CH4Q3DAYCiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgtGeVh5UWN1SFpJcw%3D%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CH4Q3DAYCiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CIEBEI5iIhMI6IPRlebRigMVEz6tBh33jAcf",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CH4Q3DAYCiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CH4Q3DAYCiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/n5DRh94eycw6xGcOKTn6LKQwztTwaw24fXPniFTXA3VPgwJaiOFdBwJNtXRHYUf7OdEAk9upwH0=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CH4Q3DAYCiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@CNN",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCupvZG-5ko_eiXAupbDfxWw",
															"canonicalBaseUrl": "/@CNN"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "10 minutes, 55 seconds"
																}
															},
															"simpleText": "10:55"
														},
														"style": "DEFAULT"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CIABEPnnAxgEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "FyXyQcuHZIs",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CIABEPnnAxgEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "FyXyQcuHZIs"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CIABEPnnAxgEIhMI6IPRlebRigMVEz6tBh33jAcf"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CH8Qx-wEGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CH8Qx-wEGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "FyXyQcuHZIs",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CH8Qx-wEGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"FyXyQcuHZIs"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"FyXyQcuHZIs"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CH8Qx-wEGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/FyXyQcuHZIs/mqdefault_6s.webp?du=3000&sqp=CKX6zrsG&rs=AOn4CLCp_3Uorms5fl2_0hClHHqeK9DixQ",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "CNN\'s Katilan Collins, Jamal Simmons, Jonah Goldberg and Donie O\'Sullivan discuss the escalating tension between "
															},
															{
																"text": "Elon Musk",
																"bold": true
															},
															{
																"text": "\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": true
												},
												{
													"snippetText": {
														"runs": [
															{
																"text": "ELON MUSK",
																"bold": true
															},
															{
																"text": " IS NOW GOING AFTER THE HARD LINERS, THE VERY HEART OF THE MAGA BASE, LABELING THEM JUST A\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "Creator-provided subtitles/CC"
															}
														]
													},
													"maxOneLine": true,
													"snippetTimestamp": {
														"accessibility": {
															"accessibilityData": {
																"label": "10 seconds <b>ELON MUSK</b> IS NOW GOING AFTER THE HARD LINERS, THE VERY HEART OF THE MAGA BASE, LABELING THEM JUST A&nbsp;..."
															}
														},
														"simpleText": "0:10"
													},
													"timestampEndpoint": {
														"clickTrackingParams": "CH4Q3DAYCiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/watch?v=FyXyQcuHZIs&t=10s",
																"webPageType": "WEB_PAGE_TYPE_WATCH",
																"rootVe": 3832
															}
														},
														"watchEndpoint": {
															"videoId": "FyXyQcuHZIs",
															"startTimeSeconds": 10,
															"watchEndpointSupportedOnesieConfig": {
																"html5PlaybackOnesieConfig": {
																	"commonConfig": {
																		"url": "https://rr2---sn-5uaeznld.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=1725f241cb87648b&ip=*************&osts=10&mt=1735639559&oweuc="
																	}
																}
															}
														}
													}
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CH4Q3DAYCiITCOiD0ZXm0YoDFRM-rQYd94wHHzIGc2VhcmNoUglFbG9uIE11c2uaAQMQ9CQ=",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=FyXyQcuHZIs&pp=YAHIAQE%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "FyXyQcuHZIs",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCgjAwubqjozQoWO6AwsIgajrl_TfwdvfAboDCwiElsq_oZL875sBugMKCKzO1rSx773HWboDCgi04a2R5vyWkHa6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "YAHIAQE%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr2---sn-5uaeznld.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=1725f241cb87648b&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgtGeVh5UWN1SFpJcyDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/n5DRh94eycw6xGcOKTn6LKQwztTwaw24fXPniFTXA3VPgwJaiOFdBwJNtXRHYUf7OdEAk9upwH0=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CH4Q3DAYCiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@CNN",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UCupvZG-5ko_eiXAupbDfxWw",
																		"canonicalBaseUrl": "/@CNN"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "m9_wkhfyiwQ",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/m9_wkhfyiwQ/hq720_2.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AHOBYACgAqKAgwIABABGFEgXihlMA8=&rs=AOn4CLCnwsDkv9nno1cdTjkGm1M3SEW2xA",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/m9_wkhfyiwQ/hq720_2.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AHOBYACgAqKAgwIABABGFEgXihlMA8=&rs=AOn4CLDauPX-AKUXTpsUUN6fS0kORR1SSQ",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "Elon Musk\'s Starship booster captured in world first. #ElonMusk #Starship #BBCNews"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "Elon Musk\'s Starship booster captured in world first. #ElonMusk #Starship #BBCNews by BBC News 932,287 views 2 months ago 40 seconds - play Short"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "BBC News",
														"navigationEndpoint": {
															"clickTrackingParams": "CHcQnaQHGAsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@BBCNews",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UC16niRr50-MSBwiO3YDb3RA",
																"canonicalBaseUrl": "/@BBCNews"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "2 months ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "40 seconds"
													}
												},
												"simpleText": "0:40"
											},
											"viewCountText": {
												"simpleText": "932,287 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CHcQnaQHGAsiEwjog9GV5tGKAxUTPq0GHfeMBx9SCUVsb24gTXVza5oBBQgyEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/shorts/m9_wkhfyiwQ",
														"webPageType": "WEB_PAGE_TYPE_SHORTS",
														"rootVe": 37414
													}
												},
												"reelWatchEndpoint": {
													"videoId": "m9_wkhfyiwQ",
													"playerParams": "8AEByAMyuAQUogYVAePzwRMabJ2CEpG7UFQeig8rKMpJkAcC",
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/vi/m9_wkhfyiwQ/frame0.jpg",
																"width": 720,
																"height": 1280
															}
														],
														"isOriginalAspectRatio": true
													},
													"overlay": {
														"reelPlayerOverlayRenderer": {
															"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
															"trackingParams": "CH0QsLUEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
														}
													},
													"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
													"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
													"sequenceParams": "CgttOV93a2hmeWl3USocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
													"loggingContext": {
														"vssLoggingContext": {
															"serializedContextData": "CgIIDA%3D%3D"
														},
														"qoeLoggingContext": {
															"serializedContextData": "CgIIDA%3D%3D"
														}
													},
													"ustreamerConfig": "CAw="
												}
											},
											"ownerBadges": [
												{
													"metadataBadgeRenderer": {
														"icon": {
															"iconType": "CHECK_CIRCLE_THICK"
														},
														"style": "BADGE_STYLE_TYPE_VERIFIED",
														"tooltip": "Verified",
														"trackingParams": "CHcQnaQHGAsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"accessibilityData": {
															"label": "Verified"
														}
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "BBC News",
														"navigationEndpoint": {
															"clickTrackingParams": "CHcQnaQHGAsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@BBCNews",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UC16niRr50-MSBwiO3YDb3RA",
																"canonicalBaseUrl": "/@BBCNews"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "BBC News",
														"navigationEndpoint": {
															"clickTrackingParams": "CHcQnaQHGAsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@BBCNews",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UC16niRr50-MSBwiO3YDb3RA",
																"canonicalBaseUrl": "/@BBCNews"
															}
														}
													}
												]
											},
											"trackingParams": "CHcQnaQHGAsiEwjog9GV5tGKAxUTPq0GHfeMBx9AhJbKv6GS_O-bAQ==",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "932K views"
													}
												},
												"simpleText": "932K views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CHwQ_pgEGAsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CHwQ_pgEGAsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "m9_wkhfyiwQ",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CHwQ_pgEGAsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"m9_wkhfyiwQ"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"m9_wkhfyiwQ"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CHwQ_pgEGAsiEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CHsQ0aoFGAwiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"offlineVideoEndpoint": {
																		"videoId": "m9_wkhfyiwQ",
																		"onAddCommand": {
																			"clickTrackingParams": "CHsQ0aoFGAwiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																			"getDownloadActionCommand": {
																				"videoId": "m9_wkhfyiwQ",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CHsQ0aoFGAwiEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CHcQnaQHGAsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgttOV93a2hmeWl3UVICCAI%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CHcQnaQHGAsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CHoQjmIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CHcQnaQHGAsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CHcQnaQHGAsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/y_esGAQOhX4rTpWvrALErAJlFbm_2TIVrvcVfcZny7TuA8dJZgOQcC6KRfd_J5hljFe-foYXj9U=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CHcQnaQHGAsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@BBCNews",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UC16niRr50-MSBwiO3YDb3RA",
															"canonicalBaseUrl": "/@BBCNews"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "Shorts"
																}
															},
															"simpleText": "SHORTS"
														},
														"style": "SHORTS",
														"icon": {
															"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
														}
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CHkQ-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "m9_wkhfyiwQ",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CHkQ-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "m9_wkhfyiwQ"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CHkQ-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CHgQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CHgQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "m9_wkhfyiwQ",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CHgQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"m9_wkhfyiwQ"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"m9_wkhfyiwQ"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CHgQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/m9_wkhfyiwQ/mqdefault_6s.webp?du=3000&sqp=CPDrzrsG&rs=AOn4CLBYShUDXvD_I1lRMUQvnIPflE9OUQ",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CHcQnaQHGAsiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=m9_wkhfyiwQ&pp=YAHIAQHwAQHoBQGiBhUB4_PBExpsnYISkbtQVB6KDysoykmQBwI%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "m9_wkhfyiwQ",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCgjAwubqjozQoWO6AwsIgajrl_TfwdvfAboDCgiLyZ3cnMj8khe6AwoIrM7WtLHvvcdZugMKCLThrZHm_JaQdroDCgjx6pnSxeW873O6AwsIpZTD-O3i4cnLAboDCgiKq4-d-uuhqEu6AwoI0dPpv9zLz_t2ugMKCKTBueCirqzbHroDCgizlZ-RzrnfhU7yAwUNJcr0Pg%3D%3D",
													"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBExpsnYISkbtQVB6KDysoykmQBwI%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr1---sn-5uaeznlz.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=9bdff09217f28b04&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgttOV93a2hmeWl3USDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/y_esGAQOhX4rTpWvrALErAJlFbm_2TIVrvcVfcZny7TuA8dJZgOQcC6KRfd_J5hljFe-foYXj9U=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CHcQnaQHGAsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@BBCNews",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UC16niRr50-MSBwiO3YDb3RA",
																		"canonicalBaseUrl": "/@BBCNews"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "WY73exaVpyw",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/WY73exaVpyw/hq720_2.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGDkgUihyMA8=&rs=AOn4CLC8VkEr1bQ1AVh39QnMs1P2TK6yEA",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/WY73exaVpyw/hq720_2.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGDkgUihyMA8=&rs=AOn4CLCwtEb1FPkhnye1Ir0UE4Sk6fe6kg",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "YouTuber\\u2019s Question Helps Elon Musk Improve Starship"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "YouTuber\\u2019s Question Helps Elon Musk Improve Starship by Everyday Astronaut 6,355,278 views 1 year ago 26 seconds - play Short"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "Everyday Astronaut",
														"navigationEndpoint": {
															"clickTrackingParams": "CHAQnaQHGAwiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@EverydayAstronaut",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UC6uKrU_WqJ1R2HMTY3LIx5Q",
																"canonicalBaseUrl": "/@EverydayAstronaut"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "1 year ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "26 seconds"
													}
												},
												"simpleText": "0:26"
											},
											"viewCountText": {
												"simpleText": "6,355,278 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CHAQnaQHGAwiEwjog9GV5tGKAxUTPq0GHfeMBx9SCUVsb24gTXVza5oBBQgyEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/shorts/WY73exaVpyw",
														"webPageType": "WEB_PAGE_TYPE_SHORTS",
														"rootVe": 37414
													}
												},
												"reelWatchEndpoint": {
													"videoId": "WY73exaVpyw",
													"playerParams": "8AEByAMyuAQUogYVAePzwRPWWtvzNYH-4-evNqtvXQL_kAcC",
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/vi/WY73exaVpyw/frame0.jpg",
																"width": 1080,
																"height": 1920
															}
														],
														"isOriginalAspectRatio": true
													},
													"overlay": {
														"reelPlayerOverlayRenderer": {
															"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
															"trackingParams": "CHYQsLUEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
														}
													},
													"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
													"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
													"sequenceParams": "CgtXWTczZXhhVnB5dyocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
													"loggingContext": {
														"vssLoggingContext": {
															"serializedContextData": "CgIIDA%3D%3D"
														},
														"qoeLoggingContext": {
															"serializedContextData": "CgIIDA%3D%3D"
														}
													},
													"ustreamerConfig": "CAw="
												}
											},
											"ownerBadges": [
												{
													"metadataBadgeRenderer": {
														"icon": {
															"iconType": "CHECK_CIRCLE_THICK"
														},
														"style": "BADGE_STYLE_TYPE_VERIFIED",
														"tooltip": "Verified",
														"trackingParams": "CHAQnaQHGAwiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"accessibilityData": {
															"label": "Verified"
														}
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "Everyday Astronaut",
														"navigationEndpoint": {
															"clickTrackingParams": "CHAQnaQHGAwiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@EverydayAstronaut",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UC6uKrU_WqJ1R2HMTY3LIx5Q",
																"canonicalBaseUrl": "/@EverydayAstronaut"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "Everyday Astronaut",
														"navigationEndpoint": {
															"clickTrackingParams": "CHAQnaQHGAwiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@EverydayAstronaut",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UC6uKrU_WqJ1R2HMTY3LIx5Q",
																"canonicalBaseUrl": "/@EverydayAstronaut"
															}
														}
													}
												]
											},
											"trackingParams": "CHAQnaQHGAwiEwjog9GV5tGKAxUTPq0GHfeMBx9ArM7WtLHvvcdZ",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "6.3 million views"
													}
												},
												"simpleText": "6.3M views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CHUQ_pgEGA4iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CHUQ_pgEGA4iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "WY73exaVpyw",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CHUQ_pgEGA4iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"WY73exaVpyw"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"WY73exaVpyw"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CHUQ_pgEGA4iEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CHQQ0aoFGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"offlineVideoEndpoint": {
																		"videoId": "WY73exaVpyw",
																		"onAddCommand": {
																			"clickTrackingParams": "CHQQ0aoFGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																			"getDownloadActionCommand": {
																				"videoId": "WY73exaVpyw",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CHQQ0aoFGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CHAQnaQHGAwiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgtXWTczZXhhVnB5d1ICCAI%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CHAQnaQHGAwiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CHMQjmIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CHAQnaQHGAwiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CHAQnaQHGAwiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/ytc/AIdro_kUZyAAcoEKNPeHoncE8QHfzXX7dByvg2piQBKsDJOH5_s=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CHAQnaQHGAwiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@EverydayAstronaut",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UC6uKrU_WqJ1R2HMTY3LIx5Q",
															"canonicalBaseUrl": "/@EverydayAstronaut"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "Shorts"
																}
															},
															"simpleText": "SHORTS"
														},
														"style": "SHORTS",
														"icon": {
															"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
														}
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CHIQ-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "WY73exaVpyw",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CHIQ-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "WY73exaVpyw"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CHIQ-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CHEQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CHEQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "WY73exaVpyw",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CHEQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"WY73exaVpyw"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"WY73exaVpyw"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CHEQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/WY73exaVpyw/mqdefault_6s.webp?du=3000&sqp=CNCJz7sG&rs=AOn4CLCpmNtwOrzujbXV6DyOS-yHi7ss6Q",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "We kept seeing a handful of videos with tens of millions of views like this that stole our content without asking and without any form\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": false
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CHAQnaQHGAwiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=WY73exaVpyw&pp=YAHIAQHwAQHoBQGiBhUB4_PBE9Za2_M1gf7j5682q29dAv-QBwI%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "WY73exaVpyw",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCgjAwubqjozQoWO6AwsIgajrl_TfwdvfAboDCgiLyZ3cnMj8khe6AwsIhJbKv6GS_O-bAboDCgi04a2R5vyWkHa6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE9Za2_M1gf7j5682q29dAv-QBwI%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr1---sn-5uaezned.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=598ef77b1695a72c&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgtXWTczZXhhVnB5dyDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/ytc/AIdro_kUZyAAcoEKNPeHoncE8QHfzXX7dByvg2piQBKsDJOH5_s=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CHAQnaQHGAwiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@EverydayAstronaut",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UC6uKrU_WqJ1R2HMTY3LIx5Q",
																		"canonicalBaseUrl": "/@EverydayAstronaut"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "diBb5mIrcLQ",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/diBb5mIrcLQ/hq720.jpg?sqp=-oaymwEnCOgCEMoBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLCJCZcj22SLj1kuKzNHrIShHzOFJw",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/diBb5mIrcLQ/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLB1blpblL_Mg00Lt1D9jWmjK3z_8g",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "Germany accuses Elon Musk of interfering in their upcoming election"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "Germany accuses Elon Musk of interfering in their upcoming election by CBC News 36,874 views 19 hours ago 2 minutes, 54 seconds"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "CBC News",
														"navigationEndpoint": {
															"clickTrackingParams": "CGoQ3DAYDSITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@CBCNews",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCuFFtHWoLl5fauMMD5Ww2jA",
																"canonicalBaseUrl": "/@CBCNews"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "19 hours ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "2 minutes, 54 seconds"
													}
												},
												"simpleText": "2:54"
											},
											"viewCountText": {
												"simpleText": "36,874 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CGoQ3DAYDSITCOiD0ZXm0YoDFRM-rQYd94wHHzIGc2VhcmNoUglFbG9uIE11c2uaAQMQ9CQ=",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=diBb5mIrcLQ&pp=ygUJRWxvbiBNdXNr",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "diBb5mIrcLQ",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCgjAwubqjozQoWO6AwsIgajrl_TfwdvfAboDCgiLyZ3cnMj8khe6AwsIhJbKv6GS_O-bAboDCgiszta0se-9x1m6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "ygUJRWxvbiBNdXNr",
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr3---sn-5uaeznsl.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=76205be6622b70b4&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"badges": [
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "New",
														"trackingParams": "CGoQ3DAYDSITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
													}
												}
											],
											"ownerBadges": [
												{
													"metadataBadgeRenderer": {
														"icon": {
															"iconType": "CHECK_CIRCLE_THICK"
														},
														"style": "BADGE_STYLE_TYPE_VERIFIED",
														"tooltip": "Verified",
														"trackingParams": "CGoQ3DAYDSITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
														"accessibilityData": {
															"label": "Verified"
														}
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "CBC News",
														"navigationEndpoint": {
															"clickTrackingParams": "CGoQ3DAYDSITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@CBCNews",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCuFFtHWoLl5fauMMD5Ww2jA",
																"canonicalBaseUrl": "/@CBCNews"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "CBC News",
														"navigationEndpoint": {
															"clickTrackingParams": "CGoQ3DAYDSITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@CBCNews",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCuFFtHWoLl5fauMMD5Ww2jA",
																"canonicalBaseUrl": "/@CBCNews"
															}
														}
													}
												]
											},
											"trackingParams": "CGoQ3DAYDSITCOiD0ZXm0YoDFRM-rQYd94wHH0C04a2R5vyWkHY=",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "36K views"
													}
												},
												"simpleText": "36K views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CG8Q_pgEGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CG8Q_pgEGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "diBb5mIrcLQ",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CG8Q_pgEGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"diBb5mIrcLQ"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"diBb5mIrcLQ"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CG8Q_pgEGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CG4Q0aoFGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"offlineVideoEndpoint": {
																		"videoId": "diBb5mIrcLQ",
																		"onAddCommand": {
																			"clickTrackingParams": "CG4Q0aoFGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																			"getDownloadActionCommand": {
																				"videoId": "diBb5mIrcLQ",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CG4Q0aoFGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CGoQ3DAYDSITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgtkaUJiNW1JcmNMUQ%3D%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CGoQ3DAYDSITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CG0QjmIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CGoQ3DAYDSITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CGoQ3DAYDSITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/JlvqbBZ_YJO2vw67UZs0kQOiIycVPHzFzytZg_TJU6YJpnuEi5Gbt-ST5oVElP9m4pIa1Mho=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CGoQ3DAYDSITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@CBCNews",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCuFFtHWoLl5fauMMD5Ww2jA",
															"canonicalBaseUrl": "/@CBCNews"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "2 minutes, 54 seconds"
																}
															},
															"simpleText": "2:54"
														},
														"style": "DEFAULT"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CGwQ-ecDGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "diBb5mIrcLQ",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CGwQ-ecDGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "diBb5mIrcLQ"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CGwQ-ecDGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CGsQx-wEGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CGsQx-wEGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "diBb5mIrcLQ",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CGsQx-wEGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"diBb5mIrcLQ"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"diBb5mIrcLQ"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CGsQx-wEGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/diBb5mIrcLQ/mqdefault_6s.webp?du=3000&sqp=CLDqzrsG&rs=AOn4CLDUrhloxfeaeUhZa1JyRpk3ZySo9A",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "Germany\'s government is accusing "
															},
															{
																"text": "Elon Musk",
																"bold": true
															},
															{
																"text": " of trying to influence their upcoming election after he expressed support for the\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": false
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CGoQ3DAYDSITCOiD0ZXm0YoDFRM-rQYd94wHHzIGc2VhcmNoUglFbG9uIE11c2uaAQMQ9CQ=",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=diBb5mIrcLQ&pp=YAHIAQE%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "diBb5mIrcLQ",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCgjAwubqjozQoWO6AwsIgajrl_TfwdvfAboDCgiLyZ3cnMj8khe6AwsIhJbKv6GS_O-bAboDCgiszta0se-9x1m6AwoI8eqZ0sXlvO9zugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "YAHIAQE%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr3---sn-5uaeznsl.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=76205be6622b70b4&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgtkaUJiNW1JcmNMUSDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/JlvqbBZ_YJO2vw67UZs0kQOiIycVPHzFzytZg_TJU6YJpnuEi5Gbt-ST5oVElP9m4pIa1Mho=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CGoQ3DAYDSITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@CBCNews",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UCuFFtHWoLl5fauMMD5Ww2jA",
																		"canonicalBaseUrl": "/@CBCNews"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "c97zLFpGdXE",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/c97zLFpGdXE/hq720_2.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGD4gYyhlMA8=&rs=AOn4CLBxPhNtYZ8Wb5ukzLG-QNfD38rEDg",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/c97zLFpGdXE/hq720_2.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGD4gYyhlMA8=&rs=AOn4CLCMsuxGS3aaQe4Xf79dV9NTaIL49w",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "Why Elon Musk\\u2019s SpaceX, Palantir, and Anduril Are Joining Forces to Disrupt U.S. Defense?"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "Why Elon Musk\\u2019s SpaceX, Palantir, and Anduril Are Joining Forces to Disrupt U.S. Defense? by BuildingIt AI 11,968 views 1 day ago 19 seconds - play Short"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "BuildingIt AI",
														"navigationEndpoint": {
															"clickTrackingParams": "CGMQnaQHGA4iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@buildingitAI",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCcKj4ineT8qpG2krHEfoIJA",
																"canonicalBaseUrl": "/@buildingitAI"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "1 day ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "19 seconds"
													}
												},
												"simpleText": "0:19"
											},
											"viewCountText": {
												"simpleText": "11,968 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CGMQnaQHGA4iEwjog9GV5tGKAxUTPq0GHfeMBx9SCUVsb24gTXVza5oBBQgyEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/shorts/c97zLFpGdXE",
														"webPageType": "WEB_PAGE_TYPE_SHORTS",
														"rootVe": 37414
													}
												},
												"reelWatchEndpoint": {
													"videoId": "c97zLFpGdXE",
													"playerParams": "8AEByAMyuAQUogYVAePzwRNjfXWe8C-n1_pYBvnxh4VqkAcC",
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/vi/c97zLFpGdXE/frame0.jpg",
																"width": 1080,
																"height": 1920
															}
														],
														"isOriginalAspectRatio": true
													},
													"overlay": {
														"reelPlayerOverlayRenderer": {
															"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
															"trackingParams": "CGkQsLUEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
														}
													},
													"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
													"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
													"sequenceParams": "CgtjOTd6TEZwR2RYRSocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
													"loggingContext": {
														"vssLoggingContext": {
															"serializedContextData": "CgIIDA%3D%3D"
														},
														"qoeLoggingContext": {
															"serializedContextData": "CgIIDA%3D%3D"
														}
													},
													"ustreamerConfig": "CAw="
												}
											},
											"badges": [
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "New",
														"trackingParams": "CGMQnaQHGA4iEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "BuildingIt AI",
														"navigationEndpoint": {
															"clickTrackingParams": "CGMQnaQHGA4iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@buildingitAI",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCcKj4ineT8qpG2krHEfoIJA",
																"canonicalBaseUrl": "/@buildingitAI"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "BuildingIt AI",
														"navigationEndpoint": {
															"clickTrackingParams": "CGMQnaQHGA4iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@buildingitAI",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCcKj4ineT8qpG2krHEfoIJA",
																"canonicalBaseUrl": "/@buildingitAI"
															}
														}
													}
												]
											},
											"trackingParams": "CGMQnaQHGA4iEwjog9GV5tGKAxUTPq0GHfeMBx9A8eqZ0sXlvO9z",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "11K views"
													}
												},
												"simpleText": "11K views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CGgQ_pgEGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CGgQ_pgEGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "c97zLFpGdXE",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CGgQ_pgEGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"c97zLFpGdXE"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"c97zLFpGdXE"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CGgQ_pgEGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CGcQ0aoFGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"offlineVideoEndpoint": {
																		"videoId": "c97zLFpGdXE",
																		"onAddCommand": {
																			"clickTrackingParams": "CGcQ0aoFGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																			"getDownloadActionCommand": {
																				"videoId": "c97zLFpGdXE",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CGcQ0aoFGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CGMQnaQHGA4iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgtjOTd6TEZwR2RYRVICCAI%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CGMQnaQHGA4iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CGYQjmIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CGMQnaQHGA4iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CGMQnaQHGA4iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/aXUbao3tdghG2tCJhvB4WpTHlGR8Qku457yu5SUl3Etqgy6TDrWb-YTIv4xbDlOn9s-P0Jcm_A0=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CGMQnaQHGA4iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@buildingitAI",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCcKj4ineT8qpG2krHEfoIJA",
															"canonicalBaseUrl": "/@buildingitAI"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "Shorts"
																}
															},
															"simpleText": "SHORTS"
														},
														"style": "SHORTS",
														"icon": {
															"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
														}
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CGUQ-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "c97zLFpGdXE",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CGUQ-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "c97zLFpGdXE"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CGUQ-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CGQQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CGQQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "c97zLFpGdXE",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CGQQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"c97zLFpGdXE"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"c97zLFpGdXE"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CGQQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/c97zLFpGdXE/mqdefault_6s.webp?du=3000&sqp=CJz6zrsG&rs=AOn4CLCNAkaCXxHLumZ4y2QSkcqpkIIrhg",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "Do you agree with "
															},
															{
																"text": "Musk\'s",
																"bold": true
															},
															{
																"text": " criticism of Lockheed Martin\'s F-35? Share your thoughts in the comments. Palantir and Anduril, two\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": false
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CGMQnaQHGA4iEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=c97zLFpGdXE&pp=YAHIAQHwAQHoBQGiBhUB4_PBE2N9dZ7wL6fX-lgG-fGHhWqQBwI%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "c97zLFpGdXE",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCgjAwubqjozQoWO6AwsIgajrl_TfwdvfAboDCgiLyZ3cnMj8khe6AwsIhJbKv6GS_O-bAboDCgiszta0se-9x1m6AwoItOGtkeb8lpB2ugMLCKWUw_jt4uHJywG6AwoIiquPnfrroahLugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE2N9dZ7wL6fX-lgG-fGHhWqQBwI%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr4---sn-5uaeznl6.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=73def32c5a467571&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgtjOTd6TEZwR2RYRSDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/aXUbao3tdghG2tCJhvB4WpTHlGR8Qku457yu5SUl3Etqgy6TDrWb-YTIv4xbDlOn9s-P0Jcm_A0=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CGMQnaQHGA4iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@buildingitAI",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UCcKj4ineT8qpG2krHEfoIJA",
																		"canonicalBaseUrl": "/@buildingitAI"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "y5OHFt8QyiU",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/y5OHFt8QyiU/hq720_2.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGGEgYShhMA8=&rs=AOn4CLATbnYXHhfsrCm1aHvoqlXbC0J3kg",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/y5OHFt8QyiU/hq720_2.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGGEgYShhMA8=&rs=AOn4CLCyYZ8WmgsgOYVgfLOW3V8sWdVQ4A",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "Elon Musk: Working from home is \'morally wrong\' #Shorts"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "Elon Musk: Working from home is \'morally wrong\' #Shorts by CNBC Television 3,391,750 views 1 year ago 52 seconds - play Short"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "CNBC Television",
														"navigationEndpoint": {
															"clickTrackingParams": "CFwQnaQHGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@CNBCtelevision",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCrp_UI8XtuYfpiqluWLD7Lw",
																"canonicalBaseUrl": "/@CNBCtelevision"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "1 year ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "52 seconds"
													}
												},
												"simpleText": "0:52"
											},
											"viewCountText": {
												"simpleText": "3,391,750 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CFwQnaQHGA8iEwjog9GV5tGKAxUTPq0GHfeMBx9SCUVsb24gTXVza5oBBQgyEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/shorts/y5OHFt8QyiU",
														"webPageType": "WEB_PAGE_TYPE_SHORTS",
														"rootVe": 37414
													}
												},
												"reelWatchEndpoint": {
													"videoId": "y5OHFt8QyiU",
													"playerParams": "8AEByAMyuAQUogYVAePzwRNyuxhFn7v-OhlGW5J8YiGlkAcC",
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/vi/y5OHFt8QyiU/frame0.jpg",
																"width": 1080,
																"height": 1920
															}
														],
														"isOriginalAspectRatio": true
													},
													"overlay": {
														"reelPlayerOverlayRenderer": {
															"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
															"trackingParams": "CGIQsLUEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
														}
													},
													"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
													"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
													"sequenceParams": "Cgt5NU9IRnQ4UXlpVSocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
													"loggingContext": {
														"vssLoggingContext": {
															"serializedContextData": "CgIIDA%3D%3D"
														},
														"qoeLoggingContext": {
															"serializedContextData": "CgIIDA%3D%3D"
														}
													},
													"ustreamerConfig": "CAw="
												}
											},
											"ownerBadges": [
												{
													"metadataBadgeRenderer": {
														"icon": {
															"iconType": "CHECK_CIRCLE_THICK"
														},
														"style": "BADGE_STYLE_TYPE_VERIFIED",
														"tooltip": "Verified",
														"trackingParams": "CFwQnaQHGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"accessibilityData": {
															"label": "Verified"
														}
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "CNBC Television",
														"navigationEndpoint": {
															"clickTrackingParams": "CFwQnaQHGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@CNBCtelevision",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCrp_UI8XtuYfpiqluWLD7Lw",
																"canonicalBaseUrl": "/@CNBCtelevision"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "CNBC Television",
														"navigationEndpoint": {
															"clickTrackingParams": "CFwQnaQHGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@CNBCtelevision",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCrp_UI8XtuYfpiqluWLD7Lw",
																"canonicalBaseUrl": "/@CNBCtelevision"
															}
														}
													}
												]
											},
											"trackingParams": "CFwQnaQHGA8iEwjog9GV5tGKAxUTPq0GHfeMBx9ApZTD-O3i4cnLAQ==",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "3.3 million views"
													}
												},
												"simpleText": "3.3M views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CGEQ_pgEGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CGEQ_pgEGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "y5OHFt8QyiU",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CGEQ_pgEGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"y5OHFt8QyiU"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"y5OHFt8QyiU"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CGEQ_pgEGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CGAQ0aoFGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"offlineVideoEndpoint": {
																		"videoId": "y5OHFt8QyiU",
																		"onAddCommand": {
																			"clickTrackingParams": "CGAQ0aoFGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																			"getDownloadActionCommand": {
																				"videoId": "y5OHFt8QyiU",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CGAQ0aoFGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CFwQnaQHGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "Cgt5NU9IRnQ4UXlpVVICCAI%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CFwQnaQHGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CF8QjmIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CFwQnaQHGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CFwQnaQHGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/WhC1oycyrx_oXiMM1EeRZm6sWBA1ciHeyeuIkQuDuK2aeQBbLIkfshKIHJuUjD7d1w-gq4AqlyU=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CFwQnaQHGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@CNBCtelevision",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCrp_UI8XtuYfpiqluWLD7Lw",
															"canonicalBaseUrl": "/@CNBCtelevision"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "Shorts"
																}
															},
															"simpleText": "SHORTS"
														},
														"style": "SHORTS",
														"icon": {
															"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
														}
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CF4Q-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "y5OHFt8QyiU",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CF4Q-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "y5OHFt8QyiU"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CF4Q-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CF0Qx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CF0Qx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "y5OHFt8QyiU",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CF0Qx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"y5OHFt8QyiU"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"y5OHFt8QyiU"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CF0Qx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/y5OHFt8QyiU/mqdefault_6s.webp?du=3000&sqp=CK3zzrsG&rs=AOn4CLC8rzdDJHBqMp2YN2Ruu5mIGeFvHQ",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "Silicon Valley \\u201claptop classes\\u201d need to get off their \\u201cmoral high horse\\u201d with their \\u201cwork-from-home bulls***,\\u201d Tesla CEO "
															},
															{
																"text": "Elon Musk",
																"bold": true
															},
															{
																"text": "\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": false
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CFwQnaQHGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=y5OHFt8QyiU&pp=YAHIAQHwAQHoBQGiBhUB4_PBE3K7GEWfu_46GUZbknxiIaWQBwI%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "y5OHFt8QyiU",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCgjAwubqjozQoWO6AwsIgajrl_TfwdvfAboDCgiLyZ3cnMj8khe6AwsIhJbKv6GS_O-bAboDCgiszta0se-9x1m6AwoItOGtkeb8lpB2ugMKCPHqmdLF5bzvc7oDCgiKq4-d-uuhqEu6AwoI0dPpv9zLz_t2ugMKCKTBueCirqzbHroDCgizlZ-RzrnfhU7yAwUNJcr0Pg%3D%3D",
													"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE3K7GEWfu_46GUZbknxiIaWQBwI%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr1---sn-5ualdnll.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=cb938716df10ca25&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "Egt5NU9IRnQ4UXlpVSDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/WhC1oycyrx_oXiMM1EeRZm6sWBA1ciHeyeuIkQuDuK2aeQBbLIkfshKIHJuUjD7d1w-gq4AqlyU=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CFwQnaQHGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@CNBCtelevision",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UCrp_UI8XtuYfpiqluWLD7Lw",
																		"canonicalBaseUrl": "/@CNBCtelevision"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "S1CHX6Oj1Yo",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/S1CHX6Oj1Yo/hq720_2.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGGUgTyhWMA8=&rs=AOn4CLDGgV3MubB9MhNc4b-VUXQJvolzZg",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/S1CHX6Oj1Yo/hq720_2.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGGUgTyhWMA8=&rs=AOn4CLAYVGVf8-cogywuqzJdFg3QOjgsGA",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "Elon Musk Never Give Up #spacex #starship #shorts"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "Elon Musk Never Give Up #spacex #starship #shorts by AstraNova 4,799,427 views 2 months ago 30 seconds - play Short"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "AstraNova",
														"navigationEndpoint": {
															"clickTrackingParams": "CFUQnaQHGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@TheAstraNova",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCnbwpG_B1SXGx1KeEAyxO9w",
																"canonicalBaseUrl": "/@TheAstraNova"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "2 months ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "30 seconds"
													}
												},
												"simpleText": "0:30"
											},
											"viewCountText": {
												"simpleText": "4,799,427 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CFUQnaQHGBAiEwjog9GV5tGKAxUTPq0GHfeMBx9SCUVsb24gTXVza5oBBQgyEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/shorts/S1CHX6Oj1Yo",
														"webPageType": "WEB_PAGE_TYPE_SHORTS",
														"rootVe": 37414
													}
												},
												"reelWatchEndpoint": {
													"videoId": "S1CHX6Oj1Yo",
													"playerParams": "8AEByAMyuAQUogYVAePzwRN5fydXxvWYt2dFOM6azms6kAcC",
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/vi/S1CHX6Oj1Yo/frame0.jpg",
																"width": 1080,
																"height": 1920
															}
														],
														"isOriginalAspectRatio": true
													},
													"overlay": {
														"reelPlayerOverlayRenderer": {
															"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
															"trackingParams": "CFsQsLUEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
														}
													},
													"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
													"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
													"sequenceParams": "CgtTMUNIWDZPajFZbyocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
													"loggingContext": {
														"vssLoggingContext": {
															"serializedContextData": "CgIIDA%3D%3D"
														},
														"qoeLoggingContext": {
															"serializedContextData": "CgIIDA%3D%3D"
														}
													},
													"ustreamerConfig": "CAw="
												}
											},
											"ownerText": {
												"runs": [
													{
														"text": "AstraNova",
														"navigationEndpoint": {
															"clickTrackingParams": "CFUQnaQHGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@TheAstraNova",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCnbwpG_B1SXGx1KeEAyxO9w",
																"canonicalBaseUrl": "/@TheAstraNova"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "AstraNova",
														"navigationEndpoint": {
															"clickTrackingParams": "CFUQnaQHGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@TheAstraNova",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCnbwpG_B1SXGx1KeEAyxO9w",
																"canonicalBaseUrl": "/@TheAstraNova"
															}
														}
													}
												]
											},
											"trackingParams": "CFUQnaQHGBAiEwjog9GV5tGKAxUTPq0GHfeMBx9AiquPnfrroahL",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "4.7 million views"
													}
												},
												"simpleText": "4.7M views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CFoQ_pgEGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CFoQ_pgEGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "S1CHX6Oj1Yo",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CFoQ_pgEGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"S1CHX6Oj1Yo"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"S1CHX6Oj1Yo"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CFoQ_pgEGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CFkQ0aoFGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"offlineVideoEndpoint": {
																		"videoId": "S1CHX6Oj1Yo",
																		"onAddCommand": {
																			"clickTrackingParams": "CFkQ0aoFGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																			"getDownloadActionCommand": {
																				"videoId": "S1CHX6Oj1Yo",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CFkQ0aoFGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CFUQnaQHGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgtTMUNIWDZPajFZb1ICCAI%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CFUQnaQHGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CFgQjmIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CFUQnaQHGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CFUQnaQHGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/6X-ICpPuFvL7tSQz2OazxPwWkqAZvsk8sQ1CxNa7RkmenYCbI1MJAk_EcOyCw0eCmSuEYaMAYQ=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CFUQnaQHGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@TheAstraNova",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCnbwpG_B1SXGx1KeEAyxO9w",
															"canonicalBaseUrl": "/@TheAstraNova"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "Shorts"
																}
															},
															"simpleText": "SHORTS"
														},
														"style": "SHORTS",
														"icon": {
															"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
														}
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CFcQ-ecDGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "S1CHX6Oj1Yo",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CFcQ-ecDGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "S1CHX6Oj1Yo"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CFcQ-ecDGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CFYQx-wEGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CFYQx-wEGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "S1CHX6Oj1Yo",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CFYQx-wEGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"S1CHX6Oj1Yo"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"S1CHX6Oj1Yo"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CFYQx-wEGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/S1CHX6Oj1Yo/mqdefault_6s.webp?du=3000&sqp=CJCJz7sG&rs=AOn4CLCAUFEFQWnMqp5bDys1uRQzYbHLHQ",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "Never ever give up on your dreams, especially not when it\'s important for humanity. #space #edit #"
															},
															{
																"text": "elonmusk",
																"bold": true
															},
															{
																"text": " #elon #shortfeed\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": false
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CFUQnaQHGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=S1CHX6Oj1Yo&pp=YAHIAQHwAQHoBQGiBhUB4_PBE3l_J1fG9Zi3Z0U4zprOazqQBwI%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "S1CHX6Oj1Yo",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCgjAwubqjozQoWO6AwsIgajrl_TfwdvfAboDCgiLyZ3cnMj8khe6AwsIhJbKv6GS_O-bAboDCgiszta0se-9x1m6AwoItOGtkeb8lpB2ugMKCPHqmdLF5bzvc7oDCwillMP47eLhycsBugMKCNHT6b_cy8_7droDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE3l_J1fG9Zi3Z0U4zprOazqQBwI%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr5---sn-5uaeznes.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=4b50875fa3a3d58a&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgtTMUNIWDZPajFZbyDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/6X-ICpPuFvL7tSQz2OazxPwWkqAZvsk8sQ1CxNa7RkmenYCbI1MJAk_EcOyCw0eCmSuEYaMAYQ=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CFUQnaQHGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@TheAstraNova",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UCnbwpG_B1SXGx1KeEAyxO9w",
																		"canonicalBaseUrl": "/@TheAstraNova"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "dvc-Xcf6adE",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/dvc-Xcf6adE/hq720.jpg?sqp=-oaymwEnCOgCEMoBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLD07K7D4tipgIzkFzTsdBnJwsAykw",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/dvc-Xcf6adE/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLDTwX6oPCQyDJ8BFJMmH94LF4nA1A",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "Trump Sides With Elon Musk On H1-B Visa Programme | World News"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "Trump Sides With Elon Musk On H1-B Visa Programme | World News by WION 118,476 views 2 days ago 1 minute, 50 seconds"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "WION",
														"navigationEndpoint": {
															"clickTrackingParams": "CE8Q3DAYESITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@WION",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UC_gUM8rL-Lrg6O3adPW9K1g",
																"canonicalBaseUrl": "/@WION"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "2 days ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "1 minute, 50 seconds"
													}
												},
												"simpleText": "1:50"
											},
											"viewCountText": {
												"simpleText": "118,476 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CE8Q3DAYESITCOiD0ZXm0YoDFRM-rQYd94wHHzIGc2VhcmNoUglFbG9uIE11c2uaAQMQ9CQ=",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=dvc-Xcf6adE&pp=ygUJRWxvbiBNdXNr",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "dvc-Xcf6adE",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCgjAwubqjozQoWO6AwsIgajrl_TfwdvfAboDCgiLyZ3cnMj8khe6AwsIhJbKv6GS_O-bAboDCgiszta0se-9x1m6AwoItOGtkeb8lpB2ugMKCPHqmdLF5bzvc7oDCwillMP47eLhycsBugMKCIqrj53666GoS7oDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "ygUJRWxvbiBNdXNr",
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr2---sn-5uaeznez.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=76f73e5dc7fa69d1&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"badges": [
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "New",
														"trackingParams": "CE8Q3DAYESITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
													}
												}
											],
											"ownerBadges": [
												{
													"metadataBadgeRenderer": {
														"icon": {
															"iconType": "CHECK_CIRCLE_THICK"
														},
														"style": "BADGE_STYLE_TYPE_VERIFIED",
														"tooltip": "Verified",
														"trackingParams": "CE8Q3DAYESITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
														"accessibilityData": {
															"label": "Verified"
														}
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "WION",
														"navigationEndpoint": {
															"clickTrackingParams": "CE8Q3DAYESITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@WION",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UC_gUM8rL-Lrg6O3adPW9K1g",
																"canonicalBaseUrl": "/@WION"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "WION",
														"navigationEndpoint": {
															"clickTrackingParams": "CE8Q3DAYESITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@WION",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UC_gUM8rL-Lrg6O3adPW9K1g",
																"canonicalBaseUrl": "/@WION"
															}
														}
													}
												]
											},
											"trackingParams": "CE8Q3DAYESITCOiD0ZXm0YoDFRM-rQYd94wHH0DR0-m_3MvP-3Y=",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "118K views"
													}
												},
												"simpleText": "118K views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CFQQ_pgEGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CFQQ_pgEGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "dvc-Xcf6adE",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CFQQ_pgEGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"dvc-Xcf6adE"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"dvc-Xcf6adE"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CFQQ_pgEGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CFMQ0aoFGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"offlineVideoEndpoint": {
																		"videoId": "dvc-Xcf6adE",
																		"onAddCommand": {
																			"clickTrackingParams": "CFMQ0aoFGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																			"getDownloadActionCommand": {
																				"videoId": "dvc-Xcf6adE",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CFMQ0aoFGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CE8Q3DAYESITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgtkdmMtWGNmNmFkRQ%3D%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CE8Q3DAYESITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CFIQjmIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CE8Q3DAYESITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CE8Q3DAYESITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/r2ZGA9E-wWJha_PIoNh8df3C_Y8uenieTqQ-Qj2-ibWQ6T__-mVxUvplOqoagFLsicskn-j3sw=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CE8Q3DAYESITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@WION",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UC_gUM8rL-Lrg6O3adPW9K1g",
															"canonicalBaseUrl": "/@WION"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "1 minute, 50 seconds"
																}
															},
															"simpleText": "1:50"
														},
														"style": "DEFAULT"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CFEQ-ecDGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "dvc-Xcf6adE",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CFEQ-ecDGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "dvc-Xcf6adE"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CFEQ-ecDGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CFAQx-wEGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CFAQx-wEGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "dvc-Xcf6adE",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CFAQx-wEGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"dvc-Xcf6adE"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"dvc-Xcf6adE"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CFAQx-wEGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/dvc-Xcf6adE/mqdefault_6s.webp?du=3000&sqp=CPyAz7sG&rs=AOn4CLA9_E7uN5cfXnnihPaf_XS-DZLM3Q",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "Donald Trump has expressed support for "
															},
															{
																"text": "Elon Musk\'s",
																"bold": true
															},
															{
																"text": " stance on the H1-B visa program, advocating for changes to attract top\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": false
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CE8Q3DAYESITCOiD0ZXm0YoDFRM-rQYd94wHHzIGc2VhcmNoUglFbG9uIE11c2uaAQMQ9CQ=",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=dvc-Xcf6adE&pp=YAHIAQE%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "dvc-Xcf6adE",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCgjAwubqjozQoWO6AwsIgajrl_TfwdvfAboDCgiLyZ3cnMj8khe6AwsIhJbKv6GS_O-bAboDCgiszta0se-9x1m6AwoItOGtkeb8lpB2ugMKCPHqmdLF5bzvc7oDCwillMP47eLhycsBugMKCIqrj53666GoS7oDCgikwbngoq6s2x66AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "YAHIAQE%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr2---sn-5uaeznez.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=76f73e5dc7fa69d1&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgtkdmMtWGNmNmFkRSDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/r2ZGA9E-wWJha_PIoNh8df3C_Y8uenieTqQ-Qj2-ibWQ6T__-mVxUvplOqoagFLsicskn-j3sw=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CE8Q3DAYESITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@WION",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UC_gUM8rL-Lrg6O3adPW9K1g",
																		"canonicalBaseUrl": "/@WION"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "HraxciwOYKQ",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/HraxciwOYKQ/hq720.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGH8gOSgfMA8=&rs=AOn4CLD3oY81nXJavAqgII8dq0oKXnsXWA",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/HraxciwOYKQ/hq720.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGH8gOSgfMA8=&rs=AOn4CLBltTSEFaqsEeG5SeRv2lKzblnXPQ",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "How To Get Hired By Elon Musk With NO College Degree"
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "How To Get Hired By Elon Musk With NO College Degree by Graham Stephan 21,373,413 views 10 months ago 57 seconds - play Short"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "Graham Stephan",
														"navigationEndpoint": {
															"clickTrackingParams": "CEgQnaQHGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@GrahamStephan",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCV6KDgJskWaEckne5aPA0aQ",
																"canonicalBaseUrl": "/@GrahamStephan"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "10 months ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "57 seconds"
													}
												},
												"simpleText": "0:57"
											},
											"viewCountText": {
												"simpleText": "21,373,413 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CEgQnaQHGBIiEwjog9GV5tGKAxUTPq0GHfeMBx9SCUVsb24gTXVza5oBBQgyEPQk",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/shorts/HraxciwOYKQ",
														"webPageType": "WEB_PAGE_TYPE_SHORTS",
														"rootVe": 37414
													}
												},
												"reelWatchEndpoint": {
													"videoId": "HraxciwOYKQ",
													"playerParams": "8AEByAMyuAQUogYVAePzwRMBGke38plY7Lqc-TQ-XMlckAcC",
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/vi/HraxciwOYKQ/frame0.jpg",
																"width": 1080,
																"height": 1920
															}
														],
														"isOriginalAspectRatio": true
													},
													"overlay": {
														"reelPlayerOverlayRenderer": {
															"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
															"trackingParams": "CE4QsLUEIhMI6IPRlebRigMVEz6tBh33jAcf",
															"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
														}
													},
													"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
													"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
													"sequenceParams": "CgtIcmF4Y2l3T1lLUSocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
													"loggingContext": {
														"vssLoggingContext": {
															"serializedContextData": "CgIIDA%3D%3D"
														},
														"qoeLoggingContext": {
															"serializedContextData": "CgIIDA%3D%3D"
														}
													},
													"ustreamerConfig": "CAw="
												}
											},
											"ownerBadges": [
												{
													"metadataBadgeRenderer": {
														"icon": {
															"iconType": "CHECK_CIRCLE_THICK"
														},
														"style": "BADGE_STYLE_TYPE_VERIFIED",
														"tooltip": "Verified",
														"trackingParams": "CEgQnaQHGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"accessibilityData": {
															"label": "Verified"
														}
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "Graham Stephan",
														"navigationEndpoint": {
															"clickTrackingParams": "CEgQnaQHGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@GrahamStephan",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCV6KDgJskWaEckne5aPA0aQ",
																"canonicalBaseUrl": "/@GrahamStephan"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "Graham Stephan",
														"navigationEndpoint": {
															"clickTrackingParams": "CEgQnaQHGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@GrahamStephan",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCV6KDgJskWaEckne5aPA0aQ",
																"canonicalBaseUrl": "/@GrahamStephan"
															}
														}
													}
												]
											},
											"trackingParams": "CEgQnaQHGBIiEwjog9GV5tGKAxUTPq0GHfeMBx9ApMG54KKurNse",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "21 million views"
													}
												},
												"simpleText": "21M views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CE0Q_pgEGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CE0Q_pgEGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "HraxciwOYKQ",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CE0Q_pgEGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"HraxciwOYKQ"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"HraxciwOYKQ"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CE0Q_pgEGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CEwQ0aoFGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"offlineVideoEndpoint": {
																		"videoId": "HraxciwOYKQ",
																		"onAddCommand": {
																			"clickTrackingParams": "CEwQ0aoFGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																			"getDownloadActionCommand": {
																				"videoId": "HraxciwOYKQ",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CEwQ0aoFGBEiEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CEgQnaQHGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgtIcmF4Y2l3T1lLUVICCAI%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CEgQnaQHGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CEsQjmIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CEgQnaQHGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CEgQnaQHGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/ytc/AIdro_m4km0pJvdvRxT_gFN6WS16Ggl9D_eX_K8uxCdgTA_hFBo=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CEgQnaQHGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@GrahamStephan",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCV6KDgJskWaEckne5aPA0aQ",
															"canonicalBaseUrl": "/@GrahamStephan"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "Shorts"
																}
															},
															"simpleText": "SHORTS"
														},
														"style": "SHORTS",
														"icon": {
															"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
														}
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CEoQ-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "HraxciwOYKQ",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CEoQ-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "HraxciwOYKQ"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CEoQ-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CEkQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CEkQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "HraxciwOYKQ",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CEkQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"HraxciwOYKQ"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"HraxciwOYKQ"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CEkQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/HraxciwOYKQ/mqdefault_6s.webp?du=3000&sqp=CJTXzrsG&rs=AOn4CLC0DsABpHoOyh1c3FhWsPoCyuiY-Q",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "How To Get Hired By "
															},
															{
																"text": "Elon Musk",
																"bold": true
															},
															{
																"text": " With NO College Degree | Michael Reeves NEW BANKROLL COFFEE NOW FOR SALE:\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": false
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CEgQnaQHGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8yBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=HraxciwOYKQ&pp=YAHIAQHwAQHoBQGiBhUB4_PBEwEaR7fymVjsupz5ND5cyVyQBwI%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "HraxciwOYKQ",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCgjAwubqjozQoWO6AwsIgajrl_TfwdvfAboDCgiLyZ3cnMj8khe6AwsIhJbKv6GS_O-bAboDCgiszta0se-9x1m6AwoItOGtkeb8lpB2ugMKCPHqmdLF5bzvc7oDCwillMP47eLhycsBugMKCIqrj53666GoS7oDCgjR0-m_3MvP-3a6AwoIs5Wfkc6534VO8gMFDSXK9D4%3D",
													"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBEwEaR7fymVjsupz5ND5cyVyQBwI%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr5---sn-5uaeznse.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=1eb6b1722c0e60a4&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgtIcmF4Y2l3T1lLUSDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/ytc/AIdro_m4km0pJvdvRxT_gFN6WS16Ggl9D_eX_K8uxCdgTA_hFBo=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CEgQnaQHGBIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@GrahamStephan",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UCV6KDgJskWaEckne5aPA0aQ",
																		"canonicalBaseUrl": "/@GrahamStephan"
																	}
																}
															}
														}
													}
												}
											}
										}
									},
									{
										"videoRenderer": {
											"videoId": "Tgt9zOInyrM",
											"thumbnail": {
												"thumbnails": [
													{
														"url": "https://i.ytimg.com/vi/Tgt9zOInyrM/hq720.jpg?sqp=-oaymwEnCOgCEMoBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLB9-uT1gi6u-Kz396tuOlxIHbAbwQ",
														"width": 360,
														"height": 202
													},
													{
														"url": "https://i.ytimg.com/vi/Tgt9zOInyrM/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLDDzMKsGo6pQQz5Y9fe-i89hbPNAA",
														"width": 720,
														"height": 404
													}
												]
											},
											"title": {
												"runs": [
													{
														"text": "Elon Musk Revealed NEW Metalic Trick to Make Fully Reusable Heat Shield instead of Ceramic..."
													}
												],
												"accessibility": {
													"accessibilityData": {
														"label": "Elon Musk Revealed NEW Metalic Trick to Make Fully Reusable Heat Shield instead of Ceramic... by ALPHA TECH  53,017 views 23 hours ago 12 minutes, 49 seconds"
													}
												}
											},
											"longBylineText": {
												"runs": [
													{
														"text": "ALPHA TECH ",
														"navigationEndpoint": {
															"clickTrackingParams": "CDYQ3DAYEyITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@alphatech4966",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCO9Vgn2ayVe67fE5tP6JQFA",
																"canonicalBaseUrl": "/@alphatech4966"
															}
														}
													}
												]
											},
											"publishedTimeText": {
												"simpleText": "23 hours ago"
											},
											"lengthText": {
												"accessibility": {
													"accessibilityData": {
														"label": "12 minutes, 49 seconds"
													}
												},
												"simpleText": "12:49"
											},
											"viewCountText": {
												"simpleText": "53,017 views"
											},
											"navigationEndpoint": {
												"clickTrackingParams": "CDYQ3DAYEyITCOiD0ZXm0YoDFRM-rQYd94wHHzIGc2VhcmNoUglFbG9uIE11c2uaAQMQ9CQ=",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=Tgt9zOInyrM&pp=ygUJRWxvbiBNdXNr",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "Tgt9zOInyrM",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCgjAwubqjozQoWO6AwsIgajrl_TfwdvfAboDCgiLyZ3cnMj8khe6AwsIhJbKv6GS_O-bAboDCgiszta0se-9x1m6AwoItOGtkeb8lpB2ugMKCPHqmdLF5bzvc7oDCwillMP47eLhycsBugMKCIqrj53666GoS7oDCgjR0-m_3MvP-3a6AwoIpMG54KKurNse8gMFDSXK9D4%3D",
													"playerParams": "ygUJRWxvbiBNdXNr",
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr2---sn-5uaeznls.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=4e0b7dcce227cab3&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"badges": [
												{
													"metadataBadgeRenderer": {
														"style": "BADGE_STYLE_TYPE_SIMPLE",
														"label": "New",
														"trackingParams": "CDYQ3DAYEyITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
													}
												}
											],
											"ownerText": {
												"runs": [
													{
														"text": "ALPHA TECH ",
														"navigationEndpoint": {
															"clickTrackingParams": "CDYQ3DAYEyITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@alphatech4966",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCO9Vgn2ayVe67fE5tP6JQFA",
																"canonicalBaseUrl": "/@alphatech4966"
															}
														}
													}
												]
											},
											"shortBylineText": {
												"runs": [
													{
														"text": "ALPHA TECH ",
														"navigationEndpoint": {
															"clickTrackingParams": "CDYQ3DAYEyITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
															"commandMetadata": {
																"webCommandMetadata": {
																	"url": "/@alphatech4966",
																	"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																	"rootVe": 3611,
																	"apiUrl": "/youtubei/v1/browse"
																}
															},
															"browseEndpoint": {
																"browseId": "UCO9Vgn2ayVe67fE5tP6JQFA",
																"canonicalBaseUrl": "/@alphatech4966"
															}
														}
													}
												]
											},
											"trackingParams": "CDYQ3DAYEyITCOiD0ZXm0YoDFRM-rQYd94wHH0CzlZ-RzrnfhU4=",
											"showActionMenu": false,
											"shortViewCountText": {
												"accessibility": {
													"accessibilityData": {
														"label": "53K views"
													}
												},
												"simpleText": "53K views"
											},
											"menu": {
												"menuRenderer": {
													"items": [
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Add to queue"
																		}
																	]
																},
																"icon": {
																	"iconType": "ADD_TO_QUEUE_TAIL"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CEcQ_pgEGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true
																		}
																	},
																	"signalServiceEndpoint": {
																		"signal": "CLIENT_SIGNAL",
																		"actions": [
																			{
																				"clickTrackingParams": "CEcQ_pgEGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"addToPlaylistCommand": {
																					"openMiniplayer": true,
																					"videoId": "Tgt9zOInyrM",
																					"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																					"onCreateListCommand": {
																						"clickTrackingParams": "CEcQ_pgEGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																						"commandMetadata": {
																							"webCommandMetadata": {
																								"sendPost": true,
																								"apiUrl": "/youtubei/v1/playlist/create"
																							}
																						},
																						"createPlaylistServiceEndpoint": {
																							"videoIds": [
																								"Tgt9zOInyrM"
																							],
																							"params": "CAQ%3D"
																						}
																					},
																					"videoIds": [
																						"Tgt9zOInyrM"
																					]
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CEcQ_pgEGA8iEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemDownloadRenderer": {
																"serviceEndpoint": {
																	"clickTrackingParams": "CEYQ0aoFGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																	"offlineVideoEndpoint": {
																		"videoId": "Tgt9zOInyrM",
																		"onAddCommand": {
																			"clickTrackingParams": "CEYQ0aoFGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																			"getDownloadActionCommand": {
																				"videoId": "Tgt9zOInyrM",
																				"params": "CAIQAA%3D%3D"
																			}
																		}
																	}
																},
																"trackingParams": "CEYQ0aoFGBAiEwjog9GV5tGKAxUTPq0GHfeMBx8="
															}
														},
														{
															"menuServiceItemRenderer": {
																"text": {
																	"runs": [
																		{
																			"text": "Share"
																		}
																	]
																},
																"icon": {
																	"iconType": "SHARE"
																},
																"serviceEndpoint": {
																	"clickTrackingParams": "CDYQ3DAYEyITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"sendPost": true,
																			"apiUrl": "/youtubei/v1/share/get_share_panel"
																		}
																	},
																	"shareEntityServiceEndpoint": {
																		"serializedShareEntity": "CgtUZ3Q5ek9JbnlyTQ%3D%3D",
																		"commands": [
																			{
																				"clickTrackingParams": "CDYQ3DAYEyITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																				"openPopupAction": {
																					"popup": {
																						"unifiedSharePanelRenderer": {
																							"trackingParams": "CEUQjmIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																							"showLoadingSpinner": true
																						}
																					},
																					"popupType": "DIALOG",
																					"beReused": true
																				}
																			}
																		]
																	}
																},
																"trackingParams": "CDYQ3DAYEyITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"hasSeparator": true
															}
														}
													],
													"trackingParams": "CDYQ3DAYEyITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
													"accessibility": {
														"accessibilityData": {
															"label": "Action menu"
														}
													}
												}
											},
											"channelThumbnailSupportedRenderers": {
												"channelThumbnailWithLinkRenderer": {
													"thumbnail": {
														"thumbnails": [
															{
																"url": "https://yt3.ggpht.com/mtq_JVkDVe1K1QtIxmLfaaduaF6uQ6gHSxEj-DtLA-kKF2ymWi-XxcAKhwBXI1T60GGsYTSl=s68-c-k-c0x00ffffff-no-rj",
																"width": 68,
																"height": 68
															}
														]
													},
													"navigationEndpoint": {
														"clickTrackingParams": "CDYQ3DAYEyITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@alphatech4966",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCO9Vgn2ayVe67fE5tP6JQFA",
															"canonicalBaseUrl": "/@alphatech4966"
														}
													},
													"accessibility": {
														"accessibilityData": {
															"label": "Go to channel"
														}
													}
												}
											},
											"thumbnailOverlays": [
												{
													"thumbnailOverlayTimeStatusRenderer": {
														"text": {
															"accessibility": {
																"accessibilityData": {
																	"label": "12 minutes, 49 seconds"
																}
															},
															"simpleText": "12:49"
														},
														"style": "DEFAULT"
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"isToggled": false,
														"untoggledIcon": {
															"iconType": "WATCH_LATER"
														},
														"toggledIcon": {
															"iconType": "CHECK"
														},
														"untoggledTooltip": "Watch later",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CEQQ-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"addedVideoId": "Tgt9zOInyrM",
																		"action": "ACTION_ADD_VIDEO"
																	}
																]
															}
														},
														"toggledServiceEndpoint": {
															"clickTrackingParams": "CEQQ-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true,
																	"apiUrl": "/youtubei/v1/browse/edit_playlist"
																}
															},
															"playlistEditEndpoint": {
																"playlistId": "WL",
																"actions": [
																	{
																		"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																		"removedVideoId": "Tgt9zOInyrM"
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Watch later"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CEQQ-ecDGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayToggleButtonRenderer": {
														"untoggledIcon": {
															"iconType": "ADD_TO_QUEUE_TAIL"
														},
														"toggledIcon": {
															"iconType": "PLAYLIST_ADD_CHECK"
														},
														"untoggledTooltip": "Add to queue",
														"toggledTooltip": "Added",
														"untoggledServiceEndpoint": {
															"clickTrackingParams": "CEMQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"commandMetadata": {
																"webCommandMetadata": {
																	"sendPost": true
																}
															},
															"signalServiceEndpoint": {
																"signal": "CLIENT_SIGNAL",
																"actions": [
																	{
																		"clickTrackingParams": "CEMQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																		"addToPlaylistCommand": {
																			"openMiniplayer": true,
																			"videoId": "Tgt9zOInyrM",
																			"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																			"onCreateListCommand": {
																				"clickTrackingParams": "CEMQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																				"commandMetadata": {
																					"webCommandMetadata": {
																						"sendPost": true,
																						"apiUrl": "/youtubei/v1/playlist/create"
																					}
																				},
																				"createPlaylistServiceEndpoint": {
																					"videoIds": [
																						"Tgt9zOInyrM"
																					],
																					"params": "CAQ%3D"
																				}
																			},
																			"videoIds": [
																				"Tgt9zOInyrM"
																			]
																		}
																	}
																]
															}
														},
														"untoggledAccessibility": {
															"accessibilityData": {
																"label": "Add to queue"
															}
														},
														"toggledAccessibility": {
															"accessibilityData": {
																"label": "Added"
															}
														},
														"trackingParams": "CEMQx-wEGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8="
													}
												},
												{
													"thumbnailOverlayNowPlayingRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Now playing"
																}
															]
														}
													}
												},
												{
													"thumbnailOverlayLoadingPreviewRenderer": {
														"text": {
															"runs": [
																{
																	"text": "Keep hovering to play"
																}
															]
														}
													}
												}
											],
											"richThumbnail": {
												"movingThumbnailRenderer": {
													"movingThumbnailDetails": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/an_webp/Tgt9zOInyrM/mqdefault_6s.webp?du=3000&sqp=COD5zrsG&rs=AOn4CLDVYfjwP-0XyOeTz20Szy4XxkxeCQ",
																"width": 320,
																"height": 180
															}
														],
														"logAsMovingThumbnail": true
													},
													"enableHoveredLogging": true,
													"enableOverlay": true
												}
											},
											"detailedMetadataSnippets": [
												{
													"snippetText": {
														"runs": [
															{
																"text": "Elon Musk",
																"bold": true
															},
															{
																"text": " Revealed NEW Metalic Trick to Make Fully Reusable Heat Shield instead of Ceramic... === 0:00-0:54: Intro 0:55-2:00:\\u00a0..."
															}
														]
													},
													"snippetHoverText": {
														"runs": [
															{
																"text": "From the video description"
															}
														]
													},
													"maxOneLine": true
												}
											],
											"inlinePlaybackEndpoint": {
												"clickTrackingParams": "CDYQ3DAYEyITCOiD0ZXm0YoDFRM-rQYd94wHHzIGc2VhcmNoUglFbG9uIE11c2uaAQMQ9CQ=",
												"commandMetadata": {
													"webCommandMetadata": {
														"url": "/watch?v=Tgt9zOInyrM&pp=YAHIAQE%3D",
														"webPageType": "WEB_PAGE_TYPE_WATCH",
														"rootVe": 3832
													}
												},
												"watchEndpoint": {
													"videoId": "Tgt9zOInyrM",
													"params": "qgMJRWxvbiBNdXNrugMKCNKhpqXjnpy8M7oDCgiwk5CzmvbJlkG6AwoIpIS17bLk6d9YugMLCJPE5Zv3ncjHsgG6AwoI8pn68OmW448ougMLCLrq46G517nbugG6AwoItMflgr-s_phmugMKCJTxrcPzu-PAAboDCgjAwubqjozQoWO6AwsIgajrl_TfwdvfAboDCgiLyZ3cnMj8khe6AwsIhJbKv6GS_O-bAboDCgiszta0se-9x1m6AwoItOGtkeb8lpB2ugMKCPHqmdLF5bzvc7oDCwillMP47eLhycsBugMKCIqrj53666GoS7oDCgjR0-m_3MvP-3a6AwoIpMG54KKurNse8gMFDSXK9D4%3D",
													"playerParams": "YAHIAQE%3D",
													"playerExtraUrlParams": [
														{
															"key": "inline",
															"value": "1"
														}
													],
													"watchEndpointSupportedOnesieConfig": {
														"html5PlaybackOnesieConfig": {
															"commonConfig": {
																"url": "https://rr2---sn-5uaeznls.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=4e0b7dcce227cab3&ip=*************&mt=1735639559&oweuc="
															}
														}
													}
												}
											},
											"expandableMetadata": {
												"expandableMetadataRenderer": {
													"header": {
														"collapsedTitle": {
															"runs": [
																{
																	"text": "0:54: Intro | 2:00: RED tiles in Case 1 | 2:51: Case 2 | 6:33: Maybe a metal Tiles | 8:14: NASA heat shield | 12:45: Future plan"
																}
															]
														},
														"collapsedThumbnail": {
															"thumbnails": [
																{
																	"url": "https://i.ytimg.com/vi/Tgt9zOInyrM/hqdefault.jpg",
																	"width": 336,
																	"height": 188
																}
															]
														},
														"collapsedLabel": {
															"runs": [
																{
																	"text": "6"
																},
																{
																	"text": " chapters"
																}
															]
														},
														"expandedTitle": {
															"runs": [
																{
																	"text": "6"
																},
																{
																	"text": " chapters in this video"
																}
															]
														},
														"showLeadingCollapsedLabel": true
													},
													"expandedContent": {
														"horizontalCardListRenderer": {
															"cards": [
																{
																	"macroMarkersListItemRenderer": {
																		"title": {
																			"runs": [
																				{
																					"text": "0:54: Intro"
																				}
																			]
																		},
																		"timeDescription": {
																			"runs": [
																				{
																					"text": "0:00"
																				}
																			]
																		},
																		"thumbnail": {
																			"thumbnails": [
																				{
																					"url": "https://i.ytimg.com/vi/Tgt9zOInyrM/hqdefault.jpg",
																					"width": 336,
																					"height": 188
																				}
																			]
																		},
																		"onTap": {
																			"clickTrackingParams": "CEIQ0NAGGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"url": "/watch?v=Tgt9zOInyrM",
																					"webPageType": "WEB_PAGE_TYPE_WATCH",
																					"rootVe": 3832
																				}
																			},
																			"watchEndpoint": {
																				"videoId": "Tgt9zOInyrM",
																				"watchEndpointSupportedOnesieConfig": {
																					"html5PlaybackOnesieConfig": {
																						"commonConfig": {
																							"url": "https://rr2---sn-5uaeznls.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=4e0b7dcce227cab3&ip=*************&mt=1735639559&oweuc="
																						}
																					}
																				}
																			}
																		},
																		"trackingParams": "CEIQ0NAGGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																		"layout": "MACRO_MARKERS_LIST_ITEM_RENDERER_LAYOUT_VERTICAL",
																		"isHighlighted": false
																	}
																},
																{
																	"macroMarkersListItemRenderer": {
																		"title": {
																			"runs": [
																				{
																					"text": "2:00: RED tiles in Case 1"
																				}
																			]
																		},
																		"timeDescription": {
																			"runs": [
																				{
																					"text": "0:55"
																				}
																			]
																		},
																		"thumbnail": {
																			"thumbnails": [
																				{
																					"url": "https://i.ytimg.com/vi/Tgt9zOInyrM/hqdefault.jpg",
																					"width": 336,
																					"height": 188
																				}
																			]
																		},
																		"onTap": {
																			"clickTrackingParams": "CEEQ0NAGGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"url": "/watch?v=Tgt9zOInyrM&t=55s",
																					"webPageType": "WEB_PAGE_TYPE_WATCH",
																					"rootVe": 3832
																				}
																			},
																			"watchEndpoint": {
																				"videoId": "Tgt9zOInyrM",
																				"startTimeSeconds": 55,
																				"watchEndpointSupportedOnesieConfig": {
																					"html5PlaybackOnesieConfig": {
																						"commonConfig": {
																							"url": "https://rr2---sn-5uaeznls.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=4e0b7dcce227cab3&ip=*************&osts=55&mt=1735639559&oweuc="
																						}
																					}
																				}
																			}
																		},
																		"trackingParams": "CEEQ0NAGGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																		"layout": "MACRO_MARKERS_LIST_ITEM_RENDERER_LAYOUT_VERTICAL",
																		"isHighlighted": false
																	}
																},
																{
																	"macroMarkersListItemRenderer": {
																		"title": {
																			"runs": [
																				{
																					"text": "2:51: Case 2"
																				}
																			]
																		},
																		"timeDescription": {
																			"runs": [
																				{
																					"text": "2:01"
																				}
																			]
																		},
																		"thumbnail": {
																			"thumbnails": [
																				{
																					"url": "https://i.ytimg.com/vi/Tgt9zOInyrM/hqdefault.jpg",
																					"width": 336,
																					"height": 188
																				}
																			]
																		},
																		"onTap": {
																			"clickTrackingParams": "CEAQ0NAGGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"url": "/watch?v=Tgt9zOInyrM&t=121s",
																					"webPageType": "WEB_PAGE_TYPE_WATCH",
																					"rootVe": 3832
																				}
																			},
																			"watchEndpoint": {
																				"videoId": "Tgt9zOInyrM",
																				"startTimeSeconds": 121,
																				"watchEndpointSupportedOnesieConfig": {
																					"html5PlaybackOnesieConfig": {
																						"commonConfig": {
																							"url": "https://rr2---sn-5uaeznls.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=4e0b7dcce227cab3&ip=*************&osts=121&mt=1735639559&oweuc="
																						}
																					}
																				}
																			}
																		},
																		"trackingParams": "CEAQ0NAGGAIiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																		"layout": "MACRO_MARKERS_LIST_ITEM_RENDERER_LAYOUT_VERTICAL",
																		"isHighlighted": false
																	}
																},
																{
																	"macroMarkersListItemRenderer": {
																		"title": {
																			"runs": [
																				{
																					"text": "6:33: Maybe a metal Tiles"
																				}
																			]
																		},
																		"timeDescription": {
																			"runs": [
																				{
																					"text": "2:52"
																				}
																			]
																		},
																		"thumbnail": {
																			"thumbnails": [
																				{
																					"url": "https://i.ytimg.com/vi/Tgt9zOInyrM/hqdefault.jpg",
																					"width": 336,
																					"height": 188
																				}
																			]
																		},
																		"onTap": {
																			"clickTrackingParams": "CD8Q0NAGGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"url": "/watch?v=Tgt9zOInyrM&t=172s",
																					"webPageType": "WEB_PAGE_TYPE_WATCH",
																					"rootVe": 3832
																				}
																			},
																			"watchEndpoint": {
																				"videoId": "Tgt9zOInyrM",
																				"startTimeSeconds": 172,
																				"watchEndpointSupportedOnesieConfig": {
																					"html5PlaybackOnesieConfig": {
																						"commonConfig": {
																							"url": "https://rr2---sn-5uaeznls.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=4e0b7dcce227cab3&ip=*************&osts=172&mt=1735639559&oweuc="
																						}
																					}
																				}
																			}
																		},
																		"trackingParams": "CD8Q0NAGGAMiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																		"layout": "MACRO_MARKERS_LIST_ITEM_RENDERER_LAYOUT_VERTICAL",
																		"isHighlighted": false
																	}
																},
																{
																	"macroMarkersListItemRenderer": {
																		"title": {
																			"runs": [
																				{
																					"text": "8:14: NASA heat shield"
																				}
																			]
																		},
																		"timeDescription": {
																			"runs": [
																				{
																					"text": "6:34"
																				}
																			]
																		},
																		"thumbnail": {
																			"thumbnails": [
																				{
																					"url": "https://i.ytimg.com/vi/Tgt9zOInyrM/hqdefault.jpg",
																					"width": 336,
																					"height": 188
																				}
																			]
																		},
																		"onTap": {
																			"clickTrackingParams": "CD4Q0NAGGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"url": "/watch?v=Tgt9zOInyrM&t=394s",
																					"webPageType": "WEB_PAGE_TYPE_WATCH",
																					"rootVe": 3832
																				}
																			},
																			"watchEndpoint": {
																				"videoId": "Tgt9zOInyrM",
																				"startTimeSeconds": 394,
																				"watchEndpointSupportedOnesieConfig": {
																					"html5PlaybackOnesieConfig": {
																						"commonConfig": {
																							"url": "https://rr2---sn-5uaeznls.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=4e0b7dcce227cab3&ip=*************&osts=394&mt=1735639559&oweuc="
																						}
																					}
																				}
																			}
																		},
																		"trackingParams": "CD4Q0NAGGAQiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																		"layout": "MACRO_MARKERS_LIST_ITEM_RENDERER_LAYOUT_VERTICAL",
																		"isHighlighted": false
																	}
																},
																{
																	"macroMarkersListItemRenderer": {
																		"title": {
																			"runs": [
																				{
																					"text": "12:45: Future plan"
																				}
																			]
																		},
																		"timeDescription": {
																			"runs": [
																				{
																					"text": "8:15"
																				}
																			]
																		},
																		"thumbnail": {
																			"thumbnails": [
																				{
																					"url": "https://i.ytimg.com/vi/Tgt9zOInyrM/hqdefault.jpg",
																					"width": 336,
																					"height": 188
																				}
																			]
																		},
																		"onTap": {
																			"clickTrackingParams": "CD0Q0NAGGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"url": "/watch?v=Tgt9zOInyrM&t=495s",
																					"webPageType": "WEB_PAGE_TYPE_WATCH",
																					"rootVe": 3832
																				}
																			},
																			"watchEndpoint": {
																				"videoId": "Tgt9zOInyrM",
																				"startTimeSeconds": 495,
																				"watchEndpointSupportedOnesieConfig": {
																					"html5PlaybackOnesieConfig": {
																						"commonConfig": {
																							"url": "https://rr2---sn-5uaeznls.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=4e0b7dcce227cab3&ip=*************&osts=495&mt=1735639559&oweuc="
																						}
																					}
																				}
																			}
																		},
																		"trackingParams": "CD0Q0NAGGAUiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
																		"layout": "MACRO_MARKERS_LIST_ITEM_RENDERER_LAYOUT_VERTICAL",
																		"isHighlighted": false
																	}
																}
															],
															"trackingParams": "CDoQkVoiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"style": {
																"type": "HORIZONTAL_CARD_LIST_STYLE_TYPE_ENGAGEMENT_PANEL_SECTION"
															},
															"previousButton": {
																"buttonRenderer": {
																	"style": "STYLE_DEFAULT",
																	"size": "SIZE_DEFAULT",
																	"isDisabled": false,
																	"icon": {
																		"iconType": "CHEVRON_LEFT"
																	},
																	"trackingParams": "CDwQ8FsiEwjog9GV5tGKAxUTPq0GHfeMBx8="
																}
															},
															"nextButton": {
																"buttonRenderer": {
																	"style": "STYLE_DEFAULT",
																	"size": "SIZE_DEFAULT",
																	"isDisabled": false,
																	"icon": {
																		"iconType": "CHEVRON_RIGHT"
																	},
																	"trackingParams": "CDsQ8FsiEwjog9GV5tGKAxUTPq0GHfeMBx8="
																}
															}
														}
													},
													"expandButton": {
														"buttonRenderer": {
															"style": "STYLE_DEFAULT",
															"size": "SIZE_DEFAULT",
															"isDisabled": false,
															"icon": {
																"iconType": "EXPAND_MORE"
															},
															"trackingParams": "CDkQ8FsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"accessibilityData": {
																"accessibilityData": {
																	"label": "More"
																}
															}
														}
													},
													"collapseButton": {
														"buttonRenderer": {
															"style": "STYLE_DEFAULT",
															"size": "SIZE_DEFAULT",
															"isDisabled": false,
															"icon": {
																"iconType": "EXPAND_LESS"
															},
															"trackingParams": "CDgQ8FsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
															"accessibilityData": {
																"accessibilityData": {
																	"label": "Less"
																}
															}
														}
													},
													"trackingParams": "CDcQ78MHIhMI6IPRlebRigMVEz6tBh33jAcf",
													"colorData": {
														"lightColorPalette": {
															"section1Color": 4284832088,
															"section2Color": 4294504945,
															"section3Color": 4294109415,
															"primaryTitleColor": 4279834134,
															"secondaryTitleColor": 4285226589,
															"iconActivatedColor": 4284042829,
															"iconInactiveColor": 4290092957,
															"section4Color": 4293648093,
															"iconDisabledColor": 4074818505
														},
														"darkColorPalette": {
															"section1Color": 4282333239,
															"section2Color": 4281543724,
															"section3Color": 4280688929,
															"primaryTitleColor": 4294961125,
															"secondaryTitleColor": 4291605425,
															"iconActivatedColor": 4294961125,
															"iconInactiveColor": 4287396730,
															"section4Color": 4279834134,
															"iconDisabledColor": 4065412935
														},
														"vibrantColorPalette": {
															"section1Color": 4284832088,
															"section2Color": 4284042829,
															"section3Color": 4283188034,
															"primaryTitleColor": 4294961125,
															"secondaryTitleColor": 4293907409,
															"iconActivatedColor": 4294961125,
															"iconInactiveColor": 4288251525,
															"section4Color": 4282333239,
															"iconDisabledColor": 4065084226
														}
													},
													"useCustomColors": true,
													"loggingDirectives": {
														"trackingParams": "CDcQ78MHIhMI6IPRlebRigMVEz6tBh33jAcf",
														"visibility": {
															"types": "12"
														}
													}
												}
											},
											"searchVideoResultEntityKey": "EgtUZ3Q5ek9JbnlyTSDnAigB",
											"avatar": {
												"decoratedAvatarViewModel": {
													"avatar": {
														"avatarViewModel": {
															"image": {
																"sources": [
																	{
																		"url": "https://yt3.ggpht.com/mtq_JVkDVe1K1QtIxmLfaaduaF6uQ6gHSxEj-DtLA-kKF2ymWi-XxcAKhwBXI1T60GGsYTSl=s68-c-k-c0x00ffffff-no-rj",
																		"width": 68,
																		"height": 68
																	}
																]
															},
															"avatarImageSize": "AVATAR_SIZE_M"
														}
													},
													"a11yLabel": "Go to channel",
													"rendererContext": {
														"commandContext": {
															"onTap": {
																"innertubeCommand": {
																	"clickTrackingParams": "CDYQ3DAYEyITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																	"commandMetadata": {
																		"webCommandMetadata": {
																			"url": "/@alphatech4966",
																			"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																			"rootVe": 3611,
																			"apiUrl": "/youtubei/v1/browse"
																		}
																	},
																	"browseEndpoint": {
																		"browseId": "UCO9Vgn2ayVe67fE5tP6JQFA",
																		"canonicalBaseUrl": "/@alphatech4966"
																	}
																}
															}
														}
													}
												}
											}
										}
									}
								],
								"trackingParams": "CDUQuy8YACITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
							}
						},
						{
							"continuationItemRenderer": {
								"trigger": "CONTINUATION_TRIGGER_ON_ITEM_SHOWN",
								"continuationEndpoint": {
									"clickTrackingParams": "CDQQt6kLGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
									"commandMetadata": {
										"webCommandMetadata": {
											"sendPost": true,
											"apiUrl": "/youtubei/v1/search"
										}
									},
									"continuationCommand": {
										"token": "EpoDEglFbG9uIE11c2sajANDQUFTQWhBQlNCU0NBUXROTTJoM09XcFRjR3RPU1lJQkMxRlRNRzV6WVZwclEySkJnZ0VMVjB3dGJrbDVNblJSYVZHQ0FRdHpiemhuTnpOT05WbG9UWUlCQzB0Q0xVMTBjRFJsYWxCSmdnRUxkWEppYlhVMVVUUTVWRy1DQVF0YWFrZzFXVjlDV2xrM1VZSUJDMEZaUjA0emVtaHlaVXBSZ2dFTFdUQk9RVmxQTVZwdlZVR0NBUXN6TjJOSFh6Qk1OakZCUllJQkMwWjVXSGxSWTNWSVdrbHpnZ0VMYlRsZmQydG9abmxwZDFHQ0FRdFhXVGN6WlhoaFZuQjVkNElCQzJScFFtSTFiVWx5WTB4UmdnRUxZemszZWt4R2NFZGtXRVdDQVF0NU5VOUlSblE0VVhscFZZSUJDMU14UTBoWU5rOXFNVmx2Z2dFTFpIWmpMVmhqWmpaaFpFV0NBUXRJY21GNFkybDNUMWxMVVlJQkMxUm5kRGw2VDBsdWVYSk5zZ0VHQ2dRSUZCQUMYgeDoGCILc2VhcmNoLWZlZWQ%3D",
										"request": "CONTINUATION_REQUEST_TYPE_SEARCH"
									}
								},
								"loggingDirectives": {
									"trackingParams": "CDQQt6kLGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8="
								}
							}
						}
					],
					"trackingParams": "CDIQui8iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
					"subMenu": {
						"searchSubMenuRenderer": {
							"trackingParams": "CDMQkXUiEwjog9GV5tGKAxUTPq0GHfeMBx8="
						}
					},
					"hideBottomSeparator": true,
					"targetId": "search-feed"
				}
			}
		}
	},
	"trackingParams": "CAAQvGkiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
	"header": {
		"searchHeaderRenderer": {
			"searchFilterButton": {
				"buttonRenderer": {
					"style": "STYLE_TEXT",
					"size": "SIZE_DEFAULT",
					"isDisabled": false,
					"text": {
						"runs": [
							{
								"text": "Filters"
							}
						]
					},
					"icon": {
						"iconType": "TUNE"
					},
					"tooltip": "Search filters",
					"trackingParams": "CBEQ8FsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
					"accessibilityData": {
						"accessibilityData": {
							"label": "Search filters"
						}
					},
					"command": {
						"clickTrackingParams": "CBEQ8FsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
						"openPopupAction": {
							"popup": {
								"searchFilterOptionsDialogRenderer": {
									"title": {
										"runs": [
											{
												"text": "Search filters"
											}
										]
									},
									"groups": [
										{
											"searchFilterGroupRenderer": {
												"title": {
													"simpleText": "Upload date"
												},
												"filters": [
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Last hour"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CDEQk3UYACITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQIARAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQIARAB"
																}
															},
															"tooltip": "Search for Last hour",
															"trackingParams": "CDEQk3UYACITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Today"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CDAQk3UYASITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQIAhAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQIAhAB"
																}
															},
															"tooltip": "Search for Today",
															"trackingParams": "CDAQk3UYASITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "This week"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CC8Qk3UYAiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQIAxAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQIAxAB"
																}
															},
															"tooltip": "Search for This week",
															"trackingParams": "CC8Qk3UYAiITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "This month"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CC4Qk3UYAyITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQIBBAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQIBBAB"
																}
															},
															"tooltip": "Search for This month",
															"trackingParams": "CC4Qk3UYAyITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "This year"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CC0Qk3UYBCITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQIBRAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQIBRAB"
																}
															},
															"tooltip": "Search for This year",
															"trackingParams": "CC0Qk3UYBCITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													}
												],
												"trackingParams": "CCwQknUYACITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
											}
										},
										{
											"searchFilterGroupRenderer": {
												"title": {
													"simpleText": "Type"
												},
												"filters": [
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Video"
															},
															"status": "FILTER_STATUS_SELECTED",
															"navigationEndpoint": {
																"clickTrackingParams": "CCsQk3UYACITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk"
																}
															},
															"tooltip": "Remove Video filter",
															"trackingParams": "CCsQk3UYACITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Channel"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CCoQk3UYASITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgIQAg%253D%253D",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgIQAg%3D%3D"
																}
															},
															"tooltip": "Search for Channel",
															"trackingParams": "CCoQk3UYASITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Playlist"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CCkQk3UYAiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgIQAw%253D%253D",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgIQAw%3D%3D"
																}
															},
															"tooltip": "Search for Playlist",
															"trackingParams": "CCkQk3UYAiITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Movie"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CCgQk3UYAyITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgIQBA%253D%253D",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgIQBA%3D%3D"
																}
															},
															"tooltip": "Search for Movie",
															"trackingParams": "CCgQk3UYAyITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													}
												],
												"trackingParams": "CCcQknUYASITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
											}
										},
										{
											"searchFilterGroupRenderer": {
												"title": {
													"simpleText": "Duration"
												},
												"filters": [
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Under 4 minutes"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CCYQk3UYACITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQARgB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQARgB"
																}
															},
															"tooltip": "Search for Under 4 minutes",
															"trackingParams": "CCYQk3UYACITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "4 - 20 minutes"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CCUQk3UYASITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQARgD",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQARgD"
																}
															},
															"tooltip": "Search for 4 - 20 minutes",
															"trackingParams": "CCUQk3UYASITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Over 20 minutes"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CCQQk3UYAiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQARgC",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQARgC"
																}
															},
															"tooltip": "Search for Over 20 minutes",
															"trackingParams": "CCQQk3UYAiITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													}
												],
												"trackingParams": "CCMQknUYAiITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
											}
										},
										{
											"searchFilterGroupRenderer": {
												"title": {
													"simpleText": "Features"
												},
												"filters": [
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Live"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CCIQk3UYACITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQAUAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQAUAB"
																}
															},
															"tooltip": "Search for Live",
															"trackingParams": "CCIQk3UYACITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "4K"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CCEQk3UYASITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQAXAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQAXAB"
																}
															},
															"tooltip": "Search for 4K",
															"trackingParams": "CCEQk3UYASITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "HD"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CCAQk3UYAiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQASAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQASAB"
																}
															},
															"tooltip": "Search for HD",
															"trackingParams": "CCAQk3UYAiITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Subtitles/CC"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CB8Qk3UYAyITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQASgB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQASgB"
																}
															},
															"tooltip": "Search for Subtitles/CC",
															"trackingParams": "CB8Qk3UYAyITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Creative Commons"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CB4Qk3UYBCITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQATAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQATAB"
																}
															},
															"tooltip": "Search for Creative Commons",
															"trackingParams": "CB4Qk3UYBCITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "360\\u00b0"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CB0Qk3UYBSITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQAXgB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQAXgB"
																}
															},
															"tooltip": "Search for 360\\u00b0",
															"trackingParams": "CB0Qk3UYBSITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "VR180"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CBwQk3UYBiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgUQAdABAQ%253D%253D",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgUQAdABAQ%3D%3D"
																}
															},
															"tooltip": "Search for VR180",
															"trackingParams": "CBwQk3UYBiITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "3D"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CBsQk3UYByITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQATgB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQATgB"
																}
															},
															"tooltip": "Search for 3D",
															"trackingParams": "CBsQk3UYByITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "HDR"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CBoQk3UYCCITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgUQAcgBAQ%253D%253D",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgUQAcgBAQ%3D%3D"
																}
															},
															"tooltip": "Search for HDR",
															"trackingParams": "CBoQk3UYCCITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Location"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CBkQk3UYCSITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgUQAbgBAQ%253D%253D",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgUQAbgBAQ%3D%3D"
																}
															},
															"tooltip": "Search for Location",
															"trackingParams": "CBkQk3UYCSITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Purchased"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CBgQk3UYCiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQAUgB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQAUgB"
																}
															},
															"tooltip": "Search for Purchased",
															"trackingParams": "CBgQk3UYCiITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													}
												],
												"trackingParams": "CBcQknUYAyITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
											}
										},
										{
											"searchFilterGroupRenderer": {
												"title": {
													"simpleText": "Sort by"
												},
												"filters": [
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Relevance"
															},
															"status": "FILTER_STATUS_SELECTED",
															"tooltip": "Sort by relevance",
															"trackingParams": "CBYQk3UYACITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Upload date"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CBUQk3UYASITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=CAISAhAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "CAISAhAB"
																}
															},
															"tooltip": "Sort by upload date",
															"trackingParams": "CBUQk3UYASITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "View count"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CBQQk3UYAiITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=CAMSAhAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "CAMSAhAB"
																}
															},
															"tooltip": "Sort by view count",
															"trackingParams": "CBQQk3UYAiITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Rating"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CBMQk3UYAyITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=CAESAhAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "CAESAhAB"
																}
															},
															"tooltip": "Sort by rating",
															"trackingParams": "CBMQk3UYAyITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
														}
													}
												],
												"trackingParams": "CBIQknUYBCITCOiD0ZXm0YoDFRM-rQYd94wHHw=="
											}
										}
									]
								}
							},
							"popupType": "DIALOG"
						}
					},
					"iconPosition": "BUTTON_ICON_POSITION_TYPE_RIGHT_OF_TEXT"
				}
			},
			"trackingParams": "CBAQiOoKIhMI6IPRlebRigMVEz6tBh33jAcf"
		}
	},
	"topbar": {
		"desktopTopbarRenderer": {
			"logo": {
				"topbarLogoRenderer": {
					"iconImage": {
						"iconType": "YOUTUBE_LOGO"
					},
					"tooltipText": {
						"runs": [
							{
								"text": "YouTube Home"
							}
						]
					},
					"endpoint": {
						"clickTrackingParams": "CA8QsV4iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
						"commandMetadata": {
							"webCommandMetadata": {
								"url": "/",
								"webPageType": "WEB_PAGE_TYPE_BROWSE",
								"rootVe": 3854,
								"apiUrl": "/youtubei/v1/browse"
							}
						},
						"browseEndpoint": {
							"browseId": "FEwhat_to_watch"
						}
					},
					"trackingParams": "CA8QsV4iEwjog9GV5tGKAxUTPq0GHfeMBx8=",
					"overrideEntityKey": "EgZ0b3BiYXIg9QEoAQ%3D%3D"
				}
			},
			"searchbox": {
				"fusionSearchboxRenderer": {
					"icon": {
						"iconType": "SEARCH"
					},
					"placeholderText": {
						"runs": [
							{
								"text": "Search"
							}
						]
					},
					"config": {
						"webSearchboxConfig": {
							"requestLanguage": "en",
							"requestDomain": "us",
							"hasOnscreenKeyboard": false,
							"focusSearchbox": true
						}
					},
					"trackingParams": "CA0Q7VAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
					"searchEndpoint": {
						"clickTrackingParams": "CA0Q7VAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
						"commandMetadata": {
							"webCommandMetadata": {
								"url": "/results?search_query=",
								"webPageType": "WEB_PAGE_TYPE_SEARCH",
								"rootVe": 4724
							}
						},
						"searchEndpoint": {
							"query": ""
						}
					},
					"clearButton": {
						"buttonRenderer": {
							"style": "STYLE_DEFAULT",
							"size": "SIZE_DEFAULT",
							"isDisabled": false,
							"icon": {
								"iconType": "CLOSE"
							},
							"trackingParams": "CA4Q8FsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
							"accessibilityData": {
								"accessibilityData": {
									"label": "Clear search query"
								}
							}
						}
					}
				}
			},
			"trackingParams": "CAEQq6wBIhMI6IPRlebRigMVEz6tBh33jAcf",
			"topbarButtons": [
				{
					"topbarMenuButtonRenderer": {
						"icon": {
							"iconType": "MORE_VERT"
						},
						"menuRequest": {
							"clickTrackingParams": "CAsQ_qsBGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
							"commandMetadata": {
								"webCommandMetadata": {
									"sendPost": true,
									"apiUrl": "/youtubei/v1/account/account_menu"
								}
							},
							"signalServiceEndpoint": {
								"signal": "GET_ACCOUNT_MENU",
								"actions": [
									{
										"clickTrackingParams": "CAsQ_qsBGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
										"openPopupAction": {
											"popup": {
												"multiPageMenuRenderer": {
													"trackingParams": "CAwQ_6sBIhMI6IPRlebRigMVEz6tBh33jAcf",
													"style": "MULTI_PAGE_MENU_STYLE_TYPE_SYSTEM",
													"showLoadingSpinner": true
												}
											},
											"popupType": "DROPDOWN",
											"beReused": true
										}
									}
								]
							}
						},
						"trackingParams": "CAsQ_qsBGAAiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
						"accessibility": {
							"accessibilityData": {
								"label": "Settings"
							}
						},
						"tooltip": "Settings",
						"style": "STYLE_DEFAULT"
					}
				},
				{
					"buttonRenderer": {
						"style": "STYLE_SUGGESTIVE",
						"size": "SIZE_SMALL",
						"text": {
							"runs": [
								{
									"text": "Sign in"
								}
							]
						},
						"icon": {
							"iconType": "AVATAR_LOGGED_OUT"
						},
						"navigationEndpoint": {
							"clickTrackingParams": "CAoQ1IAEGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
							"commandMetadata": {
								"webCommandMetadata": {
									"url": "https://accounts.google.com/ServiceLogin?service=youtube&uilel=3&passive=true&continue=https%3A%2F%2Fwww.youtube.com%2Fsignin%3Faction_handle_signin%3Dtrue%26app%3Ddesktop%26hl%3Den%26next%3Dhttps%253A%252F%252Fwww.youtube.com%252Fresults%253Fsearch_query%253DElon%252BMusk%2526sp%253DCAASAhAB&hl=en&ec=65620",
									"webPageType": "WEB_PAGE_TYPE_UNKNOWN",
									"rootVe": 83769
								}
							},
							"signInEndpoint": {
								"idamTag": "65620"
							}
						},
						"trackingParams": "CAoQ1IAEGAEiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
						"targetId": "topbar-signin"
					}
				}
			],
			"hotkeyDialog": {
				"hotkeyDialogRenderer": {
					"title": {
						"runs": [
							{
								"text": "Keyboard shortcuts"
							}
						]
					},
					"sections": [
						{
							"hotkeyDialogSectionRenderer": {
								"title": {
									"runs": [
										{
											"text": "Playback"
										}
									]
								},
								"options": [
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Toggle play/pause"
													}
												]
											},
											"hotkey": "k"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Rewind 10 seconds"
													}
												]
											},
											"hotkey": "j"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Fast forward 10 seconds"
													}
												]
											},
											"hotkey": "l"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Previous video"
													}
												]
											},
											"hotkey": "P (SHIFT+p)"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Next video"
													}
												]
											},
											"hotkey": "N (SHIFT+n)"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Previous frame (while paused)"
													}
												]
											},
											"hotkey": ",",
											"hotkeyAccessibilityLabel": {
												"accessibilityData": {
													"label": "Comma"
												}
											}
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Next frame (while paused)"
													}
												]
											},
											"hotkey": ".",
											"hotkeyAccessibilityLabel": {
												"accessibilityData": {
													"label": "Period"
												}
											}
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Decrease playback rate"
													}
												]
											},
											"hotkey": "< (SHIFT+,)",
											"hotkeyAccessibilityLabel": {
												"accessibilityData": {
													"label": "Less than or SHIFT + comma"
												}
											}
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Increase playback rate"
													}
												]
											},
											"hotkey": "> (SHIFT+.)",
											"hotkeyAccessibilityLabel": {
												"accessibilityData": {
													"label": "Greater than or SHIFT + period"
												}
											}
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Seek to specific point in the video (7 advances to 70% of duration)"
													}
												]
											},
											"hotkey": "0..9"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Seek to previous chapter"
													}
												]
											},
											"hotkey": "OPTION + \\u2190"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Seek to next chapter"
													}
												]
											},
											"hotkey": "OPTION + \\u2192"
										}
									}
								]
							}
						},
						{
							"hotkeyDialogSectionRenderer": {
								"title": {
									"runs": [
										{
											"text": "General"
										}
									]
								},
								"options": [
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Toggle full screen"
													}
												]
											},
											"hotkey": "f"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Toggle theater mode"
													}
												]
											},
											"hotkey": "t"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Toggle miniplayer"
													}
												]
											},
											"hotkey": "i"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Close miniplayer or current dialog"
													}
												]
											},
											"hotkey": "ESCAPE"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Toggle mute"
													}
												]
											},
											"hotkey": "m"
										}
									}
								]
							}
						},
						{
							"hotkeyDialogSectionRenderer": {
								"title": {
									"runs": [
										{
											"text": "Subtitles and closed captions"
										}
									]
								},
								"options": [
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "If the video supports captions, toggle captions ON/OFF"
													}
												]
											},
											"hotkey": "c"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Rotate through different text opacity levels"
													}
												]
											},
											"hotkey": "o"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Rotate through different window opacity levels"
													}
												]
											},
											"hotkey": "w"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Rotate through font sizes (increasing)"
													}
												]
											},
											"hotkey": "+"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Rotate through font sizes (decreasing)"
													}
												]
											},
											"hotkey": "-",
											"hotkeyAccessibilityLabel": {
												"accessibilityData": {
													"label": "Minus"
												}
											}
										}
									}
								]
							}
						},
						{
							"hotkeyDialogSectionRenderer": {
								"title": {
									"runs": [
										{
											"text": "Spherical Videos"
										}
									]
								},
								"options": [
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Pan up"
													}
												]
											},
											"hotkey": "w"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Pan left"
													}
												]
											},
											"hotkey": "a"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Pan down"
													}
												]
											},
											"hotkey": "s"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Pan right"
													}
												]
											},
											"hotkey": "d"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Zoom in"
													}
												]
											},
											"hotkey": "+ on numpad or ]",
											"hotkeyAccessibilityLabel": {
												"accessibilityData": {
													"label": "Plus on number pad or right bracket"
												}
											}
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Zoom out"
													}
												]
											},
											"hotkey": "- on numpad or [",
											"hotkeyAccessibilityLabel": {
												"accessibilityData": {
													"label": "Minus on number pad or left bracket"
												}
											}
										}
									}
								]
							}
						}
					],
					"dismissButton": {
						"buttonRenderer": {
							"style": "STYLE_BLUE_TEXT",
							"size": "SIZE_DEFAULT",
							"isDisabled": false,
							"text": {
								"runs": [
									{
										"text": "Dismiss"
									}
								]
							},
							"trackingParams": "CAkQ8FsiEwjog9GV5tGKAxUTPq0GHfeMBx8="
						}
					},
					"trackingParams": "CAgQteYDIhMI6IPRlebRigMVEz6tBh33jAcf"
				}
			},
			"backButton": {
				"buttonRenderer": {
					"trackingParams": "CAcQvIYDIhMI6IPRlebRigMVEz6tBh33jAcf",
					"command": {
						"clickTrackingParams": "CAcQvIYDIhMI6IPRlebRigMVEz6tBh33jAcf",
						"commandMetadata": {
							"webCommandMetadata": {
								"sendPost": true
							}
						},
						"signalServiceEndpoint": {
							"signal": "CLIENT_SIGNAL",
							"actions": [
								{
									"clickTrackingParams": "CAcQvIYDIhMI6IPRlebRigMVEz6tBh33jAcf",
									"signalAction": {
										"signal": "HISTORY_BACK"
									}
								}
							]
						}
					}
				}
			},
			"forwardButton": {
				"buttonRenderer": {
					"trackingParams": "CAYQvYYDIhMI6IPRlebRigMVEz6tBh33jAcf",
					"command": {
						"clickTrackingParams": "CAYQvYYDIhMI6IPRlebRigMVEz6tBh33jAcf",
						"commandMetadata": {
							"webCommandMetadata": {
								"sendPost": true
							}
						},
						"signalServiceEndpoint": {
							"signal": "CLIENT_SIGNAL",
							"actions": [
								{
									"clickTrackingParams": "CAYQvYYDIhMI6IPRlebRigMVEz6tBh33jAcf",
									"signalAction": {
										"signal": "HISTORY_FORWARD"
									}
								}
							]
						}
					}
				}
			},
			"a11ySkipNavigationButton": {
				"buttonRenderer": {
					"style": "STYLE_DEFAULT",
					"size": "SIZE_DEFAULT",
					"isDisabled": false,
					"text": {
						"runs": [
							{
								"text": "Skip navigation"
							}
						]
					},
					"trackingParams": "CAUQ8FsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
					"command": {
						"clickTrackingParams": "CAUQ8FsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
						"commandMetadata": {
							"webCommandMetadata": {
								"sendPost": true
							}
						},
						"signalServiceEndpoint": {
							"signal": "CLIENT_SIGNAL",
							"actions": [
								{
									"clickTrackingParams": "CAUQ8FsiEwjog9GV5tGKAxUTPq0GHfeMBx8=",
									"signalAction": {
										"signal": "SKIP_NAVIGATION"
									}
								}
							]
						}
					}
				}
			},
			"voiceSearchButton": {
				"buttonRenderer": {
					"style": "STYLE_DEFAULT",
					"size": "SIZE_DEFAULT",
					"isDisabled": false,
					"serviceEndpoint": {
						"clickTrackingParams": "CAIQ7a8FIhMI6IPRlebRigMVEz6tBh33jAcf",
						"commandMetadata": {
							"webCommandMetadata": {
								"sendPost": true
							}
						},
						"signalServiceEndpoint": {
							"signal": "CLIENT_SIGNAL",
							"actions": [
								{
									"clickTrackingParams": "CAIQ7a8FIhMI6IPRlebRigMVEz6tBh33jAcf",
									"openPopupAction": {
										"popup": {
											"voiceSearchDialogRenderer": {
												"placeholderHeader": {
													"runs": [
														{
															"text": "Listening..."
														}
													]
												},
												"promptHeader": {
													"runs": [
														{
															"text": "Didn\'t hear that. Try again."
														}
													]
												},
												"exampleQuery1": {
													"runs": [
														{
															"text": "\\"
															Play
															Dua
															Lipa
															\
															\
															""
														}
													]
												},
												"exampleQuery2": {
													"runs": [
														{
															"text": "\\"
															Show
															me
															my
															subscriptions
															\
															\
															""
														}
													]
												},
												"promptMicrophoneLabel": {
													"runs": [
														{
															"text": "Tap microphone to try again"
														}
													]
												},
												"loadingHeader": {
													"runs": [
														{
															"text": "Working..."
														}
													]
												},
												"connectionErrorHeader": {
													"runs": [
														{
															"text": "No connection"
														}
													]
												},
												"connectionErrorMicrophoneLabel": {
													"runs": [
														{
															"text": "Check your connection and try again"
														}
													]
												},
												"permissionsHeader": {
													"runs": [
														{
															"text": "Waiting for permission"
														}
													]
												},
												"permissionsSubtext": {
													"runs": [
														{
															"text": "Allow microphone access to search with voice"
														}
													]
												},
												"disabledHeader": {
													"runs": [
														{
															"text": "Search with your voice"
														}
													]
												},
												"disabledSubtext": {
													"runs": [
														{
															"text": "To search by voice, go to your browser settings and allow access to microphone"
														}
													]
												},
												"microphoneButtonAriaLabel": {
													"runs": [
														{
															"text": "Cancel"
														}
													]
												},
												"exitButton": {
													"buttonRenderer": {
														"style": "STYLE_DEFAULT",
														"size": "SIZE_DEFAULT",
														"isDisabled": false,
														"icon": {
															"iconType": "CLOSE"
														},
														"trackingParams": "CAQQ0LEFIhMI6IPRlebRigMVEz6tBh33jAcf",
														"accessibilityData": {
															"accessibilityData": {
																"label": "Cancel"
															}
														}
													}
												},
												"trackingParams": "CAMQ7q8FIhMI6IPRlebRigMVEz6tBh33jAcf",
												"microphoneOffPromptHeader": {
													"runs": [
														{
															"text": "Microphone off. Try again."
														}
													]
												}
											}
										},
										"popupType": "TOP_ALIGNED_DIALOG"
									}
								}
							]
						}
					},
					"icon": {
						"iconType": "MICROPHONE_ON"
					},
					"tooltip": "Search with your voice",
					"trackingParams": "CAIQ7a8FIhMI6IPRlebRigMVEz6tBh33jAcf",
					"accessibilityData": {
						"accessibilityData": {
							"label": "Search with your voice"
						}
					}
				}
			}
		}
	},
	"targetId": "search-page"
}
