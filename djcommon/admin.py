from django.contrib import admin
from django.utils.translation import gettext_lazy as _, pgettext_lazy as _p

from pycommon.logwrapper import getLogger
from .mixins import UserFKModelMixin, User1v1ModelMixin


log = getLogger(__package__ + "." + __file__)


class DjAdminBase(admin.ModelAdmin):
    _p("doc string", """Base class of Django model admin.""")

    empty_value_display = ""


class UserAdminMixin(admin.ModelAdmin):
    _p(
        "doc string",
        """Mixin admin class for User PK/AutoId model.
    1. user fk
    2. user privilege check functions.
    """,
    )

    def user_ref(self, obj) -> str:
        return "%s:%s" % (obj.user_id, obj.user.username) if obj.user else ("-", "-")

    user_ref.short_description = _p("field name", "user")
    user_ref.admin_order_field = "user__username"

    def user_is_admin(self, obj) -> bool:
        return obj.user and obj.user.is_superuser

    user_is_admin.short_description = _p("field name", "admin")
    user_is_admin.admin_order_field = "user__is_superuser"
    user_is_admin.boolean = True

    def user_is_staff(self, obj) -> bool:
        return obj.user and obj.user.is_staff

    user_is_staff.short_description = _p("field name", "staff")
    user_is_staff.admin_order_field = "user__is_staff"
    user_is_staff.boolean = True

    def user_is_active(self, obj) -> bool:
        return obj.user and obj.user.is_active

    user_is_active.short_description = _p("field name", "active")
    user_is_active.admin_order_field = "user__is_active"
    user_is_active.boolean = True

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        model = db_field.remote_field.model
        if db_field.name in ["user"]:
            kwargs["queryset"] = model.objects.filter(is_active=True)
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
