"""
Django settings for re-use ORM in other project.

Generated by 'django-admin startproject' using Django 3.1.7.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.1/ref/settings/
"""

from pathlib import Path
from pycommon.config import (
    DEBUG,
)
from djcommon.settings import DATABASES, LANGUAGE_CODE, TIME_ZONE

# avoid "is not accessed" warning
DEBUG, DATABASES, LANGUAGE_CODE, TIME_ZONE

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "b9-#w&ymc=dwr)55oc+x8aak5eun6zovftgm%!%pg-9&8d!9l5"

# Application definition

INSTALLED_APPS = [
    # 'django.contrib.admin',
    "django.contrib.auth",
    "django.contrib.contenttypes",
    # 'django.contrib.sessions',
    # 'django.contrib.messages',
    # 'django.contrib.staticfiles',
    # 'djorm',
]

# Internationalization
# https://docs.djangoproject.com/en/3.1/topics/i18n/

USE_I18N = True

USE_L10N = False

USE_TZ = True
