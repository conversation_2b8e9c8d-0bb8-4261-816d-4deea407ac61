import json
from abc import ABC
from collections.abc import Iterable
from datetime import datetime
from logging import getLogger
from typing import Self, TypeVar, Sequence, Type, Generic, Any, cast

from pydantic import BaseModel

from pycommon.utils.confutils import get_str_env
from pycommon.utils.datetimeutils import format_datetime
from .consts import DEFAULT_TIMEZONE
from .enums import PromptRole, OutputFormat

logger = getLogger(__name__)

PromptContentT = TypeVar("PromptContentT", bound=str)
PromptMessageT = TypeVar("PromptMessageT", bound=dict[str, str] | Sequence[str])


# ----- prompt entry -----
class Prompt(BaseModel, Generic[PromptContentT], ABC):
    role: PromptRole
    content: PromptContentT

    def __str__(self) -> str:
        # OpenAI style prompt format
        content = self.content if len(self.content) < 80 else f"{self.content[:80]}..."
        return f"<{self.__class__.__name__} {self.role}: {content}>"

    def to_dict(self) -> dict[str, str]:
        return {"role": self.role.value, "content": self.content}

    @staticmethod
    def from_dict(d: dict[str, str]) -> Any:
        assert "role" in d, 'missing "role" for prompt dict.'
        assert "content" in d, 'missing "content" for prompt dict.'
        assert d["role"] in PromptRole.values(), f'invalid role "{d["role"]}"'
        role = PromptRole(d["role"])
        cls: Type[Prompt] = ROLE_TO_PROMPT_CLASS[role.value]
        return cls(role=role, content=d["content"])

    @staticmethod
    def from_message(message: PromptMessageT) -> Any:
        """create prompt from a generic prompt message such as dict[str,str], list[str], tuple[str]...

        :param message:
        :return: Prompt or subclass
        """

        if isinstance(message, dict):
            return Prompt.from_dict(message)
        elif isinstance(message, Sequence):
            assert (
                len(message) >= 2
            ), f'Length of sequence PromptMessageT "{message}" is {len(message)}. Which is not larger or equal to 2.'
            cls = ROLE_TO_PROMPT_CLASS[message[0]]
            return cls(role=message[0], content=message[1])
        else:
            raise TypeError(f"Invalid message type {message.__class__.__name__}")

    def __bool__(self) -> bool:
        return bool(self.content)


class SystemPrompt(Prompt):
    role: PromptRole = PromptRole.SYSTEM


class UserPrompt(Prompt):
    role: PromptRole = PromptRole.USER


class AssistantPrompt(Prompt):
    role: PromptRole = PromptRole.ASSISTANT


ROLE_TO_PROMPT_CLASS: dict[str, Type[Prompt]] = {
    PromptRole.SYSTEM.value: SystemPrompt,
    PromptRole.USER.value: UserPrompt,
    PromptRole.ASSISTANT.value: AssistantPrompt,
}


# ----- chat prompts (composite of prompts) -----
class ChatPrompts(BaseModel):
    # system prompt
    instrument: SystemPrompt = SystemPrompt(content="")
    # user prompt
    inquery: UserPrompt = UserPrompt(content="")
    # few shots (user or assistant prompt presents alternately)
    few_shots: list[UserPrompt | AssistantPrompt] = []

    # as of date for the chat
    as_of_date: datetime = datetime.now(tz=DEFAULT_TIMEZONE)

    # output format (actually should be in lib level not prompt level)
    output_format: OutputFormat = "text"
    # template for output format. specify in detail if you want strict output especially `format` is 'json', 'yml'
    output_template: str = ""
    # schema for output format. typically it's JSON schema for "json" format; Yaml schema for "yaml".
    output_schema: str | dict | list = ""
    # output language
    output_lang: str = get_str_env("LLM_OUTPUT_LANG", "American English")

    @classmethod
    def new(cls, instrument: str, inquery: str, as_of_date: datetime | None = None) -> Self:
        obj = cls(
            instrument=SystemPrompt(content=instrument),
            inquery=UserPrompt(content=inquery),
        )
        obj.as_of_date = as_of_date if as_of_date else obj.as_of_date
        return obj

    def to_dict(self) -> dict:
        return self.model_dump()

    def to_str(self) -> str:
        return "".join(f"{k}: {v}\n" for d in self.to_list() for k, v in d.items())

    def __str__(self) -> str:
        return self.model_dump_json()

    @classmethod
    def from_list(cls, messages: Iterable[PromptMessageT]) -> Self:
        """fill all prompts by a message list.
        Last one must be user prompt which is inquery. Otherwise, all non-sys prompts are few-shots.
        """
        obj = cls()
        shots: list[UserPrompt | AssistantPrompt] = []
        for m in messages:
            p = Prompt.from_message(m)
            if p.role == PromptRole.SYSTEM:
                obj.set_system(p)
            elif p.role in [PromptRole.USER, PromptRole.ASSISTANT]:
                shots.append(p)
            else:
                logger.warning(f"Ignore invalid role {p.role}. ({m})")
        # last of few shots should move to user inquery
        if not obj.inquery and len(shots) > 0 and shots[-1].role == PromptRole.USER:
            obj.set_inquery(cast(UserPrompt, shots.pop(-1)))
        obj.few_shots = shots
        return obj

    def to_list(self) -> list[dict[str, str]]:
        messages = [self._get_system_dict()]
        # messages.extend([m.to_dict() for m in self.few_shots])
        messages.append(self.inquery.to_dict())
        return messages

    def set_system(self, value: PromptContentT | SystemPrompt) -> Self:
        """set system prompt"""
        if isinstance(value, SystemPrompt):
            self.instrument = value
        else:
            self.instrument = SystemPrompt(content=value)
        return self

    def set_inquery(self, value: PromptContentT | UserPrompt) -> Self:
        """set user prompt"""
        if isinstance(value, UserPrompt):
            self.inquery = value
        else:
            self.inquery = UserPrompt(content=value)
        return self

    def set_few_shots(
        self,
        messages: Iterable[PromptMessageT | UserPrompt | AssistantPrompt],
    ) -> Self:
        """set whole few shots list

        :param messages:
        :return:
        """
        self.few_shots = [
            (m if isinstance(m, (UserPrompt, AssistantPrompt)) else Prompt.from_message(m)) for m in messages
        ]
        return self

    def append_few_shot(self, message: PromptMessageT | UserPrompt | AssistantPrompt) -> Self:
        """append few shot.

        :param message:
        :return:
        """
        if isinstance(message, (UserPrompt, AssistantPrompt)):
            self.few_shots.append(message)
        else:
            self.few_shots.append(Prompt.from_message(message))
        return self

    def _get_system_dict(self) -> dict[str, str]:
        d = self.instrument.to_dict()
        sb = []

        if self.few_shots:
            sb.append("\n")
            sb.append("# Examples")
            sb.append(self._get_few_shots_str_for_sys_prompt())

        if self.as_of_date:
            sb.append("\n")
            sb.append(f"Current system time: {format_datetime(self.as_of_date)}\n")

        if self.output_schema:
            if isinstance(self.output_schema, str):
                schema = self.output_schema
            else:
                schema = json.dumps(self.output_schema, indent=2, ensure_ascii=False)
            sb.append("\n")
            sb.append(f"# Output in language `{self.output_lang}` by following the schema:\n")
            sb.append(f"{schema}")

        if self.output_template:
            sb.append("\n")
            sb.append(f"# Output in language `{self.output_lang}` by using the template format:\n")
            sb.append("\n")
            # sb.append(f"<output_format>")
            sb.append(f"{self.output_template}")
            # sb.append(f"</output_format>")

        sb.append("\n")
        if self.output_format == "text":
            sb.append("Remember ONLY output the answer. Do NOT add any other additional words.\n")
        else:
            sb.append(f"Respond only as `{self.output_format.upper()}` format. Do NOT add introduction or summary.\n")

        d["content"] = self.instrument.content + "".join(sb)
        return d

    def _get_few_shots_str_for_sys_prompt(self) -> str:
        sb = []
        for fs in self.few_shots:
            if fs.role == PromptRole.USER:
                sb.append(f"- input: {fs.content}")
            else:
                sb.append(f"  output: {fs.content}")
        return "\n".join(sb)
