import unittest

import os

from pycommon.config import EnvKey


class MyEnvKeys1(EnvKey):
    K1 = "K1", "K1 desc"
    K2 = "K2", "K2 desc"


class MyEnvKeys2(MyEnvKeys1):
    K2 = "K2_NEW", "K2 desc NEW"
    K3 = "K3", "K3 desc"


class TestConfigEnvKeyClass(unittest.TestCase):

    def setUp(self) -> None:
        os.environ[MyEnvKeys1.K1] = "a"
        os.environ[MyEnvKeys1.K2] = "b"
        os.environ[MyEnvKeys2.K2] = "b1"
        os.environ[MyEnvKeys2.K3] = "c"
        return super().setUp()

    def tearDown(self) -> None:
        del os.environ[MyEnvKeys1.K1]
        del os.environ[MyEnvKeys1.K2]
        del os.environ[MyEnvKeys2.K2]
        del os.environ[MyEnvKeys2.K3]
        return super().tearDown()

    def test_key_set(self) -> None:
        keys: set = EnvKey.key_set()
        keys1: set = set(["K1", "K2"])
        keys2: set = set(["K1", "K2_NEW", "K3"])

        self.assertSetEqual(keys1.union(keys), MyEnvKeys1.key_set())
        s2 = MyEnvKeys2.key_set()
        self.assertSetEqual(keys2.union(keys), s2)
        self.assertNotIn("K2", s2)

    def test_key_desc(self) -> None:
        d1 = MyEnvKeys1.key_desc_dict()
        self.assertDictContainsSubset({"K1": "K1 desc", "K2": "K2 desc"}, d1)
        d2 = MyEnvKeys2.key_desc_dict()
        self.assertDictContainsSubset(
            {"K1": "K1 desc", "K2_NEW": "K2 desc NEW", "K3": "K3 desc"}, d2
        )
        self.assertNotIn("K2", d2)

    def test_key_value(self) -> None:
        d1 = MyEnvKeys1.key_value_dict()
        self.assertDictContainsSubset({"K1": "a", "K2": "b"}, d1)
        d2 = MyEnvKeys2.key_value_dict()
        self.assertDictContainsSubset({"K1": "a", "K2_NEW": "b1", "K3": "c"}, d2)
        self.assertNotIn("K2", d2)
