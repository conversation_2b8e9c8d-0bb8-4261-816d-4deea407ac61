"""agent to write a complete novel"""

from typing import Any, AsyncGenerator

from novelai.engine.agents.base import NovelWritingConfig, NovelWritingAgent
from pyagent.base import Agent<PERSON><PERSON><PERSON>
from pycommon.logwrapper import get_log_wrapper

logger = get_log_wrapper(__name__)


class NovelAgentConfig(NovelWritingConfig):
    """config for NovelAgent"""

    pass


class NovelAgent(NovelWritingAgent):
    """撰写一本小说的智能体。An agent to write a complete novel automatically"""

    config: NovelAgentConfig

    async def _async_run(self, **kwargs: Any) -> AgentResult:
        raise NotImplementedError

    async def _async_iter(self, **kwargs: Any) -> AsyncGenerator:
        raise NotImplementedError
